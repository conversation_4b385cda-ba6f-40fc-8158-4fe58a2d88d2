const ConstantUtil = require('../enum/ConstantUtil');
const TaxCalculationMethodEnum = require('../enum/TaxCalculationMethodEnum');
const CalculateBaseType = require('../enum/CalculateBaseType');

class CostUtils {


  getRJPriceSCJ(item, taxCalculationMethod, csfyCalculateBaseCode) {
    if (csfyCalculateBaseCode === CalculateBaseType.RGFSCJ_JXFSCJ) {
      return item.marketPrice;
    }
    return item.dePrice;
  }
}

module.exports = {
  CostUtils: new CostUtils()
};
