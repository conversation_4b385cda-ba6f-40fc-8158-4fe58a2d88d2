'use strict';

const {Service} = require('../../../core');
const Log = require('../../../core/log');
const {BaseRuleFileDetails} = require("../model/BaseRuleFileDetails");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {In} = require("typeorm");

/**
 * 规则文件明细表
 * @class
 */
class BaseRuleFileDetailsService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseRuleFileDetailsDao = this.app.appDataSource.manager.getRepository(BaseRuleFileDetails);

    /**
     * 查规则文件
     * @param ids 规则文件ids
     * @return {Promise<BaseRuleFileDetails[]|Error>}
     */
    async listByIds(ids) {
        if (ObjectUtils.isEmpty(ids)) {
            Log.error("查规则文件入参ids为空");
            throw new Error("查规则文件入参ids为空");
        }
        return await this.baseRuleFileDetailsDao.findBy({sequenceNbr: In(ids)});
    }
}

BaseRuleFileDetailsService.toString = () => '[class BaseRuleFileDetailsService]';
module.exports = BaseRuleFileDetailsService;
