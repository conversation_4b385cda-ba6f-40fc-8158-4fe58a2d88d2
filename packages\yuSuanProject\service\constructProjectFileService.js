'use strict';

const {Service, Log} = require('../../../core');
const {ConstructProject} = require("../model/ConstructProject");
const {SingleProject} = require("../model/SingleProject");
const {UnitProject} = require("../model/UnitProject");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {format, addDays, differenceInDays} = require('date-fns');
const fs = require("fs");
const {ProjectOverview} = require("../model/ProjectOverview");
const gcjbxx = require("../jsonData/gcjbxx.json");
const dwjbxx = require("../jsonData/dwjbxx.json");
const gctz = require("../jsonData/gctz.json");
const {el} = require("date-fns/locale");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {OrganizationInstructions} = require("../model/OrganizationInstructions");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {FileLevelTreeNode} = require("../model/FileLevelTreeNode");
const {ItemBillProject} = require("../model/ItemBillProject");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const moment = require("moment/moment");
const {DateUtils} = require("../utils/DateUtils");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const path = require("path");
const {Snowflake} = require("../utils/Snowflake");
const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const {In} = require("typeorm");
const PolicyDocumentTypeEnum = require("../enum/PolicyDocumentTypeEnum");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const {HttpUtils} = require("../utils/HttpUtils");
const BsRemoteUrl = require("../enum/BsRemoteUrl");
const os = require('os');



/**
 * 示例服务
 * @class
 */
class ConstructProjectFileService extends Service {

    constructor(ctx) {
        super(ctx);
    }




    /**
     * 根据路径查询项目数据
     * @param path
     * @return {Promise<void>}
     */
    async getOneProDataByPath(path) {
        let userHistoryFileListData = PricingFileFindUtils.userHistoryFileListData();
        let data = userHistoryFileListData.filter(k =>k.path === path);
        return data;
    }


}

ConstructProjectFileService.toString = () => '[class ConstructProjectFileService]';
module.exports = ConstructProjectFileService;
