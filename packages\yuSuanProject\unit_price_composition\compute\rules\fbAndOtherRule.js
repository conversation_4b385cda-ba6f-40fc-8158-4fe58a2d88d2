/*
* 分部及父节点填充规则
* */

const baseFn = {
    //清单综合合价
    "QD_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "total"
        };
    }, //人工费合价
    "QD_RGF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "totalRfee"
        };
    }, //材料费合价
    "QD_CLF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "totalCfee"
        };
    }, //机械费合价
    "QD_JXF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "totalJfee"
        };
    }, //主材费合价
    "QD_ZCF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "totalZcfee"
        };
    }, //设备费合价
    "QD_SBF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "sbfTotal"
        };
    }, //暂估合价
    "QD_ZGF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "zgfTotal"
        };
    }, //管理费合价
    "QD_GLF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "totalManagerFee"
        };
    }, //利润合价
    "QD_LR_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "totalProfitFee"
        };
    }, //规费合价
    "QD_GF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "gfTotal"
        };
    }, //直接费合价
    "QD_ZJF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "zjfTotal"
        };
    }, //生产工具使用费合价
    "QD_SCGJSYF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "scgjsyfTotal"
        };
    }, //繁华地段管理增加费合价
    "QD_FHDDGLZJF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "fhddglzjfTotal"
        };
    }, //冬季防寒费合价
    "QD_GJFHF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "gjfhfTotal"
        };
    }, //山地管护增加费合价
    "QD_SDGHZJF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "sdghzjfTotal"
        };
    }, //绿色施工安全防护措施费合价
    "QD_LSSGAQFHCSF_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "lssgaqfhcsfTotal"
        };
    }, //进项税额合价
    "QD_JXSE_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "jxseTotal"
        };
    }, //销项税额合价
    "QD_XXSE_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "xxseTotal"
        };
    }, //增值税应纳税额合价
    "QD_ZZSYNSE_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "zzsynseTotal"
        };
    }, //附加税费合价
    "QD_FJSE_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "fjseTotal"
        };
    }, //税前工程造价合价
    "QD_SQGCZJ_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "sqgczjTotal"
        };
    }, //风险费用合价
    "QD_FXFY_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "fxfyTotal"
        };
    }, //税金合价
    "QD_SJ_TOTAL": () => {
        return {
            "type": "QD", "kind": "", "cloumn": "sjTotal"
        };
    }

}
const rules = {
    "sbfTotal": {
        "name": "设备费合价", "mathFormula": `_.sum(QD_SBF_TOTAL)`
    }, "zgfTotal": {
        "name": "暂估合价", "mathFormula": `_.sum(QD_ZGF_TOTAL)`
    }, //直接费合价
    "zjfTotal": {
        "name": "直接费合价", "mathFormula": `_.sum(QD_ZJF_TOTAL)`
    }, //人工费合价
    "totalRfee": {
        "name": "人工费合价", "mathFormula": `_.sum(QD_RGF_TOTAL)`
    }, //材料费合价
    "totalCfee": {
        "name": "材料费合价", "mathFormula": `_.sum(QD_CLF_TOTAL)`
    }, //机械费合价
    "totalJfee": {
        "name": "机械费合价", "mathFormula": `_.sum(QD_JXF_TOTAL)`
    }, //利润费合价  UPC_LR_TOTAL
    "totalProfitFee": {
        "name": "利润费合价", "mathFormula": `_.sum(QD_LR_TOTAL)`
    }, //管理费合价  UPC_GLF_TOTAL
    "totalManagerFee": {
        "name": "管理费合价", "mathFormula": `_.sum(QD_GLF_TOTAL)`
    }, //主材费合价 UPC_ZCF_TOTAL
    "totalZcfee": {
        "name": "主材费合价", "mathFormula": `_.sum(QD_ZCF_TOTAL)`
    }, "total": {
        "name": "综合合价", "mathFormula": `_.sum(QD_TOTAL)`
    }, "gfTotal": {
        "name": "规费合价", "mathFormula": `_.sum(QD_GF_TOTAL)`
    }, "scgjsyfTotal": {
        "name": "生产工具使用费合价", "mathFormula": `_.sum(QD_SCGJSYF_TOTAL)`
    },

    "fhddglzjfTotal": {
        "name": "繁华地段管理增加费合价", "mathFormula": `_.sum(QD_FHDDGLZJF_TOTAL)`
    },

    "gjfhfTotal": {
        "name": "冬季防寒费合价", "mathFormula": `_.sum(QD_GJFHF_TOTAL)`
    },

    "sdghzjfTotal": {
        "name": "山地管护增加费合价", "mathFormula": `_.sum(QD_SDGHZJF_TOTAL)`
    },

    "lssgaqfhcsfTotal": {
        "name": "绿色施工安全防护措施费合价", "mathFormula": `_.sum(QD_LSSGAQFHCSF_TOTAL)`
    },

    "jxseTotal": {
        "name": "进项税额合价", "mathFormula": `_.sum(QD_JXSE_TOTAL)`
    }, "xxseTotal": {
        "name": "销项税额合价", "mathFormula": `_.sum(QD_XXSE_TOTAL)`
    }, "zzsynseTotal": {
        "name": "增值税应纳税额合价", "mathFormula": `_.sum(QD_ZZSYNSE_TOTAL)`
    }, "fjseTotal": {
        "name": "附加税费合价", "mathFormula": `_.sum(QD_FJSE_TOTAL)`
    }, "sqgczjTotal": {
        "name": "税前工程造价合价", "mathFormula": `_.sum(QD_SQGCZJ_TOTAL)`
    }, "fxfyTotal": {
        "name": "风险费用合价", "mathFormula": `_.sum(QD_FXFY_TOTAL)`
    }, "sjTotal": {
        "name": "税金合价", "mathFormula": `_.sum(QD_SJ_TOTAL)`
    }
}

module.exports = {fbFillBaseRule: baseFn, fbFillRules: rules}

