'use strict';

const {Service, Log} = require('../../../core');
const {BaseArea} = require("../model/BaseArea");
const {ConstructProject} = require("../model/ConstructProject");
const {BaseListDeStandard} = require("../model/BaseListDeStandard");

/**
 * 示例服务
 * @class
 */
class BaseAreaService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取所有省的下拉框
     */
    async areaDropdownList(arg) {
        let result = await this.app.appDataSource.getRepository(BaseArea).find();
        let buildTree1 = this.buildTree(result);
        return buildTree1;
    }

     buildTree(nodes, pid = "0") {
        const result = [];
        for (const node of nodes) {
            if (node.pid == pid) {
                const children = this.buildTree(nodes, node.sequenceNbr);
                if (children.length > 0) {
                    node.children = children;
                }
                result.push(node);
            }
        }
        result.sort((a, b) => a.sort - b.sort);
        return result;
    }

}

BaseAreaService.toString = () => '[class BaseAreaService]';
module.exports = BaseAreaService;
