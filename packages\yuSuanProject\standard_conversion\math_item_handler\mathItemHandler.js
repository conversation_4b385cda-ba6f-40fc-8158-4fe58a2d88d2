const {BaseRcj2022} = require("../../model/BaseRcj2022");
const {BaseRcj} = require("../../model/BaseRcj");
const InsertRcjStrategy = require("../../rcj_handle/insert/insertRcjStrategy");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const _ = require("lodash");
const ConstantUtil = require("../../enum/ConstantUtil");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");
const { NumberUtil } = require("../../../../electron/utils/NumberUtil");

const {UnitRcjCacheUtil} = require("../../rcj_handle/cache/UnitRcjCacheUtil");
const { Snowflake } = require('../../utils/Snowflake');

class MathItemHandler {
    static MATH_RANGE_REGEX = /RANG\([^)]*\)/g;
    constructor(ruleCtx, math) {
        this.ctx = ruleCtx.ctx;
        this.app = ruleCtx.app;
        this.conversionService = ruleCtx.ctx.conversionService;
        this.effectDeRCJ = ruleCtx.effectDeRCJ;
        this.rule = ruleCtx.rule;
        this.notStandardActiveRcjCodes = ruleCtx.notStandardActiveRcjCodes;
        this.oriMath = math;
        // this.formatMath = this.conversionService.mathFormat(math, this.rule);
        // this.mathItem = this._initMathItem();

        let {formatMath, mathRange} = this._disassemblyFormula(this.oriMath);
        this.mathRange = mathRange;
        this.formatMath = this.conversionService.mathFormat(formatMath, this.rule);
        this.mathItem = this._initMathItem();
        this.deRcjs = ruleCtx.deRcjs;
    }

    /**
     * 准备数据：根据不同规则准备定额、人材机、规则的取消处理等
     */
    async prepare() {
        this.analysisMath();
        await this.activeRCJ();
    }

    /**
     * 逐条执行换算规则
     */
    async execute() {
        await this.prepare();
        await this.dealResQty();
        this.after();
    }

    async dealResQty(){
        await this.computeResQty();
        // 处理特殊人材机消耗量最高、最低限制
        if(this.mathRange){
            let deRcjs = this.deRcjs;
            for(let rcj of deRcjs){
                let range = this.mathRange[rcj.materialCode];
                if(range){
                    let resQty = rcj.resQty;
                    resQty = ObjectUtils.isNotEmpty(range.min) && resQty < range.min ? range.min : resQty;
                    resQty = ObjectUtils.isNotEmpty(range.max) && resQty > range.max ? range.max : resQty;
                    rcj.resQty = resQty;
                }
            }
        }
    }

    after() {

    }

    _initMathItem() {
        return {
            /**
             * type取值 代表那种规则：
             *   1. 修改消耗量;
             *   2. 替换指定材料修改消耗量；
             *   3. 替换指定材料消耗量不变；
             *   4. 新增指定材料，修改消耗量；
             *   5. 修改指定材料消耗量
             * */
            type: null,
            math: this.formatMath,
            operator: "",
            parseMath: "",
            RJCSymbol: "",  //取值： R,C,J,ALL
            activeRCJKind: [],
            activeRCJs:[],
            fromRCJCode: null,
            fromRCJLibraryCode: null,
            toRCJCode: null,
            toRCJLibraryCode: null,
            addRCJCode: null,
            addRCJLibraryCode: null,
            delRCJCode: null
        };
    }

    analysisMath() {
        throw new Error("MathItemHandler: 公式解析需要子类实现")
    }

    async activeRCJ() {
        throw new Error("MathItemHandler: 影响的人材机子类实现")
    }

    findActiveRCJByCode(materialCode) {
        let activeRCjS = this.effectDeRCJ.filter((rcj) => materialCode === rcj.materialCode);
        if(ObjectUtils.isEmpty(activeRCjS)){
            let notStandardCode = materialCode + "#";
            activeRCjS = this.effectDeRCJ.filter((rcj) => rcj.materialCode.startsWith(notStandardCode));
        }
        return activeRCjS;
    }

    async computeResQty() {
        this.mathItem.activeRCJs.forEach((rcj) => {
            // 判断人材机临时删除状态，获取处理前消耗量
            let resQtyBefore = rcj.tempDeleteFlag ? rcj.tempDeleteBackupResQty : rcj.resQty;
            let parseMathAfterCalculation = this.conversionService.mathAfterCalculation(this.mathItem.parseMath);
            let finalMath = ObjectUtils.isEmpty(this.mathItem.operator)
                ? parseMathAfterCalculation
                : `${resQtyBefore}${parseMathAfterCalculation}`

            let resQtyAfter = NumberUtil.numberScale(eval(finalMath), 6);
            if (isNaN(resQtyAfter)) {
                throw new Error("计算失败！");
            }

            if(rcj.tempDeleteFlag){
                rcj.tempDeleteBackupResQty = resQtyAfter;
            }else{
                rcj.resQty = resQtyAfter;
            }

            if (ObjectUtils.isEmpty(rcj.changeResQtyRuleIds)) {
                rcj.changeResQtyRuleIds = [this.rule.sequenceNbr];
            } else {
                rcj.changeResQtyRuleIds.push(this.rule.sequenceNbr);
            }
        })
    }


    async addNewRCJ(libraryCode, materialCode, resQty = 0) {
        const {constructId, singleId, unitId, unitProject, de} = this.ctx;
        // const baseRcj = await this.app.appDataSource
        //     .getRepository(this.ctx.isUnit2022 ? BaseRcj2022 : BaseRcj)
        //     .findOneBy({
        //         libraryCode: libraryCode,
        //         materialCode: materialCode,
        //     });
        const baseRcj = await this.conversionService.getRCJNew(unitProject, libraryCode, materialCode, this.ctx.de);
        if (!baseRcj) {
            throw new Error(`找不到替换的人材机(${libraryCode}  ${materialCode})`);
        }
        // 为了适配rcjReplaceStrategy对替换目标人材机的处理
        if(ObjectUtils.isNotEmpty(baseRcj.standardId)){
            baseRcj.sequenceNbr = baseRcj.standardId;
        }
        // 无人材机 或 无该规则新增的人材机 则 新增该人材机
        const insertRcjStrategy = new InsertRcjStrategy({
            constructId,
            singleId,
            unitId,
            projectObj: PricingFileFindUtils.getProjectObjById(constructId),
        });

        let newRcj = await insertRcjStrategy.execute({
            de: de,
            pointLine: null,
            rcj: {...baseRcj, resQty: resQty, isSupplement: 0},
        });

        if(baseRcj.isSupplement == 1 && ObjectUtils.isNotEmpty(baseRcj.childrenRcjCodeList)){
            let childrenOneRcj = UnitRcjCacheUtil.queryChildrenRcj(unitProject,baseRcj);
            if(ObjectUtils.isNotEmpty(childrenOneRcj)) {
                for (let item of childrenOneRcj) {
                    let newRcj1 = ObjectUtils.cloneDeep(item);
                    newRcj1.sequenceNbr = Snowflake.nextId();
                    newRcj1.rcjId = newRcj.sequenceNbr;
                    newRcj1.deId = de.sequenceNbr;

                    // 处理临时删除标记
                    if (newRcj.tempDeleteFlag) {
                        newRcj1.tempDeleteFlag = true;
                        newRcj1.tempDeleteBackupResQty = item.resQty;
                        newRcj1.resQty = null;
                    }
                    unitProject.rcjDetailList.push(newRcj1);
                }
            }
        }

        const {sequenceNbr: id} = newRcj;

        newRcj = unitProject.constructProjectRcjs.find((v) => v.sequenceNbr == id);
        newRcj.addFromConversionRuleId = this.rule.sequenceNbr;

        this.deRcjs.push(newRcj);

        return newRcj;
    }

    async editRcj(fromRcj, toRcj) {
        let resultRcj = await this.conversionService.editRcj(fromRcj, toRcj, this.ctx)
        if(fromRcj.addFromConversionRuleId){
            resultRcj.addFromConversionRuleId = fromRcj.addFromConversionRuleId;
        }
        resultRcj.editFromConversion = {
            ruleId : this.rule.sequenceNbr,
            fromRCJCode: fromRcj.materialCode,
            fromRCJLibraryCode: fromRcj.libraryCode
        };
        return resultRcj;
    }

    isOtherRCj(rcj){
        return ConstantUtil.SPECIAL_RCJ.includes(rcj.materialCode) && rcj.unit == ConstantUtil.BAIFENHAO;
    }


    mathOperator(mathStr){
        let firstCharacter = mathStr.charAt(0);
        if("+-*/".includes(firstCharacter)){
            return firstCharacter;
        }
        return "";
    }

    _disassemblyFormula(oriMath) {
        if(ObjectUtils.isEmpty(oriMath)){
            return {};
        }
        let matches = oriMath.match(MathItemHandler.MATH_RANGE_REGEX);
        let mathRange = null
        if(ObjectUtils.isNotEmpty(matches)){
            mathRange = {}
            for(let r of matches){
                r = r.replace("RANG(", "").replace(")", "")
                let rs = r.split(";");
                for(let codeRs of rs){
                    let rTmp = codeRs.split(":");
                    let code = rTmp[0];
                    let minMax = rTmp[1].split("~");
                    let min = ObjectUtils.isNotEmpty(minMax[0]) ? Number(minMax[0]) : null
                    let max = ObjectUtils.isNotEmpty(minMax[1]) ? Number(minMax[1]) : null
                    mathRange[code] = {min, max}
                }
            }
        }
        return {
            formatMath: oriMath.replace(MathItemHandler.MATH_RANGE_REGEX, "").replace(/^\s+|\s+$/g, ""),
            mathRange: mathRange
        }
    }
}

module.exports = MathItemHandler;
