'use strict';

const ConstantUtil = require("../enum/ConstantUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const { Service,Log } = require('../../../core');
const {BaseArea} = require("../model/BaseArea");
const {BaseListDeStandard} = require("../model/BaseListDeStandard");
const {getRepository  } =require('typeorm');
const {BaseUnitProjectType, BaseUnitProjectType2022} = require("../model/BaseUnitProjectType");


/**
 * 示例服务
 * @class
 */
class BaseUnitProjectTypeService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 工程专业下拉列表
     * @param arg
     * @return {Promise<void>}
     */
    async engineeringSpecialtiesDropdownList(arg){

        let deStandard = arg?.deStandard || ConstantUtil.DE_STANDARD_12;
        let result = [];

        if(deStandard == ConstantUtil.DE_STANDARD_22) {
            result = await this.app.appDataSource.getRepository(BaseUnitProjectType2022).find();
        }else {
            result = await this.app.appDataSource.getRepository(BaseUnitProjectType).find();
        }

        if (!ObjectUtils.isEmpty(result)){
            result.sort((a, b) => a.sortNo - b.sortNo);
        }

        return result;

    }


}

BaseUnitProjectTypeService.toString = () => '[class BaseUnitProjectTypeService]';
module.exports = BaseUnitProjectTypeService;
