'use strict';



const {ConstructProject} = require("../model/ConstructProject");
const {Service} = require("../../../core");
const {Snowflake} = require("../utils/Snowflake");
const fs = require('fs')
const xml2js = require('xml2js');
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const OtherProjectDayWorkRcjConstant = require("../enum/OtherProjectDayWorkRcjConstant");
const PolicyDocumentTypeEnum = require("../enum/PolicyDocumentTypeEnum");
const {arrayToTree} = require("../main_editor/tree");
const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../utils/ResponseData");
const {MeasureProjectTable} = require("../model/MeasureProjectTable");
const {OtherProjectZygcZgj} = require("../model/OtherProjectZygcZgj");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {OtherProjectServiceCost} = require("../model/OtherProjectServiceCost");
const {OtherProjectZgj} = require("../model/OtherProjectZgj");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {OtherProject} = require("../model/OtherProject");
const {UnitProject} = require("../model/UnitProject");
const {SingleProject} = require("../model/SingleProject");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
class AnalyzingXMLServiceYSF extends Service{
    constructor(ctx) {
        super(ctx);

        this.qbExtraTableArray = new Array();

        this.qdMap = new Map();
        this.dispNo = 1;

    }


    async  analysis(constructProject,data){
        let ZhaoBiaoXx = data.JingJiBiao.ZhaoBiaoXx;

        let JingJiBiao = data.JingJiBiao.$;
        let Dxgcxx = data.JingJiBiao.Dxgcxx;
        //单项工程
        let singleProjects = new Array();

        if(!ObjectUtils.isEmpty(ZhaoBiaoXx)){
            if(ObjectUtils.isEmpty(constructProject.biddingType)){
                constructProject.biddingType =ConstructBiddingTypeConstant.zbProject
            }
        }else {
            constructProject.biddingType =ConstructBiddingTypeConstant.tbProject
            return ResponseData.fail('文件类型有误，请重新选择');
        }

        let jsType ; //1 一般 0 简易

        if(JingJiBiao.Jsfs === '一般计税'){
            jsType = 1;
        }else {
            jsType = 0;
        }
        //计税方式
        await this.service.yuSuanProject.projectTaxCalculationService.importXMLInitProjectTaxCalculation(constructProject,jsType);

        if(ObjectUtils.isEmpty(constructProject.sequenceNbr)){
            constructProject.sequenceNbr = Snowflake.nextId();
        }
        constructProject.constructCode = JingJiBiao.Xmbh;

        constructProject.gfId = '14';
        constructProject.awfId = '44';

        constructProject.rgfId=await this.service.yuSuanProject.analyzingXMLService.getDefaultRgf();

        let map = this.convertConstructProjectJBXX(constructProject, ZhaoBiaoXx);
        // 编制说明 ---项目层级
        this.service.yuSuanProject.constructProjectService.initProjectOrUnitBZSM(1, constructProject);
        //解析单项工程
        await this.convertSingleProject(Dxgcxx,constructProject);
        //放入内存
        PricingFileWriteUtils.writeToMemory(constructProject);
        return constructProject.sequenceNbr;
    }



    convertConstructProjectJBXX(constructProject, ZhaoBiaoXx) {
        //工程基本信息
        this.service.yuSuanProject.constructProjectService.initProjectOrUnitData(constructProject, 1);
        let constructProjectJBXX = constructProject.constructProjectJBXX;

        for (let i = 0; i < constructProjectJBXX.length; i++) {
            switch (constructProjectJBXX[i].name) {
                case '工程名称':
                    constructProjectJBXX[i].remark = constructProject.constructName;
                    break;
                case '招标人(发包人)':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.Zbr;
                    break;
                case '招标人(发包人)法人或其授权人':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.ZbrDb;
                    break;
                case '工程造价咨询人':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.Zxr;
                    break;
                case '工程造价咨询人法人或其授权人':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.ZxrDb;
                    break;
                case '编制人':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.Bzr;
                    break;
                case '编制时间':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.BzRq;
                    break;
                case '核对人(复核人)':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.Fhr;
                    break;
                case '核对(复核)时间':
                    constructProjectJBXX[i].remark = ZhaoBiaoXx.FgRq;
                    break;
                default:
                    break;
            }

        }
        constructProject.constructProjectJBXX = constructProjectJBXX;
    }

    /**
     * 解析单项工程
     * @param 单项工程
     * @param constructProject
     */
    async convertSingleProject(Dxgcxx, constructProject) {
        if(!ObjectUtils.isObject(Dxgcxx)){
            let singleProjects = new Array();
            for (let i = 0; i < Dxgcxx.length; i++) {
                let singleProject = new SingleProject();
                let model = Dxgcxx[i];
                let $ = model.$;
                singleProject.sequenceNbr = Snowflake.nextId();
                singleProject.constructId = constructProject.sequenceNbr;
                singleProject.projectCode = $.Dxgcbh;
                singleProject.projectName = $.Dxgcmc;
                singleProject.Je = $.Je;
                singleProject.Gf = $.Gf;
                singleProject.Aqwmf = $.Aqwmf;
                singleProject.SbfSj = $.SbfSj;
                //判断单项下是否还有单项
                if(model.Dwgcxx === undefined){
                    //还有单项, 递归去解析
                    await this.recursionSingleProject(model.Dxgcxx, singleProject, constructProject);
                }else{
                    //解析单位工程
                    await this.convertUnitProject(model.Dwgcxx,singleProject,constructProject);
                }

                singleProjects.push(singleProject);
            }
            constructProject.singleProjects = singleProjects
        }
    }

    /**
     * 递归处理子单项
     */
    async recursionSingleProject(xmlSingleProjects, oldSingleProjects, constructProject) {
        if(!ObjectUtils.isEmpty(xmlSingleProjects)){
            let newSingleProjects = new Array();
            for (let i = 0; i < xmlSingleProjects.length; i++) {
                let singleProject = new SingleProject();
                let model = xmlSingleProjects[i];
                let $ = model.$;
                singleProject.sequenceNbr = Snowflake.nextId();
                singleProject.constructId = constructProject.sequenceNbr;
                singleProject.projectCode = $.Dxgcbh;
                singleProject.projectName = $.Dxgcmc;
                singleProject.Je = $.Je;
                singleProject.Gf = $.Gf;
                singleProject.Aqwmf = $.Aqwmf;
                singleProject.SbfSj = $.SbfSj;
                //判断单项下是否还有单项
                if(model.Dwgcxx === undefined){
                    await this.recursionSingleProject(model.Dxgcxx, singleProject, constructProject);
                }else{
                    //解析单位工程
                    await this.convertUnitProject(model.Dwgcxx, singleProject, constructProject);
                }
                newSingleProjects.push(singleProject);
            }
            oldSingleProjects.subSingleProjects = newSingleProjects;
        }


    }

    /**
     * 解析单位工程
     * @param Dwgcxx
     * @param singleProject
     */
    async convertUnitProject(Dwgcxx, singleProject, constructObj) {
        let rgfId = constructObj.rgfId;
        if(!ObjectUtils.isObject(Dwgcxx)){
            let unitProjects = new Array();
            for (let i = 0; i < Dwgcxx.length; i++) {
                let model = Dwgcxx[i].$;
                let unitProject = new UnitProject();
                unitProject.sequenceNbr = Snowflake.nextId();
                unitProject.upCode = model.Dwgcbh;
                unitProject.upName = model.Dwgcmc;
                unitProject.SbfSj = model.SbfSj;
                unitProject.Zylb = model.Zylb;
                unitProject.Aqwmf = model.Aqwmf;
                unitProject.spId = singleProject.sequenceNbr;
                unitProject.constructId = singleProject.constructId;
                unitProject.rgfId = rgfId;
                unitProject.projectTaxCalculation = constructObj.projectTaxCalculation;
                // 添加工程基本信息 ---单位层级
                this.service.yuSuanProject.constructProjectService.initProjectOrUnitData(unitProject, 3);
                // 编制说明 ---单位层级
                this.service.yuSuanProject.constructProjectService.initProjectOrUnitBZSM(3, unitProject);
                //单位工程费用汇总（包含单位工程部分数据）
                await this.convertUnitProjectSummary(Dwgcxx[i].Fywj,unitProject);
                //分部分项
                this.dispNo = 1;
                await this.convertItemBill(Dwgcxx[i].Qdxm,unitProject);
                this.dispNo = 1;
                //单价措施
                await this.convertMeasureTableDJ(Dwgcxx[i].Csxm,unitProject);
                //总价措施
                await this.convertMeasureTableZJ(Dwgcxx[i].Csxm,unitProject);

                this.qbExtraTableArray = new Array();
                //其他项目清单
                await this.convertOtherProjects(Dwgcxx[i].Qtxm[0].QtxmMx,unitProject);
                //暂列金额
                await this.convertProvisional(Dwgcxx[i].Zlje,unitProject);
                //暂估价
                await this.convertZgjSums(Dwgcxx[i].Clzg,Dwgcxx[i].Sbzg,Dwgcxx[i].Zygczg,unitProject);
                //总承包服务费
                await this.convertServiceCosts(Dwgcxx[i].Zcbfwf,unitProject);

                //其他项目 签证与索赔计价表 初始化
                let otherProjectQzSpJjbList = await this.service.yuSuanProject.otherProjectService.getInitOtherProjectQzSpJjb(unitProject);
                unitProject.otherProjectQzAndSuoPeis = otherProjectQzSpJjbList;
                //保存其他项目
                unitProject.otherProjects= ObjectUtils.isNotEmpty(this.qbExtraTableArray)?this.qbExtraTableArray:unitProject.otherProjects;
                //计日工
                await this.convertDayWorks(Dwgcxx[i].Jrg, unitProject);
                unitProject.constructProjectRcjs = [];
                unitProject.rcjDetailList = [];
                unitProjects.push(unitProject);
            }
            singleProject.unitProjects = unitProjects;
        }

    }

    /**
     * 费用汇总
     * @param 单位工程费汇总表
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertUnitProjectSummary(Fywj, unitProject) {
        if(!ObjectUtils.isEmpty(Fywj)){
            let FywjMx = Fywj[0].FywjMx;

            for (let i = 0; i < FywjMx.length; i++) {
                let model = FywjMx[i].$;
                switch(model.Xh){
                    //分部分项工程量清单计价合计
                    case "1": {
                        unitProject.fbfxhj  = model.Je;
                        unitProject.fbfxrgf = model.Rgf;
                        unitProject.fbfxclf = model.Clf;
                        unitProject.fbfxjxf = model.Jxf;
                        break;
                    }

                    //措施项目清单计价合计
                    case "2": {
                        unitProject.csxhj  = model.Je;
                        unitProject.csxrgf = model.Rgf;
                        unitProject.csxclf = model.Clf;
                        unitProject.csxjxf = model.Jxf;
                        break;
                    }

                    //单价措施项目工程量清单计价合计
                    case "2.1": {
                        unitProject.djcsxhj = model.Je;
                        unitProject.djcsxrgf= model.Rgf;
                        unitProject.djcsxclf= model.Clf;
                        unitProject.djcsxjxf= model.Jxf;
                        break;
                    }

                    //其他总价措施项目清单计价合计
                    case "2.2": {
                        unitProject.ZjCsxhj = model.Je;
                        unitProject.ZjCsxrgf= model.Rgf;
                        unitProject.ZjCsxclf= model.Clf;
                        unitProject.ZjCsxjxf= model.Jxf;
                        break;
                    }

                    //其他项目清单计价合计
                    case "3": {
                        unitProject.qtxmhj = model.Je;
                        unitProject.qtxmrgf= model.Rgf;
                        unitProject.qtxmclf= model.Clf;
                        unitProject.qtxmjxf= model.Jxf;
                        break;
                    }

                    //安全生产、文明施工费
                    case "4": {
                        unitProject.safeFee= model.Je;
                        break;
                    }

                    //税前工程造价
                    case "8": {
                        unitProject.sqgczj= model.Je;
                        break;
                    }

                    //进项税额
                    case "9": {
                        unitProject.jxse= model.Je;
                        break;
                    }

                    //销项税额
                    case "10": {
                        unitProject.xxse= model.Je;
                        break;
                    }

                    //增值税应纳税额
                    case "11": {
                        unitProject.zzsynse= model.Je;
                        break;
                    }

                    //附加税费
                    case "12": {
                        unitProject.fjse= model.Je;
                        break;
                    }

                    //税金
                    case "5": {
                        unitProject.sj= model.Je;
                        break;
                    }
                    //工程造价
                    case "6": {
                        unitProject.uptotal= model.Je;
                        break;
                    }
                }



            }
        }



    }

    /**
     * 暂列金额
     * @param 暂列金额
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertProvisional(Zlje, unitProject) {
        if(ObjectUtils.isEmpty(Zlje)){
            //调用插入暂列金额默认值
            unitProject.otherProjectProvisionals = await this.service.yuSuanProject.otherProjectProvisionalService.importInitProjectProvisional();
            return
        }
        if(!ObjectUtils.isEmpty(Zlje)){
            let model = Zlje[0].$;
            let qtJxTotal = 0;
            let qtCsTotal = 0;
            if(!ObjectUtils.isEmpty(Zlje[0].ZljeMx)){
                let ZljeMx = Zlje[0].ZljeMx;
                let otherProjectProvisionalArray = new Array();
                for (let i = 0; i < ZljeMx.length; i++) {
                    let $ = ZljeMx[i].$;
                    let otherProjectProvisional = new OtherProjectProvisional();
                    otherProjectProvisional.sequenceNbr = Snowflake.nextId();
                    otherProjectProvisional.name = $.Mc;
                    otherProjectProvisional.unit = $.Dw;
                    otherProjectProvisional.provisionalSum = ObjectUtils.isEmpty($.Zdje)?0:Number($.Zdje) ;
                    otherProjectProvisional.sortNo = i+1;
                    otherProjectProvisional.dispNo = $.Xh;
                    otherProjectProvisional.description = $.Bz;
                    otherProjectProvisional.constructId = unitProject.constructId;
                    otherProjectProvisional.spId = unitProject.spId;
                    otherProjectProvisional.unitId = unitProject.sequenceNbr;

                    otherProjectProvisional.amount = 1 ;
                    otherProjectProvisional.price = otherProjectProvisional.provisionalSum;//单价 没有单价所以直接默认赋值暂定金额
                    otherProjectProvisional.taxRemoval = 3 ; //除税系数(%)
                    // 进项合计 暂定金额*除税系数
                    otherProjectProvisional.jxTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectProvisional.provisionalSum,otherProjectProvisional.taxRemoval/100)) ;
                    otherProjectProvisional.csPrice = NumberUtil.subtract(otherProjectProvisional.provisionalSum,otherProjectProvisional.jxTotal);
                    otherProjectProvisional.csTotal = otherProjectProvisional.csPrice;

                    qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectProvisional.jxTotal);
                    qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectProvisional.csTotal);

                    otherProjectProvisionalArray.push(otherProjectProvisional);
                }
                unitProject.otherProjectProvisionals = otherProjectProvisionalArray;

            }

            this.qbExtraTableArray[0].jxTotal =qtJxTotal;
            this.qbExtraTableArray[0].csTotal =qtCsTotal;
        }



    }

    /**
     * 暂估价
     * @param 暂估价
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZgjSums(Clzg,Sbzg, Zygczg,unitProject) {


        let otherProjectZgjArray =new Array();
        let model ;

        let sortNo= 0;
        if(!ObjectUtils.isEmpty(Clzg)){
            model = Clzg[0].$;
            let otherProjectZgj = new OtherProjectZgj();
            let ClzgMx = Clzg[0].ClzgMx;
            if(!ObjectUtils.isEmpty(ClzgMx)){
                for (let i = 0; i < ClzgMx.length; i++) {
                    model = ClzgMx[i].$;
                    otherProjectZgj = new OtherProjectZgj();
                    otherProjectZgj.sequenceNbr = Snowflake.nextId();
                    otherProjectZgj.price   = model.Dj;
                    otherProjectZgj.name   = model.Mc;
                    otherProjectZgj.attr  =model.Ggxh;
                    otherProjectZgj.unit  =model.Dw;
                    // otherProjectZgj.taxRemoval  =model.除税系数;
                    // otherProjectZgj.jxTotal  =model.进项税额合计;
                    // otherProjectZgj.csPrice  =model.除税市场价;
                    // otherProjectZgj.csTotal  =model.除税合价;
                    // otherProjectZgj.parentId = id;
                    otherProjectZgj.dispNo   = ''+(i+1);
                    otherProjectZgj.sortNo   = sortNo++;
                    otherProjectZgj.description = model.Bz;
                    otherProjectZgj.constructId = unitProject.constructId;
                    otherProjectZgj.spId = unitProject.spId;
                    otherProjectZgj.unitId = unitProject.sequenceNbr;
                    otherProjectZgjArray.push(otherProjectZgj);
                }
            }
        }
        unitProject.otherProjectClZgjs = otherProjectZgjArray;
        otherProjectZgjArray = new Array();
        if(!ObjectUtils.isEmpty(Sbzg)){
            model = Sbzg[0].$;
            let otherProjectZgj = new OtherProjectZgj();

            let SbzgMx = Sbzg[0].SbzgMx;
            if(!ObjectUtils.isEmpty(SbzgMx)){
                for (let i = 0; i < SbzgMx.length; i++) {
                    model = SbzgMx[i].$;
                    otherProjectZgj = new OtherProjectZgj();
                    otherProjectZgj.sequenceNbr = Snowflake.nextId();
                    otherProjectZgj.price    = model.Dj;
                    otherProjectZgj.name    = model.Mc;
                    otherProjectZgj.attr   =model.Ggxh;
                    otherProjectZgj.unit   =model.Dw;
                    // otherProjectZgj.taxRemoval  =model.除税系数;
                    // otherProjectZgj.jxTotal  =model.进项税额合计;
                    // otherProjectZgj.csPrice  =model.除税市场价;
                    // otherProjectZgj.csTotal  =model.除税合价;
                    // otherProjectZgj.parentId = id;
                    otherProjectZgj.dispNo   = ''+(i+1);
                    otherProjectZgj.sortNo   = sortNo++;
                    otherProjectZgj.description = model.Bz;
                    otherProjectZgj.constructId = unitProject.constructId;
                    otherProjectZgj.spId = unitProject.spId;
                    otherProjectZgj.unitId = unitProject.sequenceNbr;
                    otherProjectZgjArray.push(otherProjectZgj);
                }
            }
        }
        unitProject.otherProjectSbZgjs = otherProjectZgjArray;
        otherProjectZgjArray = new Array();
        let qtJxTotal = 0;
        let qtCsTotal = 0;
        if(!ObjectUtils.isEmpty(Zygczg)){
            model = Zygczg[0].$;
            let otherProjectZgj = new OtherProjectZygcZgj();
            let ZygczgMx = Zygczg[0].ZygczgMx;
            if(!ObjectUtils.isEmpty(ZygczgMx)){
                for (let i = 0; i < ZygczgMx.length; i++) {
                    model = ZygczgMx[i].$;
                    otherProjectZgj = new OtherProjectZygcZgj();
                    otherProjectZgj.sequenceNbr = Snowflake.nextId();
                    otherProjectZgj.total   = model.Je;
                    otherProjectZgj.name   = model.Gcmc;
                    otherProjectZgj.content   = model.Gcnr;
                    otherProjectZgj.unit  =model.Dw;
                    // otherProjectZgj.parentId = id;
                    otherProjectZgj.dispNo   = ''+(i+1);
                    otherProjectZgj.sortNo   = sortNo++;
                    otherProjectZgj.description = model.Bz;
                    otherProjectZgj.constructId = unitProject.constructId;
                    otherProjectZgj.spId = unitProject.spId;
                    otherProjectZgj.unitId = unitProject.sequenceNbr;

                    otherProjectZgj.amount = 1 ;
                    otherProjectZgj.price = otherProjectZgj.total;//单价 没有单价所以直接默认赋值暂定金额
                    otherProjectZgj.taxRemoval = 3 ; //除税系数(%)
                    // 进项合计 暂定金额*除税系数
                    otherProjectZgj.jxTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectZgj.total,otherProjectZgj.taxRemoval/100)) ;
                    otherProjectZgj.csPrice = NumberUtil.subtract(otherProjectZgj.total,otherProjectZgj.jxTotal);
                    otherProjectZgj.csTotal = otherProjectZgj.csPrice;

                    qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectZgj.jxTotal);
                    qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectZgj.csTotal);

                    otherProjectZgjArray.push(otherProjectZgj);
                }
            }
            unitProject.otherProjectZygcZgjs = otherProjectZgjArray;
        }
        if(ObjectUtils.isEmpty(unitProject.otherProjectZygcZgjs)){
            //调用插入专业工程额默认值
            unitProject.otherProjectZygcZgjs = await this.service.yuSuanProject.otherProjectZgjService.importInitOtherProjectZygcZgj()
        }
        this.qbExtraTableArray[1].jxTotal =qtJxTotal;
        this.qbExtraTableArray[1].csTotal =qtCsTotal;
        this.qbExtraTableArray[4].jxTotal =qtJxTotal;
        this.qbExtraTableArray[4].csTotal =qtCsTotal;

    }


    /**
     * 总承包服务费
     * @param 总承包服务费
     * @param unitProject
     * @returns {Promise<void>}
     */
    async  convertServiceCosts(Zcbfwf, unitProject) {


        let model = Zcbfwf[0].$;

        unitProject.otherProjectServiceCosts = new Array();
        if(ObjectUtils.isEmpty(Zcbfwf)){
            //调用初始化总承包服务接口
            unitProject.otherProjectServiceCosts = await this.service.yuSuanProject.otherProjectService.getInitOtherProjectZcbfwfList()
            return;
        }
        let ZcbfwfBt = Zcbfwf[0].ZcbfwfBt;

        if(ObjectUtils.isEmpty(ZcbfwfBt)){
            return;
        }
        // 根据类型分组

        let zygcList = new Array();
        let clList   = new Array();
        let sbList   = new Array();

        for (let i = 0; i < ZcbfwfBt.length; i++) {

            if(ZcbfwfBt[i].$.Mc === '招标人另行发包专业工程'){
                if(!ObjectUtils.isEmpty(ZcbfwfBt[i].ZcbfwfMx)){
                    for (let j = 0; j < ZcbfwfBt[i].ZcbfwfMx.length; j++) {
                        zygcList.push( ZcbfwfBt[i].ZcbfwfMx[j].$);
                    }
                }
            }
            if(ZcbfwfBt[i].$.Mc === '招标人供应材料'){
                if(!ObjectUtils.isEmpty(ZcbfwfBt[i].ZcbfwfMx)){
                    for (let j = 0; j < ZcbfwfBt[i].ZcbfwfMx.length; j++) {
                        clList.push( ZcbfwfBt[i].ZcbfwfMx[j].$);
                    }
                }
            }
            if(ZcbfwfBt[i].$.Mc === '招标人供应设备'){
                if(!ObjectUtils.isEmpty(ZcbfwfBt[i].ZcbfwfMx)){
                    for (let j = 0; j < ZcbfwfBt[i].ZcbfwfMx.length; j++) {
                        sbList.push( ZcbfwfBt[i].ZcbfwfMx[j].$);
                    }
                }
            }
            
        }


        await this.setServiceCosts(unitProject, zygcList, '1', '招标人另行发包专业工程');
        await this.setServiceCosts(unitProject, clList, '2', '招标人供应材料');
        await this.setServiceCosts(unitProject, sbList, '3', '招标人供应设备');

        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        this.qbExtraTableArray[5].total = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.fwje)  ;
        }, 0).toFixed(2))

        this.service.yuSuanProject.otherProjectServiceCostService.updateBiaotiTotal(unitProject);
    }

    /**
     * 总承包服务费
     * @param unitProject
     * @param list
     * @param dispNo
     * @param name
     * @returns {Promise<void>}
     */
    async  setServiceCosts(unitProject, list, dispNo, name) {
        let serviceCostsArray = new Array();
        let parentsUuid = Snowflake.nextId();
        let serviceCostsParents = new OtherProjectServiceCost();
        serviceCostsParents.sequenceNbr = parentsUuid;
        serviceCostsParents.dispNo = dispNo;
        serviceCostsParents.fxName = name;
        serviceCostsParents.sortNo = 0;
        serviceCostsParents.constructId = unitProject.constructId;
        serviceCostsParents.spId = unitProject.spId;
        serviceCostsParents.unitId = unitProject.sequenceNbr;
        serviceCostsParents.dataType = 1;

        serviceCostsArray.push(serviceCostsParents);

        if (!ObjectUtils.isEmpty(list)) {
            for (let i = 0; i < list.length; i++) {
                let model = list[i];
                let uuid = Snowflake.nextId();
                let otherProjectServiceCost = new OtherProjectServiceCost();
                otherProjectServiceCost.sequenceNbr  = uuid;
                otherProjectServiceCost.xmje  = model.Xmjz;
                otherProjectServiceCost.dispNo  = dispNo+'.'+(i+1);
                otherProjectServiceCost.fxName  = model.Mc;
                otherProjectServiceCost.fwje  = model.Je;
                //otherProjectServiceCost.fwje = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectServiceCost.xmje,NumberUtil.divide(otherProjectServiceCost.rate,100)));
                otherProjectServiceCost.rate  = model.Fl ;
                otherProjectServiceCost.parentId  = parentsUuid;
                otherProjectServiceCost.constructId = unitProject.constructId;
                otherProjectServiceCost.spId = unitProject.spId;
                otherProjectServiceCost.unitId = unitProject.sequenceNbr;
                otherProjectServiceCost.sortNo  = i + 1;
                otherProjectServiceCost.amount  = 1;
                otherProjectServiceCost.dataType = 2;
                otherProjectServiceCost.serviceContent = model.serviceContent;


                serviceCostsArray.push(otherProjectServiceCost);
            }
        }else {
            let myNumber = 0;
            let formattedNumber = myNumber.toFixed(2);
            let otherProjectServiceCost = new OtherProjectServiceCost();
            otherProjectServiceCost.sequenceNbr = Snowflake.nextId();
            otherProjectServiceCost.dispNo=dispNo+'.1';
            otherProjectServiceCost.xmje = formattedNumber;
            otherProjectServiceCost.rate= formattedNumber;
            otherProjectServiceCost.fwje = formattedNumber;
            otherProjectServiceCost.dataType =2 ;
            otherProjectServiceCost.parentId = parentsUuid;
            serviceCostsArray.push(otherProjectServiceCost);
        }
        unitProject.otherProjectServiceCosts.push(...serviceCostsArray);

    }

    /**
     * 计日工
     * @param 计日工
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertDayWorks(Jrg, unitProject) {
        if (ObjectUtils.isEmpty(Jrg)) {
            unitProject.otherProjectDayWorks = await this.service.yuSuanProject.otherProjectService.getInitOtherProjectJrgList();
            return;
        }

        const JrgBt = Jrg[0].JrgBt;

        let rgJrg = new Array();
        let clJrg = new Array();
        let jxJrg = new Array();

        let rgJrgBt ;
        let clJrgBt ;
        let jxJrgBt ;

        for (let i = 0; i < JrgBt.length; i++) {
            if(JrgBt[i].$.Mc=== '人工'){
                rgJrgBt = JrgBt[i].$;
                if(!ObjectUtils.isEmpty(JrgBt[i].JrgMx)){
                    for (let j = 0; j < JrgBt[i].JrgMx.length; j++) {
                        rgJrg.push( JrgBt[i].JrgMx[j].$);
                    }
                }
            }
            if(JrgBt[i].$.Mc=== '材料'){
                clJrgBt = JrgBt[i].$;
                if(!ObjectUtils.isEmpty(JrgBt[i].JrgMx)){
                    for (let j = 0; j < JrgBt[i].JrgMx.length; j++) {
                        clJrg.push( JrgBt[i].JrgMx[j].$);
                    }
                }
            }
            if(JrgBt[i].$.Mc=== '机械'){
                jxJrgBt = JrgBt[i].$;
                if(!ObjectUtils.isEmpty(JrgBt[i].JrgMx)){
                    for (let j = 0; j < JrgBt[i].JrgMx.length; j++) {
                        jxJrg.push( JrgBt[i].JrgMx[j].$);
                    }
                }
            }

            
        }
        unitProject.otherProjectDayWorks = new Array();
        await this.setDayWorks(unitProject,rgJrg,'人工',1,rgJrgBt);
        await this.setDayWorks(unitProject,clJrg,'材料',2,clJrgBt);
        await this.setDayWorks(unitProject,jxJrg,'机械',3,jxJrgBt);
        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        this.qbExtraTableArray[6].qtJxTotal = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.qtJxTotal)  ;
        }, 0).toFixed(2))
        this.qbExtraTableArray[6].qtCsTotal = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.qtCsTotal)  ;
        }, 0).toFixed(2))

        this.service.yuSuanProject.otherProjectDayWorkService.updateBiaotiTotal(unitProject);
        this.service.yuSuanProject.otherProjectDayWorkService.updateHuiZongTotal(unitProject);
        //this.service.yuSuanProject.otherProjectService.updateAllOtherProjectsXml(unitProject);
    }



    async setDayWorks(unitProject,arr,type,dispNo,JrgBt){
        // let qtJxTotal = 0;
        // let qtCsTotal = 0;
        const dayWorksArray = [];

        if(type === '人工'){
            //计日工汇总
            let otherProject0 = new OtherProjectDayWork();
            otherProject0.sequenceNbr =Snowflake.nextId();
            otherProject0.worksName = '计日工费用汇总';
            otherProject0.jinzhiFlag=true;
            otherProject0.dataType = 0;
            otherProject0.csTotal = 0;
            otherProject0.jxTaxAmount = 0;
            otherProject0.total = 0;
            dayWorksArray.push(otherProject0);
        }

        let otherProjectDayWork = new OtherProjectDayWork();
        let id = Snowflake.nextId();

        otherProjectDayWork.sequenceNbr = id;
        otherProjectDayWork.worksName = JrgBt.Mc;
        otherProjectDayWork.total = JrgBt.Je;
        otherProjectDayWork.dispNo = dispNo;
        otherProjectDayWork.sortNo = 0;
        otherProjectDayWork.constructId = unitProject.constructId;
        otherProjectDayWork.spId = unitProject.spId;
        otherProjectDayWork.unitId = unitProject.sequenceNbr;
        otherProjectDayWork.dataType = 1;

        dayWorksArray.push(otherProjectDayWork);

        if(!ObjectUtils.isEmpty(arr)){
            for (let i = 0; i < arr.length; i++) {
                const element = arr[i];

                otherProjectDayWork = new OtherProjectDayWork();
                otherProjectDayWork.sequenceNbr = Snowflake.nextId();
                otherProjectDayWork.worksName = element.Mc;
                otherProjectDayWork.specification = element.Ggxh;
                otherProjectDayWork.unit = element.Dw;
                otherProjectDayWork.tentativeQuantity = element.Zdsl;

                otherProjectDayWork.price = element.Zhdj;
                otherProjectDayWork.total = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.tentativeQuantity, otherProjectDayWork.price));
                otherProjectDayWork.taxRemoval = element.Csxs;
                if(type === '材料'){
                    otherProjectDayWork.taxRemoval =  11.28;
                }
                if(type === '机械'){
                    otherProjectDayWork.taxRemoval =  8.66;
                }
                otherProjectDayWork.jxTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.total,otherProjectDayWork.taxRemoval/100));
                otherProjectDayWork.csPrice = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.price,(100-otherProjectDayWork.taxRemoval)/100)) ;
                otherProjectDayWork.csTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.total,(100-otherProjectDayWork.taxRemoval)/100)) ;
                otherProjectDayWork.dispNo = dispNo+'.'+(i+1);
                otherProjectDayWork.sortNo = i+1;
                otherProjectDayWork.parentId = id;
                otherProjectDayWork.constructId = unitProject.constructId;
                otherProjectDayWork.spId = unitProject.spId;
                otherProjectDayWork.unitId = unitProject.sequenceNbr;
                otherProjectDayWork.dataType = 2;

                // qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectDayWork.jxTotal);
                // qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectDayWork.csTotal);

                dayWorksArray.push(otherProjectDayWork);
            }
        }else {
            let myNumber = 0;
            let formattedNumber = myNumber.toFixed(2);
            let formattedNumber6 = myNumber.toFixed(6);
            otherProjectDayWork = new OtherProjectDayWork();
            otherProjectDayWork.sequenceNbr = Snowflake.nextId();
            otherProjectDayWork.dispNo = dispNo+".1";
            otherProjectDayWork.tentativeQuantity = formattedNumber6;
            otherProjectDayWork.price = formattedNumber;
            otherProjectDayWork.total = formattedNumber;
            otherProjectDayWork.taxRemoval = formattedNumber;
            otherProjectDayWork.jxTotal = formattedNumber;
            otherProjectDayWork.csPrice =formattedNumber;
            otherProjectDayWork.csTotal = formattedNumber;
            otherProjectDayWork.dataType = 2;
            otherProjectDayWork.parentId =id ;
            otherProjectDayWork.rcjFlag = OtherProjectDayWorkRcjConstant.rg;
            
            if(type === '材料'){
                otherProjectDayWork.taxRemoval =  11.28;
                otherProjectDayWork.rcjFlag = OtherProjectDayWorkRcjConstant.cl;
            }
            if(type === '机械'){
                otherProjectDayWork.taxRemoval =  8.66;
                otherProjectDayWork.rcjFlag = OtherProjectDayWorkRcjConstant.jx;
            }
            dayWorksArray.push(otherProjectDayWork);
        }

        unitProject.otherProjectDayWorks.push(...dayWorksArray);

    }



    /**
     * 分部分项
     * @param 分部分项工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertItemBill(Qdxm, unitProject) {
        let itemBillProjectArray = [];
        let itemBillProject = new ItemBillProject();
        let topId = Snowflake.nextId();
        itemBillProject.sequenceNbr = topId;
        itemBillProject.name = '单位工程';
        itemBillProject.kind = BranchProjectLevelConstant.top;
        itemBillProject.constructId = unitProject.constructId;
        itemBillProject.spId = unitProject.spId;
        itemBillProject.unitId = unitProject.sequenceNbr;
        itemBillProject.displaySign = BranchProjectDisplayConstant.open;
        itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
        itemBillProjectArray.push(itemBillProject);
        if(ObjectUtils.isNotEmpty(Qdxm)) {
            let QdBt = Qdxm[0].QdBt;
            if (ObjectUtils.isEmpty(QdBt)) {
                let Qdmx = Qdxm[0].Qdmx;
                if (!ObjectUtils.isEmpty(Qdmx)) {
                    await this.convertItemBillQd(Qdmx, topId, unitProject, itemBillProjectArray);
                }
                unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
                return;
            }

            let kind = BranchProjectLevelConstant.fb;
            // 递归处理子项目
            const createSubProjects = async (subProjects, parentId, kind) => {
                for (let i = 0; i < subProjects?.length; i++) {
                    let subProjectElement = subProjects[i];
                    let $ = subProjectElement.$;
                    itemBillProject = new ItemBillProject();
                    let id = Snowflake.nextId();
                    itemBillProject.sequenceNbr = id;
                    itemBillProject.name = $.Mc;
                    itemBillProject.name = $.Mc;
                    itemBillProject.total = $.Je;
                    itemBillProject.kind = kind;
                    itemBillProject.parentId = parentId;
                    itemBillProject.constructId = unitProject.constructId;
                    itemBillProject.spId = unitProject.spId;
                    itemBillProject.unitId = unitProject.sequenceNbr;
                    itemBillProject.displaySign = BranchProjectDisplayConstant.open;
                    itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
                    itemBillProjectArray.push(itemBillProject);

                    if (ObjectUtils.isEmpty(subProjectElement.Qdmx)) {
                        await createSubProjects(subProjectElement.QdBt, id, BranchProjectLevelConstant.zfb);
                    } else {
                        let Qdmx = subProjectElement.Qdmx;
                        await this.convertItemBillQd(Qdmx, id, unitProject, itemBillProjectArray);

                    }
                }
            };

            await createSubProjects(QdBt, topId, kind);
        }
        unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
    }


    async convertItemBillQd(Qdmx, id, unitProject, itemBillProjectArray) {
        for (let t = 0; t < Qdmx.length; t++) {
            let $ = Qdmx[t].$;
            let itemBillProject = new ItemBillProject();
            itemBillProject.sequenceNbr = Snowflake.nextId();
            itemBillProject.name = $.Mc;
            itemBillProject.name = $.Mc;
            itemBillProject.bdCode = $.Qdbm;
            itemBillProject.fxCode = $.Qdbm;
            itemBillProject.unit = $.Dw;
            itemBillProject.quantity = $.Sl;
            //清单工程量=清单工程量表达式/单位符号前数值
            let 单位num = $.Dw.replace(/[^0-9].*/ig, '') !== '' ? $.Dw.replace(/[^0-9].*/ig, '') : 1;
            itemBillProject.quantityExpression = NumberUtil.multiplyToString(单位num, $.Sl, 3);
            itemBillProject.quantityExpressionNbr = NumberUtil.multiplyToString(单位num, $.Sl, 3);
            // itemBillProject.rfee = $.人工费单价;
            // itemBillProject.cfee = $.材料费单价;
            // itemBillProject.jfee = $.机械费单价;
            // itemBillProject.managerFee = $.管理费单价;
            // itemBillProject.profitFee = $.利润单价;
            // itemBillProject.price = $.综合单价;
            // itemBillProject.total = $.综合合价;
            itemBillProject.projectAttr = $.Xmtz;
            itemBillProject.kind = BranchProjectLevelConstant.qd;
            itemBillProject.dispNo = (this.dispNo++) + '';
            itemBillProject.parentId = id;
            itemBillProject.constructId = unitProject.constructId;
            itemBillProject.spId = unitProject.spId;
            itemBillProject.unitId = unitProject.sequenceNbr;
            itemBillProject.displaySign = BranchProjectDisplayConstant.noSign;
            itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
            if (ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))) {
                //如果map中没有去查数据库
                let res = await this.service.yuSuanProject.baseListService.queryQdByCode(itemBillProject.fxCode);
                if (!ObjectUtils.isEmpty(res)) {
                    this.qdMap.set(itemBillProject.fxCode, res)
                }
            }
            itemBillProject.standardId = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? this.qdMap.get(itemBillProject.fxCode).sequenceNbr : '';
            itemBillProject.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? 0 : 1;
            itemBillProjectArray.push(itemBillProject);
        }
    }

    /**
     * 单价措施项目（目前只处理单价措施清单）
     * @param 措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableDJ(Csxm, unitProject) {
        if(ObjectUtils.isEmpty(Csxm)){
            return ;
        }


        if(!ObjectUtils.isEmpty(Csxm[0].DjCs)){
            let djMeasureProjectTableArray = new Array();

            let DjCsBt = Csxm[0].DjCs[0].DjCsBt;
            let dispNo =1;
            //遍历标题
            for (let j = 0; j < DjCsBt.length; j++) {
                let $ = DjCsBt[j].$;
                //存放标题
                let measureProjectTableBt = new MeasureProjectTable();
                let btId = Snowflake.nextId();
                measureProjectTableBt.sequenceNbr = btId;
                // measureProjectTableBt.fxCode = $.编码;
                measureProjectTableBt.name = $.Mc;
                measureProjectTableBt.total = $.Je;
                measureProjectTableBt.constructId = unitProject.constructId;
                measureProjectTableBt.spId = unitProject.spId;
                measureProjectTableBt.unitId = unitProject.sequenceNbr;
                measureProjectTableBt.kind = BranchProjectLevelConstant.zfb;
                measureProjectTableBt.displaySign = BranchProjectDisplayConstant.open;
                measureProjectTableBt.displayStatu = BranchProjectDisplayConstant.displayMax;
                measureProjectTableBt.adjustmentCoefficient = 1;
                djMeasureProjectTableArray.push(measureProjectTableBt)
                //存放标题下的清单
                let DjCsMx = DjCsBt[j].DjCsMx;
                if(!ObjectUtils.isEmpty(DjCsMx)){
                    for (let k = 0; k < DjCsMx.length; k++) {
                        let $ = DjCsMx[k].$ ;
                        let measureProjectTable = new MeasureProjectTable();
                        measureProjectTable.sequenceNbr =Snowflake.nextId();
                        measureProjectTable.dispNo = (dispNo++)+'';
                        measureProjectTable.name = $.Mc;
                        measureProjectTable.name = $.Mc;
                        measureProjectTable.bdCode = $.Xmbm;
                        measureProjectTable.fxCode = $.Xmbm;
                        measureProjectTable.total = $.Zhhj;
                        measureProjectTable.projectAttr = $.Xmtz;
                        measureProjectTable.unit = $.Dw;
                        measureProjectTable.quantity = $.Sl;
                        let 单位num = $.Dw.replace(/[^0-9].*/ig,'')!==''?$.Dw.replace(/[^0-9].*/ig,''): 1;
                        measureProjectTable.quantityExpression =NumberUtil.multiplyToString(单位num, $.Sl,3);
                        measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToString(单位num, $.Sl,3);
                        // measureProjectTable.rfee = $.人工费单价;
                        // measureProjectTable.cfee = $.材料费单价;
                        // measureProjectTable.jfee = $.机械费单价;
                        // measureProjectTable.managerFee = $.管理费单价;
                        // measureProjectTable.profitFee = $.利润单价;
                        // measureProjectTable.price = $.综合单价;
                        // measureProjectTable.total = $.综合合价;
                        measureProjectTable.kind = BranchProjectLevelConstant.qd;
                        //单价措施类型
                        measureProjectTable.parentId =btId;
                        measureProjectTable.constructId = unitProject.constructId;
                        measureProjectTable.spId = unitProject.spId;
                        measureProjectTable.unitId = unitProject.sequenceNbr;
                        measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                        measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                        if (ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))) {
                            //如果map中没有去查数据库
                            let res = await this.service.yuSuanProject.baseListService.queryQdByCode(measureProjectTable.fxCode);
                            if (!ObjectUtils.isEmpty(res)) {
                                this.qdMap.set(measureProjectTable.fxCode, res)
                            }
                        }
                        measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr : '';
                        measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                        measureProjectTable.adjustmentCoefficient = 1;
                        djMeasureProjectTableArray.push(measureProjectTable);
                    }
                }
            }

            unitProject.djMeasureProjectTableArray = djMeasureProjectTableArray;
        }

    }

    /**
     *
     * @param 总价措施措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableZJ(Csxm, unitProject) {
        if(ObjectUtils.isEmpty(Csxm)){
            return ;
        }
        let ZjCsBt = Csxm[0].ZjCs[0].ZjCsBt;
        if(!ObjectUtils.isEmpty(ZjCsBt)){
            for (let i = 0; i < ZjCsBt.length; i++) {
                let ZjCsBtElement = ZjCsBt[i];
                let $ = ZjCsBtElement.$;
                if($.Mc==='安全生产、文明施工费'){
                    let awfMeasureProjectTableArray = new Array();
                    let ZjCsMx = ZjCsBtElement.ZjCsMx;
                    if(!ObjectUtils.isEmpty(ZjCsMx)){
                        for (let k = 0; k < ZjCsMx.length; k++) {
                            $ = ZjCsMx[k].$ ;
                            let measureProjectTable = new MeasureProjectTable();
                            measureProjectTable.sequenceNbr =Snowflake.nextId();
                            measureProjectTable.dispNo = (k+1)+'';
                            measureProjectTable.name = $.Mc;
                            measureProjectTable.name = $.Mc;
                            measureProjectTable.bdCode = $.Xmbm;
                            measureProjectTable.fxCode = $.Xmbm;
                            measureProjectTable.total = $.Zhhj;
                            measureProjectTable.projectAttr = $.Xmtz;
                            measureProjectTable.unit = '项';
                            measureProjectTable.quantity = 1;//因为EZJC总价措施安稳费没有工程量
                            measureProjectTable.quantityExpression =1;
                            measureProjectTable.quantityExpressionNbr = 1;

                            measureProjectTable.kind = BranchProjectLevelConstant.qd;
                            measureProjectTable.constructId = unitProject.constructId;
                            measureProjectTable.spId = unitProject.spId;
                            measureProjectTable.unitId = unitProject.sequenceNbr;
                            measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                            measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                            measureProjectTable.adjustmentCoefficient = 1;
                            if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                //如果map中没有去查数据库
                                let res =await this.service.yuSuanProject.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                if(!ObjectUtils.isEmpty(res)){
                                    this.qdMap.set(measureProjectTable.fxCode,res)
                                }
                            }
                            if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                    measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                }
                            }
                            measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                            awfMeasureProjectTableArray.push(measureProjectTable)
                        }
                        unitProject.awfMeasureProjectTableArray = awfMeasureProjectTableArray;
                    }
                }else if($.Mc=== '其他总价措施'){
                    let zjMeasureProjectTableArray = new Array();
                    let ZjCsMx = ZjCsBtElement.ZjCsMx;
                    if(!ObjectUtils.isEmpty(ZjCsMx)){
                        for (let k = 0; k < ZjCsMx.length; k++) {
                            $ = ZjCsMx[k].$ ;
                            let measureProjectTable = new MeasureProjectTable();
                            measureProjectTable.sequenceNbr =Snowflake.nextId();
                            measureProjectTable.dispNo = (k+1)+'';
                            measureProjectTable.name = $.Mc;
                            measureProjectTable.name = $.Mc;
                            measureProjectTable.bdCode = $.Xmbm;
                            measureProjectTable.fxCode = $.Xmbm;
                            measureProjectTable.total = $.Zhhj;
                            measureProjectTable.projectAttr = $.Xmtz;
                            measureProjectTable.unit = '项';
                            measureProjectTable.quantity = 1;//因为EZJC总价措施安稳费没有工程量
                            measureProjectTable.quantityExpression =1;
                            measureProjectTable.quantityExpressionNbr = 1;
                            // measureProjectTable.rfee = $.人工费单价;
                            // measureProjectTable.cfee = $.材料费单价;
                            // measureProjectTable.jfee = $.机械费单价;
                            // measureProjectTable.managerFee = $.管理费单价;
                            // measureProjectTable.profitFee = $.利润单价;
                            // measureProjectTable.price = $.综合单价;
                            // measureProjectTable.total = $.综合合价;
                            measureProjectTable.kind = BranchProjectLevelConstant.qd;
                            measureProjectTable.constructId = unitProject.constructId;
                            measureProjectTable.spId = unitProject.spId;
                            measureProjectTable.unitId = unitProject.sequenceNbr;
                            measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                            measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                            measureProjectTable.adjustmentCoefficient = 1;
                            if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                //如果map中没有去查数据库
                                let res = await this.service.yuSuanProject.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                if(!ObjectUtils.isEmpty(res)){
                                    this.qdMap.set(measureProjectTable.fxCode,res);
                                }
                            }
                            if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                    measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                }
                            }
                            measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                            zjMeasureProjectTableArray.push(measureProjectTable);
                        }
                        unitProject.zjMeasureProjectTableArray = zjMeasureProjectTableArray;
                    }
                }

            }
        }
    }

    async convertOtherProjects(QtxmMx, unitProject) {

        if(!ObjectUtils.isEmpty(QtxmMx)){
            for (let i = 0; i < QtxmMx.length; i++) {
                let model = QtxmMx[i].$;
                let otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.sortNo = 1;
                otherProject.extraName = model.Mc;
                otherProject.dispNo = model.Xh;
                otherProject.total = model.Je;
                // otherProject.unit = model.单位;
                // otherProject.description = model.备注;
                if('暂列金额' === model.Mc){
                    otherProject.type = OtherProjectCalculationBaseConstant.zljr;
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                    otherProject.putOntotalFlag = true;
                }else if('暂估价' === model.Mc){
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                    otherProject.putOntotalFlag = true;
                }else if('专业工程暂估价' === model.Mc){
                    otherProject.type = OtherProjectCalculationBaseConstant.zygczgj;
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                }else if('总承包服务费' === model.Mc){
                    otherProject.type = OtherProjectCalculationBaseConstant.zcbfwf;
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                    otherProject.putOntotalFlag = true;
                }else if('计日工' === model.Mc){
                    otherProject.type = OtherProjectCalculationBaseConstant.jrg;
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                    otherProject.putOntotalFlag = true;
                }else {
                    otherProject.markSj = 0;
                    otherProject.markSafa = 0;
                }
                if('暂列金额' === model.Mc
                    || '暂估价' === model.Mc
                    || '材料暂估价' === model.Mc
                    || '设备暂估价' === model.Mc
                    || '专业工程暂估价' === model.Mc
                    || '总承包服务费' === model.Mc
                    || '计日工' === model.Mc
                ){
                    this.qbExtraTableArray.push(otherProject);
                }

            }
        }

    }

}


AnalyzingXMLServiceYSF.toString = () => '[class AnalyzingXMLServiceYSF]';
module.exports = AnalyzingXMLServiceYSF;