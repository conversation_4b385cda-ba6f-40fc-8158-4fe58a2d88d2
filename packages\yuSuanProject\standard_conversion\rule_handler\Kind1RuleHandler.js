const RuleHandler = require("./ruleHandler");

class Kind1RuleHandler extends RuleHandler{

    deCodeUpdateInfo() {
        let redSubArray = [];
        for (let handler of this.rule.mathHandlers) {
                redSubArray.push(handler.oriMath);
        }
        return {redStr: redSubArray.join(","), blackStr: null}
    }

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = this.rule.relation;
    }


    deNameUpdateInfo(rule){
        return rule.relation;
    }
}

module.exports = Kind1RuleHandler;
