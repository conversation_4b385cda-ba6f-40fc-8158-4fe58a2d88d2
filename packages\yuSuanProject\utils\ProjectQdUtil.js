// exceljs 所需的 polyfills
// require('core-js/modules/es.promise');
// require('core-js/modules/es.string.includes');
// require('core-js/modules/es.object.assign');
// require('core-js/modules/es.object.keys');
// require('core-js/modules/es.symbol');
// require('core-js/modules/es.symbol.async-iterator');
// require('regenerator-runtime/runtime');

// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("./ObjectUtils");
const {ExcelUtil} = require("./ExcelUtil");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../../core');
class ProjectQdUtil {

    constructor() {
    }
    /********工程项目层级*************/
    //封面1 工程量清单
    async writeDataToCover1(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName!=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招　标　人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充造价咨询人
        let adviceCell = ExcelUtil.findValueCell(worksheet,"造价咨询人：");
        if (adviceCell != null) {
            let filterAdvice = data.filter(object => object.name=="造价咨询人")[0];
            let adviceRow = worksheet.getRow(adviceCell.cell._row._number);
            if (filterAdvice !=null && filterAdvice.remark != null) {
                adviceRow._cells[4].value = filterAdvice.remark;
            }
            ExcelUtil.traversalRowToCellBottom(adviceRow);
        }
        //填充编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport !=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    //扉页1 工程量清单
    async writeDataToCover2(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName !=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招　标　人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[2].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充委托代理人
        let agentCell = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell != null) {
            let filterAgent = data.filter(object => object.name=="法定代表人或委托代理人(第三行)")[0];
            let agentRow = worksheet.getRow(agentCell.cell._row._number);
            if (filterAgent!=null && filterAgent.remark != null) {
                agentRow._cells[5].value = filterAgent.remark;
            }
        }

        //工程造价咨询人
        let adviceCell = ExcelUtil.findValueCell(worksheet,"工程造价　\n咨 询 人：");
        if (adviceCell != null) {
            let filterAdvice = data.filter(object => object.name=="工程造价咨询人")[0];
            let adviceRow = worksheet.getRow(adviceCell.cell._row._number);
            if (filterAdvice!=null && filterAdvice.remark != null) {
                adviceRow._cells[4].value = filterAdvice.remark;
            }
            ExcelUtil.traversalRowToCellBottom(adviceRow);
        }
        //委托代理人
        let agentCell2 = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell2 != null) {
            let filterAgent2 = data.filter(object => object.name=="法定代表人或委托代理人(第五行)")[0];
            let agentRow2 = worksheet.getRow(agentCell2.cell._row._number);
            if (filterAgent2 !=null && filterAgent2.remark != null) {
                agentRow2._cells[5].value = filterAgent2.remark;
            }
        }
        //编制人
        let organizeCell = ExcelUtil.findValueCell(worksheet,"编 制 人：");
        if (organizeCell != null) {
            let organizeAgent = data.filter(object => object.name=="编制人")[0];
            let organizeRow = worksheet.getRow(organizeCell.cell._row._number);
            if (organizeCell!=null && organizeAgent.remark != null) {
                organizeRow._cells[4].value = organizeAgent.remark;
            }
            ExcelUtil.traversalRowToCellBottom(organizeRow);
        }
        //复核人
        let verifyCell = ExcelUtil.findValueCell(worksheet,"复　核　人：");
        let filterVerify = data.filter(object => object.name=="复核人")[0];
        let verifyRow = worksheet.getRow(verifyCell.cell._row._number);
        if (filterVerify!=null && filterVerify.remark != null) {
            verifyRow._cells[5].value = filterVerify.remark;
        }
        //编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }

    //工程项目总价表  和单位层级的模板表结构一样
    async writeDataToSheet1(data, worksheet,constructIs2022) {
        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                        worksheet.mergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = data[countRow].sortNo+"";
                }
                if (cell.col == 2) {
                    cell.value = data[countRow].name;
                }
                if (cell.col == 4) {
                    cell.value = null; //金额
                }
                if (constructIs2022) {
                    if (cell.col == 5) {
                        cell.value = null;//安文费
                        if (rowObject._cells[1].value.includes("设备费及其税金")) {
                            cell.value = "/";
                        }
                    }
                }else{
                    if (cell.col == 6) {
                        cell.value = null;//规费
                        if (rowObject._cells[1].value.includes("设备费及其税金")) {
                            cell.value = "/";
                        }
                    }
                    if (cell.col == 7) {
                        cell.value = null;//安文费
                        if (rowObject._cells[1].value.includes("设备费及其税金")) {
                            cell.value = "/";
                        }
                    }
                }
            }
        }

        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet," 合　　计");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[3].value = null;//金额合计
        if (constructIs2022) {
            row._cells[4].value = null;//安文费合计
        }else {
            row._cells[4].value = null;//规费合计
            row._cells[5].value = null;//安文费合计
        }
    }

    //表1-4 单项工程费汇总表
    async writeDataToSheet2(data, worksheet,constructIs2022) {
        let dataList = data.analysisZJ;
        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataList.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                        worksheet.mergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataList[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = dataList[countRow].projectName+" 合计";//名称
                }
                if (cell.col == 4) {
                    cell.value = null;
                }
                if (constructIs2022) {
                    if (cell.col == 5) {
                        cell.value = null;//安文费
                    }
                }else {
                    if (cell.col == 6) {
                        cell.value = null;//规费
                    }
                    if (cell.col == 7) {
                        cell.value = null;//安文费
                    }
                }

            }
        }
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合　计(不含设备费)");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[3].value = null;//金额合计
        if (constructIs2022) {
            row._cells[4].value = null;//安文费合计
        }else {
            row._cells[4].value = null;//规费合计
            row._cells[5].value = null;//安文费合计
        }
    }
    /********单项工程层级*************/
    // 表1-5单项工程费汇总表
    async writeDataToSheet5(data, worksheet) {
        let dataTotal = [];
        let totalUnit = 0;
        let totalRg = 0;
        let totalCl = 0;
        let totalJx = 0;
        for (let i = 0; i < data.length; i++) {
            totalUnit = totalUnit+data[i][0].price
            totalRg= totalRg + data[i][0].rg
            totalCl= totalCl + data[i][0].cl
            totalJx= totalJx + data[i][0].jx
            for (let m = 0; m < data[i].length; m++) {
                dataTotal.push(data[i][m]);
            }
        }

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataTotal.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                        worksheet.mergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataTotal[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = dataTotal[countRow].name;
                }
                if (cell.col == 4) {
                    cell.value = dataTotal[countRow].instructions;
                }
                if (cell.col == 5) {
                    cell.value = dataTotal[countRow].rate;//费率
                    if (cell.value==100) {
                        cell.value = "/";
                    }
                }
                if (cell.col == 6) {
                    cell.value = null;  //金额
                }
                if (cell.col == 8) {
                    cell.value = null;  //人工费
                }
                if (cell.col == 9) {
                    cell.value = null;  //材料费
                }
                if (cell.col == 10) {
                    cell.value = null;  //机械费
                }
            }
        }
        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = null;
        row._cells[7].value = null;
        row._cells[8].value = null;
        row._cells[9].value = null;
    }
    /********单位层级*************/
    // 表1-5 单位工程费汇总表
    async writeDataToSheet8(dataTotal, worksheet) {
        // let fbfx = dataTotal.filter(object => object.name=="分部分项工程量清单计价合计")[0];
        // let csxm = dataTotal.filter(object => object.name=="措施项目清单计价合价")[0];
        // let csxmDj = dataTotal.filter(object => object.name=="单价措施项目工程量清单计价合计")[0];
        // let csxmOther = dataTotal.filter(object => object.name=="其他总价措施项目清单计价合计")[0];
        // csxm.rg = csxmDj.rg+csxmOther.rg;
        // csxm.cl = csxmDj.cl+csxmOther.cl;
        // csxm.jx = csxmDj.jx+csxmOther.jx;
        // //含税工程总价
        // // let projectCost = dataTotal.filter(object => object.name.endsWith("工程造价"))[0];
        // let projectCostObj = dataTotal.filter(object => object.name === "工程造价");
        // if (ObjectUtils.isEmpty(filter)) {
        //     projectCostObj = dataTotal.filter(object => object.name === "含税工程造价");
        // }
        // let projectCost = projectCostObj[0];
        // let totalRg = (Number.parseFloat(csxm.rg)+Number.parseFloat(fbfx.rg)).toFixed(2);
        // let totalCl = (Number.parseFloat(csxm.cl)+Number.parseFloat(fbfx.cl)).toFixed(2);
        // let totalJx = (Number.parseFloat(csxm.jx)+Number.parseFloat(fbfx.jx)).toFixed(2);
        //对数据进行处理
        let countRow = -1;
        for (let i = 0; i < worksheet._rows.length; i++) {
            let rowNum = worksheet._rows[i];
            if (rowNum.number >= 5) {  //从第五行开始填充
                countRow++;//从索引零开始 填充
                if (countRow>=dataTotal.length) break;
                for (let j = 0; j < rowNum._cells.length; j++) {
                    let cell = rowNum._cells[j];
                    if (cell.col == 1) {
                        cell.value = dataTotal[countRow].dispNo+"";
                    }
                    if (cell.col == 2){
                        cell.value = dataTotal[countRow].name;
                    }
                    if (cell.col == 4) {
                        cell.value = dataTotal[countRow].instructions;
                    }
                    if (cell.col == 5) {
                        if (dataTotal[countRow].rate == 100) {
                            cell.value = "/";
                        }else {
                            cell.value = dataTotal[countRow].rate;//费率
                        }
                    }
                    if (cell.col == 6) {
                        cell.value = null;  //金额
                    }
                    if (cell.col == 8) {
                        cell.value = null;  //人工费
                        if (!(rowNum._cells[0].value == "1"||rowNum._cells[0].value == "2"||rowNum._cells[0].value == "2.1"
                        || rowNum._cells[0].value == "2.2"|| rowNum._cells[0].value == "6")
                        ) {
                            cell.value = "/";
                        }
                    }
                    if (cell.col == 9) {
                        cell.value = null;  //材料费
                        if (!(rowNum._cells[0].value == "1"||rowNum._cells[0].value == "2"||rowNum._cells[0].value == "2.1"
                            || rowNum._cells[0].value == "2.2"|| rowNum._cells[0].value == "6")
                        ) {
                            cell.value = "/";
                        }
                    }
                    if (cell.col == 10) {
                        cell.value = null;  //机械费
                        if (!(rowNum._cells[0].value == "1"||rowNum._cells[0].value == "2"||rowNum._cells[0].value == "2.1"
                            || rowNum._cells[0].value == "2.2"|| rowNum._cells[0].value == "6")
                        ) {
                            cell.value = "/";
                        }
                    }
                }
            }
        }

        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = null;
        row._cells[7].value = null;
        row._cells[8].value = null;
        row._cells[9].value = null;
    }

    // 表1-6 分部分项工程量清单与计价表
    async writeDataToUnitSheet9(data, worksheet,arg) {

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                        worksheet.mergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].bdCode;
                if (cell.col == 3) cell.value = data[countRow].name;
                if (cell.col == 5) cell.value = data[countRow].projectAttr;
                if (cell.col==6) cell.value = data[countRow].unit;//计量单位  缺失
                if (cell.col == 7) cell.value = data[countRow].quantity;
                if (cell.col == 9){
                    cell.value = null;
                }
                if (cell.col == 10){
                    cell.value = null;
                }
            }
        }
    }
    // 表1-7 单价措施项目工程量清单与计价表
    async writeDataToUnitSheet10(data, worksheet) {
        //增加第一行数据 项目名称为 单价措施项目
        data.unshift({'name':"单价措施项目",'fxCode':"2"});

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                        worksheet.mergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = data[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = data[countRow].fxCode;
                }
                if (cell.col == 3) {
                    cell.value = data[countRow].name;
                }
                if (cell.col == 5) {
                    cell.value = data[countRow].projectAttr;
                }
                if (cell.col == 6) {
                    cell.value = data[countRow].unit;//计量单位  缺失
                }
                if (cell.col == 7) {
                    cell.value = data[countRow].quantity;
                }
                if (cell.col == 9) {
                    cell.value = null;
                }
                if (cell.col == 10) {
                    cell.value = null;
                }
            }
        }
    }
    // 表1-8 总价措施项目清单与计价表
    async writeDataToUnitSheet11(data, worksheet) {

        //先填充安文费
        let row = worksheet._rows[4];//安文费序号为1的那一行
        row._cells[1].value = data[0][0].fxCode;
        row._cells[2].value = data[0][0].name;
        row._cells[5].value = null;
        let awenXiaoJi = worksheet._rows[5];
        awenXiaoJi._cells[5].value = null;
        //增加其他总价措施的小计
        let otherTotal = 0;
        data[1].forEach(function(element) {
            if (element.total != null) {
                otherTotal += element.total;
            }
        });
        data[1].push({"fxCode":"/","name":"小计","total":otherTotal});
        //填充其他总价措施项目
        let headCount = 6;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data[1].length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                        worksheet.mergeCells([top+copyDistance,left,bottom+copyDistance,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = data[1][countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = data[1][countRow].fxCode;
                }
                if (cell.col == 3) {
                    cell.value = data[1][countRow].name;
                    if (cell.value != null && cell.value == "小计") {
                        //对小计的格式做出处理
                        cell.style.alignment.horizontal = "center";
                    }
                }
                if (cell.col == 6) {
                    cell.value = null;
                }
            }
        }
    }
}
module.exports = {
    ProjectQdUtil: new ProjectQdUtil()
};
