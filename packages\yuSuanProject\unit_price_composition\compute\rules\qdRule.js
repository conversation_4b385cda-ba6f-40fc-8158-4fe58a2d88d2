/*
* 清单金额填充规则
* */
/*
* 清单行填充规则---------------------------------start-----------------------------------
* */
const {Gene} = require("@valuation/rules-engine");
const baseFn = {
    //定额综合单价
    "DE_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "total"
        };
    },
    //人工费合价
    "DE_RGF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "totalRfee"
        };
    },
    //材料费合价
    "DE_CLF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "totalCfee"
        };
    },
    //机械费合价
    "DE_JXF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "totalJfee"
        };
    },
    //主材费合价
    "DE_ZCF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "totalZcfee"
        };
    },
    //设备费合价
    "DE_SBF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "sbfTotal"
        };
    },
    //暂估合价
    "DE_ZGF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "zgfTotal"
        };
    },
    //管理费合价
    "DE_GLF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "totalManagerFee"
        };
    },
    //利润合价
    "DE_LR_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "totalProfitFee"
        };
    },
    //规费合价
    "DE_GF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "gfTotal"
        };
    },
    //直接费合价
    "DE_ZJF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "zjfTotal"
        };
    },
    "quantity": () => {
        return {
            "type": "qd",
            "kind": "",
            "cloumn": "quantity"
        };
    },

    //生产工具使用费合价
    "DE_SCGJSYF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "scgjsyfTotal"
        };
    },
    //繁华地段管理增加费合价
    "DE_FHDDGLZJF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "fhddglzjfTotal"
        };
    },
    //冬季防寒费合价
    "DE_GJFHF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "gjfhfTotal"
        };
    }
    ,
    //山地管护增加费合价
    "DE_SDGHZJF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "sdghzjfTotal"
        };
    }
    ,
    //绿色施工安全防护措施费合价
    "DE_LSSGAQFHCSF_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "lssgaqfhcsfTotal"
        };
    },
    //进项税额合价
    "DE_JXSE_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "jxseTotal"
        };
    },
    //销项税额合价
    "DE_XXSE_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "xxseTotal"
        };
    },
    //增值税应纳税额合价
    "DE_ZZSYNSE_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "zzsynseTotal"
        };
    },
    //附加税费合价
    "DE_FJSE_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "fjseTotal"
        };
    },
    //税前工程造价合价
    "DE_SQGCZJ_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "sqgczjTotal"
        };
    },
    //风险费用合价
    "DE_FXFY_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "fxfyTotal"
        };
    },
    //税金合价
    "DE_SJ_TOTAL": () => {
        return {
            "type": "DE",
            "kind": "",
            "cloumn": "sjTotal"
        };
    }

}
const rules = {
    "sbfPrice": {
        "name": "设备费单价",
        "mathFormula": `quantity>0?(sbfTotal/quantity):0`
    },
    "sbfTotal": {
        "name": "设备费合价",
        "mathFormula": `_.sum(DE_SBF_TOTAL)`
    },
    "zgfPrice": {
        "name": "暂估单价",
        "mathFormula": `quantity>0?(zgfTotal/quantity):0`
    },
    "zgfTotal": {
        "name": "暂估合价",
        "mathFormula": `_.sum(DE_ZGF_TOTAL)`
    },
    "zjfPrice": {
        "name": "直接费单价",
        "mathFormula": `quantity>0?(zjfTotal/quantity):0`
    },
    //直接费合价
    "zjfTotal": {
        "name": "直接费合价",
        "mathFormula": `_.sum(DE_ZJF_TOTAL)`
    },
    //人工费单间
    "rfee": {
        "name": "人工费单价",
        "mathFormula": `quantity>0?(totalRfee/quantity):0`
    },
    //人工费合价
    "totalRfee": {
        "name": "人工费合价",
        "mathFormula": `_.sum(DE_RGF_TOTAL)`
    }
    ,
    //材料费单间
    "cfee": {
        "name": "材料费单价",
        "mathFormula": `quantity>0?(totalCfee/quantity):0`
    },
    //材料费合价
    "totalCfee": {
        "name": "材料费合价",
        "mathFormula": `_.sum(DE_CLF_TOTAL)`
    }
    ,
    //机械费UPC_JXF_PRICE
    "jfee": {
        "name": "机械费单价",
        "mathFormula": `quantity>0?(totalJfee/quantity):0`
    },
    //机械费合价
    "totalJfee": {
        "name": "机械费合价",
        "mathFormula": `_.sum(DE_JXF_TOTAL)`
    },
    //利润费单间
    "profitFee": {
        "name": "利润费单价",
        "mathFormula": `quantity>0?(totalProfitFee/quantity):0`
    },
    //利润费合价
    "totalProfitFee": {
        "name": "利润费合价",
        "mathFormula": `_.sum(DE_LR_TOTAL)`
    }
    ,
    //管理费费单间 UPC_GLF_PRICE
    "managerFee": {
        "name": "管理费单价",
        "mathFormula": `quantity>0?(totalManagerFee/quantity):0`
    },
    //管理费合价  UPC_GLF_TOTAL
    "totalManagerFee": {
        "name": "管理费合价",
        "mathFormula": `_.sum(DE_GLF_TOTAL)`
    }
    ,
    //主材费单间  UPC_ZCF_PRICE
    "zcfee": {
        "name": "主材费单间",
        "mathFormula": `quantity>0?(totalZcfee/quantity):0`

    },
    //主材费合价 UPC_ZCF_TOTAL
    "totalZcfee": {
        "name": "主材费合价",
        "mathFormula": `_.sum(DE_ZCF_TOTAL)`
    }
    ,
    //工程造价单间
    "price": {
        "name": "工程造价单价",
        "mathFormula": Gene.from(["quantity","DE_TOTAL"],({quantity,DE_TOTAL,_})=>{
          return _.sumBy(DE_TOTAL, pair => quantity>0?pair/quantity:0);
        })//`quantity>0?DE_TOTAL/quantity:0`//（Σ下挂定额【综合合价】）/清单【工程量】
    },
    //工程造价合价 UPC_GCZJ_TOTAL
    "total": {
        "name": "工程造价合价",
        "mathFormula": `price*quantity`
    },
    "gfPrice": {
        "name": "规费单价",
        "mathFormula": `quantity>0?(gfTotal/quantity):0`
    },
    "gfTotal": {
        "name": "规费合价",
        "mathFormula": `_.sum(DE_GF_TOTAL)`
    },
    "scgjsyfPrice": {
        "name": "生产工具使用费单价",
        "mathFormula": `quantity>0?(scgjsyfTotal/quantity):0`
    },
    "scgjsyfTotal": {
        "name": "生产工具使用费合价",
        "mathFormula": `_.sum(DE_SCGJSYF_TOTAL)`
    },
    "fhddglzjfPrice": {
        "name": "繁华地段管理增加费单价",
        "mathFormula": `quantity>0?(fhddglzjfTotal/quantity):0`
    },
    "fhddglzjfTotal": {
        "name": "繁华地段管理增加费合价",
        "mathFormula": `_.sum(DE_FHDDGLZJF_TOTAL)`
    },
    "gjfhfPrice": {
        "name": "冬季防寒费单价",
        "mathFormula": `quantity>0?(gjfhfTotal/quantity):0`
    },
    "gjfhfTotal": {
        "name": "冬季防寒费合价",
        "mathFormula": `_.sum(DE_GJFHF_TOTAL)`
    },
    "sdghzjfPrice": {
        "name": "山地管护增加费单价",
        "mathFormula": `quantity>0?(sdghzjfTotal/quantity):0`
    },
    "sdghzjfTotal": {
        "name": "山地管护增加费合价",
        "mathFormula": `_.sum(DE_SDGHZJF_TOTAL)`
    },
    "lssgaqfhcsfPrice": {
        "name": "绿色施工安全防护措施费单价",
        "mathFormula": `quantity>0?(lssgaqfhcsfTotal/quantity):0`
    },
    "lssgaqfhcsfTotal": {
        "name": "绿色施工安全防护措施费合价",
        "mathFormula": `_.sum(DE_LSSGAQFHCSF_TOTAL)`
    },

    "jxsePrice": {
        "name": "进项税额单价",
        "mathFormula": `quantity>0?(jxseTotal/quantity):0`
    },
    "jxseTotal": {
        "name": "进项税额合价",
        "mathFormula": `_.sum(DE_JXSE_TOTAL)`
    }
    ,
    "xxsePrice": {
        "name": "销项税额单价",
        "mathFormula": `quantity>0?(xxseTotal/quantity):0`
    },
    "xxseTotal": {
        "name": "销项税额合价",
        "mathFormula": `_.sum(DE_XXSE_TOTAL)`
    }
    ,
    "zzsynsePrice": {
        "name": "增值税应纳税额单价",
        "mathFormula": `quantity>0?(zzsynseTotal/quantity):0`
    },
    "zzsynseTotal": {
        "name": "增值税应纳税额合价",
        "mathFormula": `_.sum(DE_ZZSYNSE_TOTAL)`
    },
    "fjsePrice": {
        "name": "附加税费单价",
        "mathFormula": `quantity>0?(fjseTotal/quantity):0`
    },
    "fjseTotal": {
        "name": "附加税费合价",
        "mathFormula": `_.sum(DE_FJSE_TOTAL)`
    },
    "sqgczjPrice": {
        "name": "税前工程造价单价",
        "mathFormula": `quantity>0?(sqgczjTotal/quantity):0`
    },
    "sqgczjTotal": {
        "name": "税前工程造价合价",
        "mathFormula": `_.sum(DE_SQGCZJ_TOTAL)`
    }
    ,
    "fxfyPrice": {
        "name": "风险费用单价",
        "mathFormula": `quantity>0?(fxfyTotal/quantity):0`
    },
    "fxfyTotal": {
        "name": "风险费用合价",
        "mathFormula": `_.sum(DE_FXFY_TOTAL)`
    },
    "sjPrice": {
        "name": "税金单价",
        "mathFormula": `quantity>0?(sjTotal/quantity):0`
    },
    "sjTotal": {
        "name": "税金合价",
        "mathFormula": `_.sum(DE_SJ_TOTAL)`
    }
}
/*
* 清单行填充规则---------------------------end-------------------------------
* */

/*
* 单价构成计算规则---------------------------start-------------------------------
* */

const upcBaseFn = {
    //直接费合价
    "UPC_ZJF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZJF",
            "cloumn": "displayAllPrice"
        };
    }
    ,
    //利润费合价
    "UPC_LR_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_LR",
            "cloumn": "displayAllPrice"
        };
    },

    //管理费合价
    "UPC_GLF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GLF",
            "cloumn": "displayAllPrice"
        };
    },
    //工程造价合价
    "UPC_GCZJ_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GCZJ",
            "cloumn": "displayAllPrice"
        };
    },
    //生产工具使用费
    "UPC_SCGJSYF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SCGJSYF",
            "cloumn": "displayAllPrice"
        };
    },
    //繁华地段管理增加费
    "UPC_FHDDGLZJF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FHDDGLZJF",
            "cloumn": "displayAllPrice"
        };
    },
    //冬季防寒费
    "UPC_GJFHF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GJFHF",
            "cloumn": "displayAllPrice"
        };
    },
    //山地管理增加费
    "UPC_SDGLZJF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SDGLZJF",
            "cloumn": "displayAllPrice"
        };
    },
    //绿色施工安全防护措施费
    "UPC_LSSGAQFHCSF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_LSSGAQFHCSF",
            "cloumn": "displayAllPrice"
        };
    },
    //税金
    "UPC_SJ_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SJ",
            "cloumn": "displayAllPrice"
        };
    },
    //风险费用
    "UPC_FXFY_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FXFY",
            "cloumn": "displayAllPrice"
        };
    },
    //规费明细
    "UPC_GFMX_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GFMX",
            "cloumn": "displayAllPrice"
        };
    },
    //社会保障费
    "UPC_SHBZF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SHBZF",
            "cloumn": "displayAllPrice"
        };
    },
    //综合费
    "UPC_ZHF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZHF",
            "cloumn": "displayAllPrice"
        };
    },
    //人工费调整额
    "UPC_RGFTZE_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZHF",
            "cloumn": "displayAllPrice"
        };
    },
    //现场管理费
    "UPC_ZCGLF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZCGLF",
            "cloumn": "displayAllPrice"
        };
    },
    //
    //企业管理费
    "UPC_QYGLF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_QYGLF",
            "cloumn": "displayAllPrice"
        };
    },
    //财务费
    "UPC_CWF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_QYGLF",
            "cloumn": "displayAllPrice"
        };
    },
    //职工失业保险
    "UPC_ZGSYBX_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZGSYBX",
            "cloumn": "displayAllPrice"
        };
    },
    //医疗保险费
    "UPC_YLBXF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_YLBXF",
            "cloumn": "displayAllPrice"
        };
    },
    //单价措施项目费
    "UPC_DJCSXMF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_DJCSXMF",
            "cloumn": "displayAllPrice"
        };
    },
    //其他总价措施项目费
    "UPC_QTZJCSXMF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_QTZJCSXMF",
            "cloumn": "displayAllPrice"
        };
    },
    //安全文明施工费基本费
    "UPC_AQWMSGFJBF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_AQWMSGFJBF",
            "cloumn": "displayAllPrice"
        };
    },
    //安全文明施工费增加费
    "UPC_AQWMSGFZJF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_AQWMSGFZJF",
            "cloumn": "displayAllPrice"
        };
    },

    //规费合价
    "UPC_GF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GF",
            "cloumn": "displayAllPrice"
        };
    },
    //安全生产、文明施工费
    "UPC_AWF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_AWF",
            "cloumn": "displayAllPrice"
        };
    },
    //生产工具使用费合价
    "UPC_SCGJSYF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SCGJSYF",
            "cloumn": "displayAllPrice"
        };
    },
    //繁华地段管理增加费合价
    "UPC_FHDDGLZJF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FHDDGLZJF",
            "cloumn": "displayAllPrice"
        };
    },
    //冬季防寒费合价
    "UPC_GJFHF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GJFHF",
            "cloumn": "displayAllPrice"
        };
    },
    //山地管护增加费合价
    "UPC_SDGHZJF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SDGLZJF",
            "cloumn": "displayAllPrice"
        };
    },
    //绿色施工安全防护措施费合价
    "UPC_LSSGAQFHCSF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_LSSGAQFHCSF",
            "cloumn": "displayAllPrice"
        };
    },
    //进项税额合价
    "UPC_JXSE_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_JXSE",
            "cloumn": "displayAllPrice"
        };
    },
    //进项税额合价
    "UPC_FJSE_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FJSE",
            "cloumn": "displayAllPrice"
        };
    },
    "UPC_YLSSWHF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_YLSSWHF",
            "cloumn": "displayAllPrice"
        };
    },
    "UPC_LHSSWHF_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_LHSSWHF",
            "cloumn": "displayAllPrice"
        };
    },

    //销项税额合价
    "UPC_XXSE_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_XXSE",
            "cloumn": "displayAllPrice"
        };
    },
    //增值税应纳税额合价
    "UPC_ZZSYNSE_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZZSYNSE",
            "cloumn": "displayAllPrice"
        };
    },
    //税前工程造价合价
    "UPC_SQGCZJ_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SQGCZJ",
            "cloumn": "displayAllPrice"
        };
    },
    //风险费用合价
    "UPC_FXFY_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FXFY",
            "cloumn": "displayAllPrice"
        };
    },
    //税金合价
    "UPC_SJ_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SJ",
            "cloumn": "displayAllPrice"
        };
    },
    //税金合价
    "UPC_SJ_TOTAL": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SJ",
            "cloumn": "displayAllPrice"
        };
    },


};
//单价构成字段规则
const upcRules = {
    "UPC_ZJF_allPrice": {
        "name": "直接费",
        "mathFormula": `_.sum(UPC_ZJF_TOTAL)`
    },
    "UPC_GLF_allPrice": {
        "name": "管理费",
        "mathFormula": `_.sum(UPC_GLF_TOTAL)`
    },
    "UPC_LR_allPrice": {
        "name": "利润",
        "mathFormula": `_.sum(UPC_LR_TOTAL)`
    },
    "UPC_GF_allPrice": {
        "name": "规费",
        "mathFormula": `_.sum(UPC_GF_TOTAL)`
    },
    "UPC_AWF_allPrice": {
        "name": "安全生产、文明施工费",
        "mathFormula": `_.sum(UPC_AWF_TOTAL)`
    },
    "UPC_GCZJ_allPrice": {
        "name": "工程造价",
        "mathFormula": `_.sum(UPC_GCZJ_TOTAL)`
    },
    "UPC_RGF_allPrice": {
        "name": "人工费",
        "mathFormula": `_.sum(DE_RGF_TOTAL)`
    },
    "UPC_CLF_allPrice": {
        "name": "材料费",
        "mathFormula": `_.sum(DE_CLF_TOTAL)`
    },
    "UPC_JXF_allPrice": {
        "name": "机械费",
        "mathFormula": `_.sum(DE_JXF_TOTAL)`
    },
    "UPC_SBF_allPrice": {
        "name": "设备费",
        "mathFormula": `_.sum(DE_SBF_TOTAL)`
    },
    "UPC_ZCF_allPrice": {
        "name": "主材费",
        "mathFormula": `_.sum(DE_ZCF_TOTAL)`
    },
    "UPC_SCGJSYF_allPrice": {
        "name": "生产工具使用费",
        "mathFormula": `_.sum(UPC_SCGJSYF_TOTAL)`
    },
    "UPC_FHDDGLZJF_allPrice": {
        "name": "繁华地段管理增加费",
        "mathFormula": `_.sum(UPC_FHDDGLZJF_TOTAL)`
    }
    ,
    "UPC_GJFHF_allPrice": {
        "name": "冬季防寒费",
        "mathFormula": `_.sum(UPC_GJFHF_TOTAL)`
    }
    ,
    "UPC_SDGLZJF_allPrice": {
        "name": "山地管理增加费",
        "mathFormula": `_.sum(UPC_SDGLZJF_TOTAL)`
    }
    ,
    "UPC_LSSGAQFHCSF_allPrice": {
        "name": "绿色施工安全防护措施费",
        "mathFormula": `_.sum(UPC_LSSGAQFHCSF_TOTAL)`
    },
    "UPC_SJ_allPrice": {
        "name": "税金",
        "mathFormula": `_.sum(UPC_SJ_TOTAL)`
    }
    ,
    "UPC_FXFY_allPrice": {
        "name": "风险费用",
        "mathFormula": `_.sum(UPC_FXFY_TOTAL)`
    },
    "UPC_GFMX_allPrice": {
        "name": "风险费用",
        "mathFormula": `_.sum(UPC_GFMX_TOTAL)`
    },
    "UPC_SHBZF_allPrice": {
        "name": "社会保障费",
        "mathFormula": `_.sum(UPC_SHBZF_TOTAL)`
    },
    "UPC_ZHF_allPrice": {
        "name": "综合费",
        "mathFormula": `_.sum(UPC_ZHF_TOTAL)`
    }
    ,
    "UPC_RGFTZE_allPrice": {
        "name": "人工费调整额",
        "mathFormula": `_.sum(UPC_RGFTZE_TOTAL)`
    },
    "UPC_ZCGLF_allPrice": {
        "name": "现场管理费",
        "mathFormula": `_.sum(UPC_ZCGLF_TOTAL)`
    },
    "UPC_QYGLF_allPrice": {
        "name": "企业管理费",
        "mathFormula": `_.sum(UPC_QYGLF_TOTAL)`
    }
    ,
    "UPC_CWF_allPrice": {
        "name": "财务费",
        "mathFormula": `_.sum(UPC_CWF_TOTAL)`
    }
    ,
    "UPC_ZGSYBX_allPrice": {
        "name": "职工失业保险",
        "mathFormula": `_.sum(UPC_ZGSYBX_TOTAL)`
    }
    ,
    "UPC_YLBXF_allPrice": {
        "name": "医疗保险费",
        "mathFormula": `_.sum(UPC_YLBXF_TOTAL)`
    },
    "UPC_DJCSXMF_allPrice": {
        "name": "单价措施项目费",
        "mathFormula": `_.sum(UPC_DJCSXMF_TOTAL)`
    }
    ,
    "UPC_QTZJCSXMF_allPrice": {
        "name": "其他总价措施项目费",
        "mathFormula": `_.sum(UPC_QTZJCSXMF_TOTAL)`
    }
    ,
    "UPC_AQWMSGFJBF_allPrice": {
        "name": "安全文明施工费基本费",
        "mathFormula": `_.sum(UPC_AQWMSGFJBF_TOTAL)`
    }
    ,
    "UPC_AQWMSGFZJF_allPrice": {
        "name": "安全文明施工费增加费",
        "mathFormula": `_.sum(UPC_AQWMSGFZJF_TOTAL)`
    },
    "UPC_SQGCZJ_allPrice": {
        "name": "税前工程造价",
        "mathFormula": `_.sum(UPC_SQGCZJ_TOTAL)`
    },
    "UPC_YLSSWHF_allPrice": {
        "name": "园林设施维护费",
        "mathFormula": `_.sum(UPC_YLSSWHF_TOTAL)`
    },
    "UPC_LHSSWHF_allPrice": {
        "name": "亮化设施维护费",
        "mathFormula": `_.sum(UPC_LHSSWHF_TOTAL)`
    }
}
const upcRules2012 = {
    "UPC_JXSE_allPrice": {
        "name": "进项税额",
        "mathFormula": `_.sum(UPC_JXSE_TOTAL)`
    },
    "UPC_XXSE_allPrice": {
        "name": "进项税额",
        "mathFormula": `_.sum(UPC_XXSE_TOTAL)`
    },
    "UPC_ZZSYNSE_allPrice": {
        "name": "增值税应纳税额",
        "mathFormula": `_.sum(UPC_ZZSYNSE_TOTAL)`
    },
    "UPC_FJSE_allPrice": {
        "name": "附加税额",
        "mathFormula": `_.sum(UPC_FJSE_TOTAL)`
    }


}
/*
* 单价构成计算规则---------------------------end-------------------------------
* */


module.exports = {
    qdFillBaseRule: baseFn,
    qdFillRules: rules,
    qdUPCBaseRule: upcBaseFn,
    qdUPCRules: upcRules,
    qdUPCRules2012: {...upcRules, ...upcRules2012}
}

