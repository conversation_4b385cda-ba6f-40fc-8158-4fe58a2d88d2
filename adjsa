  V1.0-sprint2-dev[m
  V1.0-sprint2-dev-Frontends[m
* [32mV1.0-sprint2-dev-Frontends-merge[m
  master[m
  v1.0-sprint-dev-sc[m
  v1.0-sprint1-dev[m
  v1.0-sprint1-dev-Frontends[m
  v1.0-sprint1-dev-frontend[m
  v1.0-sprint2-dev[m
  [31mremotes/origin/HEAD[m -> origin/master
  [31mremotes/origin/V1.0-sprint2-dev[m
  [31mremotes/origin/V1.0-sprint2-dev-Frontends[m
  [31mremotes/origin/V1.0-sprint2-dev-Frontends-merge[m
  [31mremotes/origin/dev[m
  [31mremotes/origin/master[m
  [31mremotes/origin/v1.0-sprint-dev-sc[m
  [31mremotes/origin/v1.0-sprint-dev-zqf[m
  [31mremotes/origin/v1.0-sprint1-dev[m
  [31mremotes/origin/v1.0-sprint1-dev-Frontends[m
  [31mremotes/origin/v1.0-sprint1-dev-frontend[m
  [31mremotes/origin/v1.0-sprint1-dev-wxf[m
  [31mremotes/origin/v1.0-sprint2-dev[m
