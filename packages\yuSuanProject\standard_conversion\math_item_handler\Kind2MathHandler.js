const {BaseRcj2022} = require("../../model/BaseRcj2022");
const {BaseRcj} = require("../../model/BaseRcj");
const MathItemHandler = require("./mathItemHandler");
const XXXToYYYMathHandler = require("./XXXToYYYMathHandler");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. kind=2：XXXX 替换为 YYYY
 */
class Kind2MathHandler extends XXXToYYYMathHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 3;
        mathItem.fromRCJCode = this.rule.defaultRcjCode;
        mathItem.fromRCJLibraryCode = this.rule.defaultRcjLibraryCode;
        mathItem.toRCJCode = this.rule.clpb.detailsCode;
        mathItem.toRCJLibraryCode = this.rule.clpb.libraryCode;
    }
}

module.exports = Kind2MathHandler;