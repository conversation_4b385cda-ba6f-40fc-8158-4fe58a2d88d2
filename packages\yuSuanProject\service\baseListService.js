'use strict';

const {Service, Log} = require('../../../core');
const {BaseList} = require("../model/BaseList");
const QdDeParam = require("../params/QdDeParam");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {Like} = require("typeorm");
const qdChapterTree13Json = require("../jsonData/qdChapterTree_13.json");
const qdGuideChapterTree13Json = require("../jsonData/qdGuideChapterTree_13.json");

/**
 * 国标清单service
 */
class BaseListService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseListDao = this.app.appDataSource.manager.getRepository(BaseList);

    /**
     * 获取清单分类目录树
     * @param libraryCode 清单册code
     * @returns {Promise<*[]>}
     */
    async listTreeByLibraryCode(libraryCode) {
        if(ObjectUtils.isNotEmpty(qdChapterTree13Json)){
            return qdChapterTree13Json;
        }

        // 清单arrays
        let listArrays = await this.baseListDao.find({
            where: {libraryCode: libraryCode},
            order: {bdCodeLevel04: "ASC"}
        });
        // 去重取level01/02/03的list
        let level01AllList = this.distinctList(listArrays, "bdCodeLevel01").sort((a, b) => a.bdCodeLevel01 - b.bdCodeLevel01);
        let level02AllList = this.distinctList(listArrays, "bdCodeLevel02").sort((a, b) => a.bdCodeLevel02 - b.bdCodeLevel02);
        let level03AllList = this.distinctList(listArrays, "bdCodeLevel03").sort((a, b) => a.bdCodeLevel03 - b.bdCodeLevel03);
        // 给前端作为唯一标识
        let sequenceNbr = 1;
        // level01
        level01AllList.forEach(level01Item => {
            // 给前端统一code和name以及唯一标识
            this.modifyingAttributeValue(level01Item, 1, sequenceNbr++);
            let leve02SubList = level02AllList.filter(level02AllItem => level02AllItem.bdCodeLevel01 === level01Item.bdCodeLevel01);
            level01Item.childrenList = leve02SubList;
            // level02
            leve02SubList.forEach(level02Item => {
                // 给前端统一code和name以及唯一标识
                this.modifyingAttributeValue(level02Item, 2, sequenceNbr++);
                let leve03SubList = level03AllList.filter(level03AllItem => level03AllItem.bdCodeLevel02 === level02Item.bdCodeLevel02);
                level02Item.childrenList = leve03SubList;
                // level03
                leve03SubList.forEach(level03Item => {
                    // 给前端统一code和name以及唯一标识
                    this.modifyingAttributeValue(level03Item, 3, sequenceNbr++);
                    /* 23.5.20 注释清单专业树中的清单层即level04
                    // level04
                    let level04SubList = listArrays.filter(allItem => allItem.bdCodeLevel03 === level03Item.bdCodeLevel03);
                    level03Item.childrenList = level04SubList;
                    level04SubList.forEach(level04Item => {
                        // 给前端统一code和name以及唯一标识
                        this.modifyingAttributeValue(level04Item, 4, sequenceNbr++);
                    })*/
                });
            });
        });
        return level01AllList;
    }

    /**
     * 获取清单分类目录树- 4层树：bd_name_level01、bd_name_level02、bd_name_level03、bd_code_level04 bd_name_level04
     * @param params 清单code或name 模糊查询;libraryCode 清单册code
     * @returns {Promise<{baseList,childrenList}>}
     */
    async listQdTree(params) {

        // qdCodeOrName 清单code或name 模糊查询; libraryCode 清单册code
        let {qdCodeOrName, libraryCode} = params;

        if(ObjectUtils.isEmpty(qdCodeOrName) && ObjectUtils.isNotEmpty(qdGuideChapterTree13Json)){
            return qdGuideChapterTree13Json;
        }

        // sql查询条件，默认按清单册code查
        let sqlWhere = {libraryCode: libraryCode}
        if (ObjectUtils.isNotEmpty(qdCodeOrName)) {
            // 清单code或name不为空，则根据清单code或name模糊查询
            // 根据能否转为纯数字来判断是不是清单code
            if (/^\d+$/.test(qdCodeOrName)) {
                // code
                sqlWhere.bdCodeLevel04 = Like("%" + qdCodeOrName + "%");
            } else {
                // name
                sqlWhere.bdNameLevel04 = Like("%" + qdCodeOrName + "%");
            }
        }

        // 清单arrays
        let listArrays = await this.baseListDao.find({where: sqlWhere, order: {bdCodeLevel04: "ASC"}});

        // 24.3.15 根据清单编码或清单名称查询时，跟广联达统一，直接返回清单array，不做层级化
        if (ObjectUtils.isNotEmpty(qdCodeOrName)) {
            return listArrays;
        }

        // 去重取level01/02/03的list
        let level01AllList = this.distinctList(listArrays, "bdCodeLevel01").sort((a, b) => a.bdCodeLevel01 - b.bdCodeLevel01);
        let level02AllList = this.distinctList(listArrays, "bdCodeLevel02").sort((a, b) => a.bdCodeLevel02 - b.bdCodeLevel02);
        let level03AllList = this.distinctList(listArrays, "bdCodeLevel03").sort((a, b) => a.bdCodeLevel03 - b.bdCodeLevel03);
        // 给前端作为唯一标识
        let sequenceNbr = 1;
        // level01
        level01AllList.forEach(level01Item => {
            // 给前端统一code和name以及唯一标识
            this.modifyingAttributeValue(level01Item, 1, sequenceNbr++);
            let leve02SubList = level02AllList.filter(level02AllItem => level02AllItem.bdCodeLevel01 === level01Item.bdCodeLevel01);
            level01Item.childrenList = leve02SubList;
            // level02
            leve02SubList.forEach(level02Item => {
                // 给前端统一code和name以及唯一标识
                this.modifyingAttributeValue(level02Item, 2, sequenceNbr++);
                let leve03SubList = level03AllList.filter(level03AllItem => level03AllItem.bdCodeLevel02 === level02Item.bdCodeLevel02);
                level02Item.childrenList = leve03SubList;
                // level03
                leve03SubList.forEach(level03Item => {
                    // 给前端统一code和name以及唯一标识
                    this.modifyingAttributeValue(level03Item, 3, sequenceNbr++);
                    // level04
                    let level04SubList = listArrays.filter(allItem => allItem.bdCodeLevel03 === level03Item.bdCodeLevel03);
                    level03Item.childrenList = level04SubList;
                    level04SubList.forEach(level04Item => {
                        // 给前端统一code和name // 第4层即清单层 不需要重置seq，因为它本身就是唯一的清单id
                        this.modifyingAttributeValue(level04Item, 4);
                    })
                });
            });
        });
        return level01AllList;
    }

    /**
     * 修改属性值
     * @param baseList 清单
     * @param level 清单专业level
     * @param sequenceNbr 重置后的sequenceNbr
     */
    modifyingAttributeValue(baseList, level, sequenceNbr) {
        /*
         * 1.赋值bdCode和bdName
         * 2.置空level下有误的属性值
         * 3.重置sequenceNbr
         */
        if (ObjectUtils.isNotEmpty(sequenceNbr)) {
            baseList.sequenceNbr = sequenceNbr;
        }
        switch (level) {
            case 1:
                // level01
                baseList.bdCode = baseList.bdCodeLevel01;
                baseList.bdName = baseList.bdNameLevel01;
                baseList.bdCodeLevel02 = null;
                baseList.bdNameLevel02 = null;
                baseList.bdCodeLevel03 = null;
                baseList.bdNameLevel03 = null;
                baseList.bdCodeLevel04 = null;
                baseList.bdNameLevel04 = null;
                baseList.unit = null;
                break;
            case 2:
                // level02
                baseList.bdCode = baseList.bdCodeLevel02;
                baseList.bdName = baseList.bdNameLevel02;
                baseList.bdCodeLevel03 = null;
                baseList.bdNameLevel03 = null;
                baseList.bdCodeLevel04 = null;
                baseList.bdNameLevel04 = null;
                baseList.unit = null;
                break;
            case 3:
                // level03
                baseList.bdCode = baseList.bdCodeLevel03;
                baseList.bdName = baseList.bdNameLevel03;
                baseList.bdCodeLevel04 = null;
                baseList.bdNameLevel04 = null;
                baseList.unit = null;
                break;
            case 4:
                // level04
                baseList.bdCode = baseList.bdCodeLevel04;
                baseList.bdName = baseList.bdNameLevel04;
                break;
            default:
                // ...
                break;
        }
    }

    /**
     * list去重
     * @param list 要分组的list
     * @param distinctColumn 分组字段名str
     * @returns {*[]} 新的list
     */
    distinctList(list, distinctColumn) {
        if (!Array.isArray(list)) {
            return [];
        }
        let groupArray = [];
        list.forEach(item => {
            // 是否存在
            let isExist = groupArray.some(g => g[distinctColumn] === item[distinctColumn]);
            // 不存在add
            if (!isExist) {
                groupArray.push(Object.assign({}, item));
            }
        })
        return groupArray;
    }

    /**
     * 模糊查清单
     * @param qdDeParam 清单编码、名称、分类名
     * @see QdDeParam
     * @returns {Promise<ObjectLiteral[]>}
     */
    async queryListByBdCodeAndName(qdDeParam) {

        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 清单册
        if (qdDeParam.libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += "baseList.libraryCode = :libraryCode"
            whereParams.libraryCode = qdDeParam.libraryCode;
        }
        // 清单名称
        if (qdDeParam.bdName) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "( baseList.bdNameLevel04 like :bdName or " +
                "baseList.bdCodeLevel04 like :bdCode )"
            whereParams.bdName = "%" + qdDeParam.bdName + "%";
            whereParams.bdCode = "%" + qdDeParam.bdName + "%";
        }
        // 一级分类
        if (qdDeParam.classifyLevel1) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseList.bdNameLevel01 = :bdNameLevel01"
            whereParams.bdNameLevel01 = qdDeParam.classifyLevel1;
        }
        // 二级分类
        if (qdDeParam.classifyLevel2) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseList.bdNameLevel02 = :bdNameLevel02"
            whereParams.bdNameLevel02 = qdDeParam.classifyLevel2;
        }
        // 三级分类
        if (qdDeParam.classifyLevel3) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseList.bdNameLevel03 = :bdNameLevel03"
            whereParams.bdNameLevel03 = qdDeParam.classifyLevel3;
        }
        if (!whereSql) {
            console.log("error,参数为空");
        }

        let baseLists = await this.baseListDao
            .createQueryBuilder("baseList")
            .where(whereSql, whereParams)
            .orderBy("baseList.bdCodeLevel04", "ASC")
            .getMany();

        // set bdCode,bdName,unitList 与BS端保持一致
        baseLists.forEach(i => {
            i.bdCode = i.bdCodeLevel04;
            i.bdName = i.bdNameLevel04;
            i.unitList = i.unit;
        });
        return baseLists;
    }

    /**
     * 定位清单
     * @param sequenceNbr 清单主键
     * @returns {Promise<*>}
     */
    async queryQdById(sequenceNbr) {
        return await this.baseListDao.findOneBy({sequenceNbr: sequenceNbr});
    }

    /**
     * selectOne
     * @param sequenceNbr sequenceNbr
     * @return {Promise<BaseList>}
     */
    async selectOne(sequenceNbr) {
        return await this.baseListDao.findOneBy({sequenceNbr: sequenceNbr});
    }





    /**
     * 根据清单编码查询前9位
     * @returns {Promise<*>}
     */
    async queryQdByCode(code) {
        if (ObjectUtils.isEmpty(code)){
            return code;
        }
        code = code.slice(0, 9);

        return await this.baseListDao.findOneBy({bdCodeLevel04: code});
    }


    async queryQd(qdParam) {

        if (qdParam.bdCodeLevel01) {
            return await this.baseListDao.findOneBy({bdCodeLevel01: qdParam.bdCodeLevel01});
        }
        if (qdParam.bdCodeLevel02) {
            return await this.baseListDao.findOneBy({bdCodeLevel02: qdParam.bdCodeLevel02});
        }
        if (qdParam.bdCodeLevel03) {
            return await this.baseListDao.findOneBy({bdCodeLevel03: qdParam.bdCodeLevel03});
        }

        return  null;
    }


}

BaseListService.toString = () => '[class BaseListService]';
module.exports = BaseListService;
