const {UPCTemplate} = require("../../model/UPCTemplate");
const {BaseFeeFile2022, BaseFeeFile} = require("../../model/BaseFeeFile");
const _ = require("lodash");
const os = require("os");
const {writeFileSync, readFileSync, existsSync, mkdirSync} = require("fs");
const path = require('path');
/**
 * 单价构成系统模板
 * 单价构成系统模板下拉列表
 */
class UPCContext {
    constructor() {

        this.systemTtemplateListMap = new Map();//系统单价构成系统模板列表 全局使用不可更改
        this.imitationTemplateListMap = new Map();//仿制单价构成模板列表
        this.feeFileList = [];//取费文件列表
        this.feeFileMap = new Map();//单价构成 仿制模板拉列表
        this.qfCodeMap = new Map();//增量取费文件 qfCOde 和系统qfCOde 映射
    }

    load({qfCodeMap, feeFileMap, incrementTemplateListMap}) {
        for (const qfCodeMapKey in qfCodeMap) {
            this.qfCodeMap.set(qfCodeMapKey, qfCodeMap[qfCodeMapKey])
        }
        for (const feeFileMapKey in feeFileMap) {
            this.feeFileMap.set(feeFileMapKey, new Map(Object.entries(feeFileMap[feeFileMapKey])));
        }
        for (const incrementTemplateListMapKey in incrementTemplateListMap) {
            this.imitationTemplateListMap.set(incrementTemplateListMapKey, new Map(Object.entries(incrementTemplateListMap[incrementTemplateListMapKey])))
        }
    }

    getAllData() {
        let obj = {};
        for (let [key, value] of this.feeFileMap.entries()) {
            obj[key] = Object.fromEntries(value);
        }
        let obj1 = {};
        for (let [key, value] of this.imitationTemplateListMap.entries()) {
            obj1[key] = Object.fromEntries(value);
        }
        return {qfCodeMap: Object.fromEntries(this.qfCodeMap), feeFileMap: obj, incrementTemplateListMap: obj1};
    }

    async init(defaultDataSource) {
        let templateList = await defaultDataSource.getRepository(UPCTemplate).find();
        let groupTemplateList = _.groupBy(templateList, "standard");
        for (const groupTemplateListKey in groupTemplateList) {
            let qfCodeGroupTemplateList = _.groupBy(groupTemplateList[groupTemplateListKey], "qfCode");
            let groupList = new Map();
            for (const qfCodeGroupTemplateListKey in qfCodeGroupTemplateList) {
                groupList.set(qfCodeGroupTemplateListKey, qfCodeGroupTemplateList[qfCodeGroupTemplateListKey]);
            }
            this.systemTtemplateListMap.set(groupTemplateListKey, groupList);
        }
        let baseFeeFile = await defaultDataSource.getRepository(BaseFeeFile2022).find({
            where: {delFlag: 0},
            order: {sortNo: "ASC"}
        });
        let baseFeeFile12 = await defaultDataSource.getRepository(BaseFeeFile).find({
            where: {delFlag: 0},
            order: {sortNo: "ASC"}
        });
        this.addFeeFileData(baseFeeFile, "22");

        this.addFeeFileData(baseFeeFile12, "12");
        this.checkAndWriteSystemFile();
    }

    /**
     *
     */
    async checkAndWriteSystemFile(force = false) {
        let pathss = {};

        ["", "12", "22"].forEach(path => {
            let defaultStoragePath = `${os.homedir()}\\.xilidata\\单价构成模板\\${path}`;
            if (!existsSync(defaultStoragePath)) {
                mkdirSync(defaultStoragePath);
            } else {
                force == true;
            }
            pathss[path || "default"] = defaultStoragePath;

        });
        if (force) return;
        let t12 = this.getFeeFileData("12");
        let templateMap = new Map();
        let temp12Map = this.systemTtemplateListMap.get("12");
        t12.forEach(item => {
            let value = temp12Map.get(item.qfCode);
            if (value) {
                value = value.map(item => {
                    const {sequenceNbr, ...rest} = item;
                    return rest;
                });
                templateMap.set(item.qfName + "_12", value);
            }

        });
        let t22 = this.getFeeFileData("22");
        let temp22Map = this.systemTtemplateListMap.get("22");
        t22.forEach(item => {
            let value = temp22Map.get(item.qfCode);
            if (value) {
                value = value.map(item => {
                    const {sequenceNbr, ...rest} = item;
                    return rest;
                });
                templateMap.set(item.qfName + "_22", value);
            }

        });
        for (let [key, value] of templateMap.entries()) {
            let s = key.split("_");
            const filePath = path.join(pathss[s[1]], `${s[0]}.djgc`);
            writeFileSync(filePath, JSON.stringify(value), 'utf-8');
        }
        //writeFileSync
    }

    //添加取费文件
    addFeeFileData(feeFileList, type) {
        feeFileList.forEach(item => {
            this.feeFileList.push({...item, type});
        });
    }

    //获取取费文件列表
    getFeeFileData(type) {
        return this.feeFileList.filter(item => item.type == type);
    }

    //获取单价构下拉列表
    getUpcTemplateSelectList(is2022, constructId, unitId) {
        //先获取到系统的
        let list = this.getFeeFileData(is2022 ? "22" : "12");
        //再拿到仿制的
        let pMap = this.getfeeFilebyPath(constructId + "," + unitId);
        if (pMap) {
            list = list.concat([...pMap.values()]);
        }
        return list;
    }

    /**
     * 获取单价构成系统模板
     * @param qfcode
     * @param type
     * @returns {*}
     */
    getSystemTemplateList(qfcode, type) {
        return this.systemTtemplateListMap.get(type).get(qfcode);
    }

    //获取仿制模板
    getImitationTemplateList(constructId, unitId, qfCode) {
        let pMap = this.getTemplateListbyPath(constructId, unitId);
        return pMap.get(qfCode);
    }

    /**
     * 仿制 单价构成模板下拉列表
     * @param path
     * @returns {Map<any, any>|any}
     */
    getfeeFilebyPath(path) {
        if (this.feeFileMap.has(path)) {
            return this.feeFileMap.get(path);
        }
        let map = new Map();
        this.feeFileMap.set(path, map);
        return map;
    }

    /**
     * 获取增量模板 map 只到单位级别
     * @param path
     * @returns {Map<any, any>|any}
     */
    getTemplateListbyPath(constructId, unitId) {
        let path = constructId + "," + unitId;
        if (this.imitationTemplateListMap.has(path)) {
            return this.imitationTemplateListMap.get(path);
        }
        let map = new Map();
        this.imitationTemplateListMap.set(path, map);
        return map;
    }


}

module.exports = {UPCContext: new UPCContext()}
