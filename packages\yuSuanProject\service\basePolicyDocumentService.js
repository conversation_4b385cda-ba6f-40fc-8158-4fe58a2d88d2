'use strict';

const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const { Service,Log } = require('../../../core');

/**
 * 示例服务
 * @class
 */
class BasePolicyDocumentService extends Service {

    constructor(ctx) {
        super(ctx);
    }


    /**
     * 查询人工费
     * @param arg
     * @return {Promise<void>}
     */
    async queryBySequenceNbr(sequenceNbr) {
        let result = await this.app.appDataSource.getRepository(BasePolicyDocument).findOne({
            where: {
                sequenceNbr: sequenceNbr
            }
        });
        return result;
    }

}

BasePolicyDocumentService.toString = () => '[class BasePolicyDocumentService]';
module.exports = BasePolicyDocumentService;
