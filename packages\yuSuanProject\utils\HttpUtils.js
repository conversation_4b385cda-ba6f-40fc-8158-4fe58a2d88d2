const axios = require("axios");

/**
 * http请求
 */
class HttpUtils{


    /**
     * get请求
     * @param url 请求地址
     * @param headers 请求头
     * @param params 参数
     * @return {Promise<*>}
     */
    async GET(url,headers,params){
        try {
            const response = await axios.get(url,{
                params: params,
                headers:headers
            });
            if (response.status === 200){
                return response.data;
            }
        } catch (error) {
            console.error(error);
            throw error;
        }
    }


    async POST(url,data){
        try {
            const response = await axios.post(url,data);
            if (response.status === 200){
                return response.data;
            }
        } catch (error) {
            console.error("网络异常");
        }
    }

    /**
     * 加密狗专用
     * @param url
     * @param data
     * @return {Promise<*>}
     * @constructor
     */
    async DONGLE_POST(url,data){
        try {
            const response = await axios.post(url,data);
            if (response.status === 200){
                return response.data;
            }
        } catch (error) {
            console.error("未检测到加密锁驱动");
        }
    }
}

module.exports = {
    HttpUtils: new HttpUtils()
};

