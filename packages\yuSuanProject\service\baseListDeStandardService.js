'use strict';

const {ObjectUtils} = require("../utils/ObjectUtils");
const { Service,Log } = require('../../../core');
const {BaseArea} = require("../model/BaseArea");
const {BaseListDeStandard} = require("../model/BaseListDeStandard");
const {getRepository  } =require('typeorm');


/**
 * 示例服务
 * @class
 */
class BaseListDeStandardService extends Service {

    constructor(ctx) {
        super(ctx);
        this._baseListDeStandardDao = this.app.appDataSource.manager.getRepository(BaseListDeStandard);
    }


    /**
     * 清单标准下拉列表
     * @param arg
     * @return {Promise<void>}
     */
    async listStandardDropdownList(arg){

        let result =await this.app.appDataSource.getRepository(BaseListDeStandard).find({
            where:{
                type:arg.type,
                areaId:arg.areaId
            }
        });
        return result;
    }

    /**
     * 定额标准列表
     * @param arg
     * @return {Promise<void>}
     */
    async quotaStandardDropdownList(arg){
        let result= await this.app.appDataSource.getRepository(BaseListDeStandard).find({
            where:{
                type:arg.type,
                areaId:arg.areaId
            }
        });

        // 根据rereleaseYear（标准发布年份） 排序
        result.sort((a,b) => b.releaseYear - a.releaseYear);

        return result;
    }

    /**
     * 定额标准
     * @param sequenceNbr 定额标准Id
     * @return {Promise<void>}
     */
    async quotaStandardById(sequenceNbr){
        let result= await this.app.appDataSource.getRepository(BaseListDeStandard).find({
            where:{
                sequenceNbr: sequenceNbr
            }
        });

        return ObjectUtils.isEmpty(result) ? null : result[0];
    }

    /**
     * 主键sequenceNbr查询
     * @param sequenceNbr
     * @return {BaseListDeStandard|Error}
     */
    async selectOne(sequenceNbr) {
        if (ObjectUtils.isEmpty(sequenceNbr)) {
            throw new Error("必传参数清单/定额标准id为空");
        }
        return await this._baseListDeStandardDao.findOneBy({sequenceNbr: sequenceNbr});
    }

}

BaseListDeStandardService.toString = () => '[class BaseListDeStandardService]';
module.exports = BaseListDeStandardService;
