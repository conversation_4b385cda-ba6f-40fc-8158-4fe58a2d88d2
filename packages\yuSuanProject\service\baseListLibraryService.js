'use strict';

const { Service,Log } = require('../../../core');
const {BaseListLibrary} = require("../model/BaseListLibrary");

/**
 * 示例服务
 * @class
 */
class BaseListLibraryService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseListLibraryDao = this.app.appDataSource.manager.getRepository(BaseListLibrary);

    /**
     * 根据清单标准查清单册
     * @param qdStandardId
     * @returns {Promise<Error|BaseListLibrary[]>}
     */
    async listByQdStandard(qdStandardId) {
        if (null == qdStandardId) {
            throw new Error("必传参数定额标准为空");
        }

        return await this.baseListLibraryDao.findBy({qdStandardId: qdStandardId});
    }

}

BaseListLibraryService.toString = () => '[class BaseListLibraryService]';
module.exports = BaseListLibraryService;
