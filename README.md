# pricing-cs

pricing-cs
```
+---electron
|   +---addon
|   |   +---autoUpdater
|   |   |       index.js
|   |   |       
|   |   +---awaken
|   |   |       index.js
|   |   |       
|   |   +---chromeExtension
|   |   |       index.js
|   |   |       
|   |   +---example
|   |   |       index.js
|   |   |       
|   |   +---javaServer
|   |   |       index.js
|   |   |       ps.js
|   |   |       server.js
|   |   |       
|   |   +---security
|   |   |       index.js
|   |   |       
|   |   \---tray
|   |           index.js
|   |           
|   +---config
|   |       config.default.js
|   |       config.local.js
|   |       config.prod.js
|   |       encrypt.js
|   |       nodemon.json
|   |       
|   +---controller
|   |   |   analyzingXMLController.js
|   |   |   baseAreaController.js
|   |   |   baseClpbController.js
|   |   |   baseDeController.js
|   |   |   baseDeLibraryController.js
|   |   |   baseDeRcjRelationController.js
|   |   |   baseDeRuleRelationController.js
|   |   |   baseFeeFileController.js
|   |   |   baseJxpbController.js
|   |   |   baseListController.js
|   |   |   baseListDeStandardController.js
|   |   |   baseListLibraryController.js
|   |   |   basePolicyDocumentController.js
|   |   |   baseRateController.js
|   |   |   baseRcjClassLevelController.js
|   |   |   baseRuleDetailsController.js
|   |   |   baseRuleFileDetailsController.js
|   |   |   baseTaxReformFileController.js
|   |   |   baseUnitProjectTypeController.js
|   |   |   branchProjectController.js
|   |   |   commonController.js
|   |   |   constructConfigController.js
|   |   |   constructCostMathController.js
|   |   |   constructProjectController.js
|   |   |   constructProjectRcjController.js
|   |   |   constructTaxReformFileController.js
|   |   |   controlBoardController.js
|   |   |   conversionDeController.js
              标准换算入口
|   |   |   conversionInfoController.js
|   |   |   demo.js
|   |   |   deRelatedDescriptionController.js
|   |   |   dongleController.js
|   |   |   example.js
|   |   |   gfeeController.js
|   |   |   itemBillProjectController.js
|   |   |   jsonToXmlController.js
|   |   |   listFeatureController.js
|   |   |   measureProjectTableController.js
|   |   |   oauthClientDetailsController.js
|   |   |   organizationInstructionsController.js
|   |   |   otherProjectController.js
|   |   |   otherProjectDayWorkController.js
|   |   |   otherProjectProvisionalController.js
|   |   |   otherProjectServiceCostController.js
|   |   |   otherProjectZgjController.js
|   |   |   projectOverviewController.js
|   |   |   qdDeIndexController.js
|   |   |   qdRelatedDescriptionController.js
|   |   |   quantitiesController.js
|   |   |   rcjController.js
|   |   |   rcjDeRelationController.js
|   |   |   safeFeeController.js
|   |   |   singleProjectController.js
|   |   |   stepItemCostController.js
|   |   |   supplementController.js
|   |   |   sysDisctionaryController.js
|   |   |   sysUserController.js
|   |   |   unitCostCodePriceController.js
|   |   |   unitCostSummaryController.js
|   |   |   unitProjectController.js
|   |   |   
|   |   \---export
|   |           JieSuanExportQueryController.js
|   |           
|   +---enum
|   |       AdjacentRoadsNumEnum.js
|   |       BranchProjectDisplayConstant.js
|   |       BranchProjectLevelConstant.js
|   |       BranchProjectOptionMenuConstant.js
|   |       BranchProjectTypeConstant.js
|   |       BsRemoteUrl.js
|   |       ConstantUtil.js
|   |       ConstructBiddingTypeConstant.js
|   |       ConstructionMeasureTypeConstant.js
|   |       ConversionSourceEnum.js
|   |       CostCodeTypeEnum.js
|   |       DePropertyTypeConstant.js
|   |       DongleRemoteUrl.js
|   |       EngineeringLocationEnum.js
|   |       EngineeringTypeEnum.js
|   |       ExcelEnum.js
|   |       ExportSheetNameEnum.js
|   |       FileCheckFormatResultEnum.js
|   |       FloorSpaceEnum.js
|   |       GroundTypeConstant.js
|   |       GsjRateKindEnum.js
|   |       MenuBarEnum.js
|   |       MunicipalEngineeringCostEnum.js
|   |       OtherProjectCalculationBaseConstant.js
|   |       OtherProjectDayWorkRcjConstant.js
|   |       OvergroundEnum.js
|   |       PolicyDocumentTypeEnum.js
|   |       PrecastRateEnum.js
|   |       ProjectLevelConstant.js
|   |       RcjLevelMarkConstant.js
|   |       RcjMathEnum.js
|   |       TypConstant.js
|   |       SecondSpecialityEnum.js
|   |       StepItemCostLevelConstant.js
|   |       TaxCalculationMethodEnum.js
|   |       TaxPayingRegionEnum.js
|   |       UnitConversion.js
|   |       
|   +---jobs
|   |   \---example
|   |           hello.js
|   |           softdog.js
|   |           timer.js
|   |           
|   |       
|   +---model
|   |       BaseAnwenRate.js
|   |       BaseAnwenRate.js.map
|   |       BaseAnwenRate.ts
|   |       BaseAnZhuangRate.js
|   |       BaseAnZhuangRate.js.map
|   |       BaseAnZhuangRate.ts
|   |       BaseArea.js
|   |       BaseArea.js.map
|   |       BaseArea.ts
|   |       BaseClpb.js
|   |       BaseClpb.js.map
|   |       BaseClpb.ts
|   |       BaseDe.js
|   |       BaseDe.js.map
|   |       BaseDe.ts
|   |       BaseDeAwfRelation.js
|   |       BaseDeAwfRelation.js.map
|   |       BaseDeAwfRelation.ts
|   |       BaseDeCslbRelation.js
|   |       BaseDeCslbRelation.js.map
|   |       BaseDeCslbRelation.ts
|   |       BaseDeGtfMajorRelation.js
|   |       BaseDeGtfMajorRelation.js.map
|   |       BaseDeGtfMajorRelation.ts
|   |       BaseDeJobContent.js
|   |       BaseDeJobContent.js.map
|   |       BaseDeJobContent.ts
|   |       BaseDeLibrary.js
|   |       BaseDeLibrary.js.map
|   |       BaseDeLibrary.ts
|   |       BaseDeRcjRelation.js
|   |       BaseDeRuleRelation.js
|   |       BaseDeRuleRelation.js.map
|   |       BaseDeRuleRelation.ts
|   |       BaseDeZsCgRelation.js
|   |       BaseDeZsCgRelation.js.map
|   |       BaseDeZsCgRelation.ts
|   |       BaseFeeFile.js
|   |       BaseFeeFile.js.map
|   |       BaseFeeFile.ts
|   |       BaseFeeFileRelation.js
|   |       BaseFeeFileRelation.js.map
|   |       BaseFeeFileRelation.ts
|   |       BaseGsjRate.js
|   |       BaseGsjRate.js.map
|   |       BaseGsjRate.ts
|   |       BaseJxpb.js
|   |       BaseJxpb.js.map
|   |       BaseJxpb.ts
|   |       BaseList.js
|   |       BaseList.js.map
|   |       BaseList.ts
|   |       BaseListCalcRule.js
|   |       BaseListCalcRule.js.map
|   |       BaseListCalcRule.ts
|   |       BaseListDeStandard.js
|   |       BaseListDeStandard.js.map
|   |       BaseListDeStandard.ts
|   |       BaseListFeature.js
|   |       BaseListFeature.js.map
|   |       BaseListFeature.ts
|   |       BaseListJobContent.js
|   |       BaseListJobContent.js.map
|   |       BaseListJobContent.ts
|   |       BaseListLibrary.js
|   |       BaseListLibrary.js.map
|   |       BaseListLibrary.ts
|   |       BaseManageRate.js
|   |       BaseManageRate.js.map
|   |       BaseManageRate.ts
|   |       BaseModel.js
|   |       BaseModel.js.map
|   |       BaseModel.ts
|   |       BasePolicyDocument.js
|   |       BasePolicyDocument.js.map
|   |       BasePolicyDocument.ts
|   |       BaseRcj.js
|   |       BaseRcj.js.map
|   |       BaseRcj.ts
|   |       BaseRuleDetails.js
|   |       BaseRuleDetails.js.map
|   |       BaseRuleDetails.ts
|   |       BaseRuleFileDetails.js
|   |       BaseRuleFileDetails.js.map
|   |       BaseRuleFileDetails.ts
|   |       BaseTaxReformDocuments.js
|   |       BaseTaxReformDocuments.js.map
|   |       BaseTaxReformDocuments.ts
|   |       BaseUnitProjectType.js
|   |       BaseUnitProjectType.js.map
|   |       BaseUnitProjectType.ts
|   |       ConstructProject.js
|   |       ConstructProject.js.map
|   |       ConstructProject.ts
|   |       ConstructProjectFile.js
|   |       ConstructProjectFile.js.map
|   |       ConstructProjectFile.ts
|   |       JieSuanConstructProjectRcj.js
|   |       JieSuanConstructProjectRcj.js.map
|   |       JieSuanConstructProjectRcj.ts
|   |       ConversionDeRuleDetails.js
|   |       ConversionDeRuleDetails.js.map
|   |       ConversionDeRuleDetails.ts
|   |       ConversionInfo.js
|   |       ConversionInfo.js.map
|   |       ConversionInfo.ts
|   |       ConversionRuleOperationRecord.js
|   |       ConversionRuleOperationRecord.js.map
|   |       ConversionRuleOperationRecord.ts
|   |       CostAnalysisSingleVO.js
|   |       CostAnalysisSingleVO.js.map
|   |       CostAnalysisSingleVO.ts
|   |       CostAnalysisUnitVO.js
|   |       CostAnalysisUnitVO.js.map
|   |       CostAnalysisUnitVO.ts
|   |       CostAnalysisVO.js
|   |       CostAnalysisVO.js.map
|   |       CostAnalysisVO.ts
|   |       FeeCollectionVO.js
|   |       FeeCollectionVO.js.map
|   |       FeeCollectionVO.ts
|   |       FileLevelTreeNode.js
|   |       FileLevelTreeNode.js.map
|   |       FileLevelTreeNode.ts
|   |       Gfee.js
|   |       Gfee.js.map
|   |       Gfee.ts
|   |       index.js
|   |       index.js.map
|   |       index.ts
|   |       ItemBillProject.js
|   |       ItemBillProject.js.map
|   |       ItemBillProject.ts
|   |       ListDescribe.js
|   |       ListDescribe.js.map
|   |       ListDescribe.ts
|   |       ListFeature.js
|   |       ListFeature.js.map
|   |       ListFeature.ts
|   |       ListFeatureOperatingRecord.js
|   |       ListFeatureOperatingRecord.js.map
|   |       ListFeatureOperatingRecord.ts
|   |       MeasureProjectTable.js
|   |       MeasureProjectTable.js.map
|   |       MeasureProjectTable.ts
|   |       OrganizationInstructions.js
|   |       OrganizationInstructions.js.map
|   |       OrganizationInstructions.ts
|   |       OtherProject.js
|   |       OtherProject.js.map
|   |       OtherProject.ts
|   |       OtherProjectDayWork.js
|   |       OtherProjectDayWork.js.map
|   |       OtherProjectDayWork.ts
|   |       OtherProjectJgclSb.js
|   |       OtherProjectJgclSb.js.map
|   |       OtherProjectJgclSb.ts
|   |       OtherProjectProvisional.js
|   |       OtherProjectProvisional.js.map
|   |       OtherProjectProvisional.ts
|   |       OtherProjectServiceCost.js
|   |       OtherProjectServiceCost.js.map
|   |       OtherProjectServiceCost.ts
|   |       OtherProjectZgj.js
|   |       OtherProjectZgj.js.map
|   |       OtherProjectZgj.ts
|   |       OtherProjectZyclSb.js
|   |       OtherProjectZyclSb.js.map
|   |       OtherProjectZyclSb.ts
|   |       OtherProjectZygcZgj.js
|   |       OtherProjectZygcZgj.js.map
|   |       OtherProjectZygcZgj.ts
|   |       ProjectOverview.js
|   |       ProjectOverview.js.map
|   |       ProjectOverview.ts
|   |       ProjectTaxCalculation.js
|   |       ProjectTaxCalculation.js.map
|   |       ProjectTaxCalculation.ts
|   |       ProjectTaxCalculationVO.js
|   |       ProjectTaxCalculationVO.js.map
|   |       ProjectTaxCalculationVO.ts
|   |       RcjDetails.js
|   |       RcjDetails.js.map
|   |       RcjDetails.ts
|   |       SafeFee.js
|   |       SafeFee.js.map
|   |       SafeFee.ts
|   |       SingleProject.js
|   |       SingleProject.js.map
|   |       SingleProject.ts
|   |       SysDisctionary.js
|   |       SysDisctionary.js.map
|   |       SysDisctionary.ts
|   |       TreeList.js
|   |       TreeList.js.map
|   |       TreeList.ts
|   |       UnitCostCodePrice.js
|   |       UnitCostCodePrice.js.map
|   |       UnitCostCodePrice.ts
|   |       UnitCostSummary.js
|   |       UnitCostSummary.js.map
|   |       UnitCostSummary.ts
|   |       UnitFeeBuild.js
|   |       UnitFeeBuild.js.map
|   |       UnitFeeBuild.ts
|   |       UnitFeeDescription.js
|   |       UnitFeeDescription.js.map
|   |       UnitFeeDescription.ts
|   |       UnitFeeFile.js
|   |       UnitFeeFile.js.map
|   |       UnitFeeFile.ts
|   |       UnitProject.js
|   |       UnitProject.js.map
|   |       UnitProject.ts
|   |       
|   +---params
|   |       QdDeParam.js
|   |       
|   +---preload
|   |       bridge.js
|   |       index.js
|   |       
|   +---service
|   |   |   analyzingExcelService.js
|   |   |   analyzingXMLService.js
|   |   |   analyzingXMLServiceEZJC.js
|   |   |   analyzingXMLServiceHZB.js
|   |   |   analyzingXMLServiceJZBYZB.js
|   |   |   analyzingXMLServiceYCG.js
|   |   |   analyzingXMLServiceZBT.js
|   |   |   analyzingXMLServiceZCJB.js
|   |   |   autoCostMathService.js
|   |   |   azCostMathService.js
|   |   |   baseAnwenRateService.js
|   |   |   baseAreaService.js
|   |   |   baseBranchProjectOptionService.js
                分部分项 措施项目 操作区域元素操作基础类
                属于基础类，类比java的 抽象base类
                主要实现方法包括 
                    元素新增            insertLine
                    修改               updateByList
                    删除 批量删除        removeLine / removeLineBlock
                    从索引界面添加/替换元素    
                                      updateFromIndexPage/replaceFronIndexPage
                    查询               pageSearch
                    
                ItemBillProjectOptionService    分部分项服务
                stepItemCostService             措施项目服务
                以上两个服务类的方法继承自baseBranchProjectOptionService
|   |   |   baseDeLibraryService.js
|   |   |   baseDeRuleRelationService.js
|   |   |   baseDeService.js
|   |   |   baseFeeFileRelationService.js
|   |   |   baseFeeFileService.js
|   |   |   baseGsjRateService.js
|   |   |   baseListDeStandardService.js
|   |   |   baseListFeatureService.js
|   |   |   baseListLibraryService.js
|   |   |   baseListService.js
|   |   |   BaseManageRateService.js
|   |   |   basePolicyDocumentService.js
|   |   |   baseRateService.js
|   |   |   baseRcjClassLevelService.js
|   |   |   baseRuleDetailsService.js
|   |   |   baseRuleFileDetailsService.js
|   |   |   baseTaxReformFileService.js
|   |   |   baseUnitProjectTypeService.js
|   |   |   branchProjectService.js
|   |   |   chapterRelevantDataService.js
|   |   |   commonService.js
|   |   |   constructConfigService.js
|   |   |   constructCostMathService.js
|   |   |   constructProjectService.js
|   |   |   constructTaxReformFileService.js
|   |   |   conversionDeProcess.js
|   |   |   conversionDeService.js
               //batchOperationalConversionRule 标准换算核心批量
               //conversionRule 核心
               
|   |   |   conversionInfoService.js
|   |   |   conversionRuleOperationRecordService.js
|   |   |   demo.js
|   |   |   example.js
|   |   |   gfeeService.js
|   |   |   jsonToXmlService.js
|   |   |   listFeatureProcess.js
|   |   |   listFeatureService.js
|   |   |   oauthClientDetailsService.js
|   |   |   organizationInstructionsService.js
|   |   |   otherProjectDayWorkService.js
|   |   |   otherProjectProvisionalService.js
|   |   |   otherProjectService.js
|   |   |   otherProjectServiceCostService.js
|   |   |   otherProjectZgjService.js
|   |   |   projectOverviewService.js
|   |   |   projectTaxCalculationService.js
|   |   |   rcjDeRelationService.js
|   |   |   safeFeeService.js
|   |   |   singleProjectService.js
|   |   |   softdogService.js
|   |   |   storage.js
|   |   |   supplementService.js
|   |   |   sysDisctionaryService.js
|   |   |   SystemService.js
|   |   |   sysUser.js
|   |   |   jieSuanUnitCostCodePriceService.js
|   |   |   jieSuanUnitCostSummaryService.js
|   |   |   unitProjectService.js
|   |   |   ZsProjectCgCostMathService.js
|   |   |   
|   |   +---deDescribe
|   |   |       baseDeJobContentService.js
|   |   |       deDescribeProcess.js
|   |   |       
|   |   +---excel
|   |   |       dirService.js
|   |   |       
|   |   +---export
|   |   |       JieSuanExportQueryService.js
|   |   |       
|   |   +---fbfx
|   |   |       baseQdDeProcess.js
|   |   |       itemBillProjectOptionService.js
                分部分项服务类
                    此服务类继承自 BaseBranchProjectOptionService 
                    对 BaseBranchProjectOptionService 的方法进行了一定的补充
                    如遇到部分业务为分部分项独有，则在此方法重进行补充
|   |   |       itemBillProjectProcess.js
|   |   |       itemBillProjectService.js
|   |   |       UnitPriceService.js
                    单价构成核心服务
                    核心方法：
                        caculataDEUnitPrice 计算定额的单价构成
                        实现步骤：
                            1.获取费率
                            2.根据费率，循环定额下人材机，计算人工费 材料费 机械费 主材费
                            3.计算直接费用
                            4.计算其他费用
                            5.填充展示用数据
                    caculataDEUnitPrice 计算之后，会向上汇总清单
                    清单计算完成后会向上汇总分部分项
|   |   |       
|   |   +---listitem
|   |   |       listItemService.js
|   |   |       
|   |   +---overall
|   |   |       Management.js
|   |   |       
|   |   +---qdDescribe
|   |   |       baseListCalcRuleService.js
|   |   |       baseListJobContentService.js
|   |   |       qdDescribeProcess.js
|   |   |       
|   |   +---quantities
|   |   |       quantitiesService.js
|   |   |       
|   |   +---rcj
|   |   |       baseClpbService.js
|   |   |       baseDeRcjRelationService.js
|   |   |       baseJxpbService.js
|   |   |       baseRcjService.js
|   |   |       constructProjectRcjService.js
|   |   |       rcjProcess.js
|   |   |       
|   |   \---stepitem
|   |           measureProjectTableService.js
|   |           stepItemCostService.js
                    措施项目 服务类型
                    方法继承自  BaseBranchProjectOptionService
                    措施项目特有业务：
                        initItemCost 初始化措施项目
                            措施项目在项目初始化时候会生成一批数据，包括三个菜单，及单价措施下
                        save  新增一个单价措施元素
                            单价措施元素不能有子分部
                            单价措施元素的定额编码，每换一个清单，编码从新从 1.1开始排
|   |           
|   +---utils
|   |       aaa.js
|   |       ArrayUtil.js
|   |       ClJxSbZzsUtil.js
|   |       ConvertUtils.js
|   |       DateUtils.js
|   |       ExcelSheetList.js
|   |       JieSuanExcelUtil.js
|   |       HttpUtils.js
|   |       NumberUtil.js
|   |       ObjectUtils.js
|   |       PricingFileFindUtils.js
|   |       PricingFileWriteUtils.js
|   |       ProjectQdUtil.js
|   |       ReadXmlUtil.js
|   |       ResponseData.js
|   |       Snowflake.js
|   |       SqlUtils.js
|   |       StringUtils.js
|   |       TouBiaoUtil.js
|   |       ZhaoBiaoUtil.js
|   |       
|   \---vo
|           BranchDirectoryTree.js
|           CellVo.js
|           DeDescribeVo.js
|           SheetStyle.js
|           TreeListVo.js
|           



```
