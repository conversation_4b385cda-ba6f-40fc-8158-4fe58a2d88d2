'use strict';

const {Service, Log} = require('../../../core');
const {Snowflake} = require("../utils/Snowflake");
const {BaseDeLibrary} = require("../model/BaseDeLibrary");
const {ObjectUtils} = require("../utils/ObjectUtils");

/**
 * 定额册 service
 * @class
 */
class BaseDeLibraryService extends Service {

    constructor(ctx) {
        super(ctx);
        this._baseDeLibraryDao = this.app.appDataSource.manager.getRepository(BaseDeLibrary);
    }

    /**
     * 查所有定额册
     * @returns {BaseDeLibrary[]}
     */
    async list() {
        return await this._baseDeLibraryDao.find({
            order: {sortNo: "ASC"}
        });
    }

    /**
     * 根据定额标准查定额册
     * @param deStandardId
     * @returns {BaseDeLibrary[]|Error}
     */
    async listByDeStandard(deStandardId) {
        if (ObjectUtils.isEmpty(deStandardId)) {
            throw new Error("必传参数定额标准为空");
        }

        return await this._baseDeLibraryDao.find({
            where: {deStandardId: deStandardId},
            order: {sortNo: "ASC"}
        });
    }

    /**
     * 根据定额册编码查定额册
     * @param libraryCode
     * @returns {BaseDeLibrary|Error}
     */
    async getByLibraryCode(libraryCode) {
        if (ObjectUtils.isEmpty(libraryCode)) {
            throw new Error("必传参数定额册编码为空");
        }

        return await this._baseDeLibraryDao.findOneBy({libraryCode: libraryCode});
    }

}

BaseDeLibraryService.toString = () => '[class BaseDeLibraryService]';
module.exports = BaseDeLibraryService;
