'use strict';

const {Service} = require('../../../core');
const Log = require('../../../core/log');
const {BaseDeRuleRelation} = require("../model/BaseDeRuleRelation");

/**
 * 定额和规则关联关系表
 */
class BaseDeRuleRelationService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseDeRuleRelationDao = this.app.appDataSource.manager.getRepository(BaseDeRuleRelation);

    /**
     * 查定额和规则的关联关系
     * @param standardDeId 国标定额id
     * @return {Promise<BaseDeRuleRelation|Error>}
     */
    async selectRelationByDeId(standardDeId) {
        if (!standardDeId) {
            Log.error("必传参数国标定额id为空, selectRelationByDeId()查定额和规则的关联关系出错。");
            return null;
        }

        // 根据国标定额id查关联关系（ps: 若后续存在获取不到国标定额id的情况，可通过定额编码、名称、定额册code来查）
        return (await this.baseDeRuleRelationDao.findOneBy({sequenceNbr: standardDeId}));
    }
}

BaseDeRuleRelationService.toString = () => '[class BaseDeRuleRelationService]';
module.exports = BaseDeRuleRelationService;
