'use strict';

const { Service, Log } = require('../../../core');
const { ObjectUtils } = require('../utils/ObjectUtils');
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const DePropertyTypeConstant = require('../enum/DePropertyTypeConstant');
const { NumberUtil } = require('../utils/NumberUtil');
const BranchProjectLevelConstant = require('../enum/BranchProjectLevelConstant');
const { ArrayUtil } = require('../utils/ArrayUtil');
const GroundTypeConstant = require('../enum/GroundTypeConstant');
const StepItemCostLevelConstant = require('../enum/StepItemCostLevelConstant');
const { BaseAnZhuangRate, BaseAnZhuangRate2022 } = require('../model/BaseAnZhuangRate');
const { ConstructProjectRcj } = require('../model/ConstructProjectRcj');
const RcjTypeEnum = require('../enum/RcjTypeEnum');
const ConstantUtil = require('../enum/ConstantUtil');
const tempSettings = require('../enum/tempSettings');
const RcjLevelMarkConstant = require('../enum/RcjLevelMarkConstant');
const { BasePolicyDocument } = require('../model/BasePolicyDocument');
const ConstructionMeasureTypeConstant = require('../enum/ConstructionMeasureTypeConstant');
const { BaseDe2022, BaseDe } = require('../model/BaseDe');
const FxtjCostConstants = require('./costCalculation/fxtjCostMatch/FxtjCostConstants');
const FxtjCostMatchContext = require('./costCalculation/fxtjCostMatch/FxtjCostMatchContext');


/**
 * 自动记取
 * @class
 */
class AutoCostMathService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 手动添加定额
   */
  async autoCostMath(arg) {
    let { constructId, singleId, unitId, countCostCodeFlag, changeDeIdArr } = arg;
    console.time('自动计算费用定额记取耗时');

    let changeDeIds = new Set();
    if (ObjectUtils.isNotEmpty(changeDeIdArr)) {
      changeDeIds = new Set([...changeDeIdArr]);
    }

    //垂运记取
    await this.cyCost(arg, changeDeIds);

    //超高记取
    await this.cgCost(arg, changeDeIds);

    //安装记取
    await this.azCost(arg, changeDeIds);

    //其他总价措施记取
    await this.qtzjCost(arg, changeDeIds);

    // 房修土建费用记取
    await this.fxtjCostMatch(arg, changeDeIds);

    //安文费记取
    await this.awfCost(arg, changeDeIds);

    // 水电费   水电费中不修改定额
    await this.service.yuSuanProject.waterElectricCostMatchService.autoCalculateWaterElectricCost(arg);

    // 泵送增加费
    await this.service.yuSuanProject.pumpingAddFeeService.autoCalculatePumpingAddFee(constructId, singleId, unitId);

    const csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
    const fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);

    //重新计算所有金额  做定额和清单的汇总
    await this.service.yuSuanProject.unitPriceService.reCacaulateChange(constructId, singleId, unitId, csxm, changeDeIds);
    await this.service.yuSuanProject.unitPriceService.reCacaulateChange(constructId, singleId, unitId, fbFx, changeDeIds);

    console.timeEnd('自动计算费用定额记取耗时');
    // 统一计算费用代码
    countCostCodeFlag = ObjectUtils.isNotEmpty(countCostCodeFlag) ? countCostCodeFlag : false;
    if (countCostCodeFlag) {
      console.time('自动计算费用定额记取后-汇总费用代码耗时');
      await this.service.yuSuanProject.unitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId
      });
      console.timeEnd('自动计算费用定额记取后-汇总费用代码耗时');
    }
  }


  /**
   * 超高记取
   */
  async cgCost(arg, changeDeIds) {
    let { constructId, singleId, unitId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
    //获取超高的费用定额
    let deScope = this.costDeScope(constructId, singleId, unitId);

    let cgCostDeList = deScope.filter(k => k.isCostDe === DePropertyTypeConstant.CG_DE);
    if (ObjectUtils.isNotEmpty(cgCostDeList)) {
      // 筛选不属于总价包干的定额
      cgCostDeList = cgCostDeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    if (ObjectUtils.isEmpty(cgCostDeList)) {
      return;
    }

    //获取到缓存
    let result = unit.cgCostMathCache;

    //获取单位下所有人材机数据
    let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
    //循环超高费用定额
    for (const cgCostDe of cgCostDeList) {
      changeDeIds.add(cgCostDe.sequenceNbr);
      //计算基数
      let deMathBase = 0;
      let caculateParam = {};
      //缓存不为空
      if (!ObjectUtils.isEmpty(result)) {
        let data = result.data;
        let baseDeList = await this.getCgBaseDeList(data, arg);
        let group = ArrayUtil.group(baseDeList, 'up');
        caculateParam['groupByUp'] = group;
        let cacheBaseDeList = group.get(cgCostDe.bdCode);
        if (!ObjectUtils.isEmpty(cacheBaseDeList)) {
          const intersection = cacheBaseDeList.filter(item1 =>
            deScope.some(item2 =>
              item2.sequenceNbr === item1.sequenceNbr && item2.bdCode === item1.bdCode
            )
          );
          if (!ObjectUtils.isEmpty(intersection)) {
            //重新人材机计算合计数量以及合价
            //deMathBase = this.updateTotalNumber(cacheBaseDeList, unit, rcjList, intersection, cgCostDe);
            deMathBase = this.updateTotalNumber(intersection, unit, rcjList, intersection, cgCostDe);
          }
        }
      }
      let is22De = PricingFileFindUtils.is22Unit(unit);
      //费用定额的人材机数据

      const deRcj = rcjList.filter(k => k.deId === cgCostDe.sequenceNbr);
      // 因为下面不需要单独去计算人材机中的【人工】的合计数量和合价  所以不需要筛选了 这里把这个筛选注释掉
      // let rjTypes = is22De ? [RcjTypeEnum['Rengong'].code] : [RcjTypeEnum['Rengong'].code, RcjTypeEnum['Jixie'].code];
      // 筛选出定额下的人材机
      // // 在根据人才机类型【rjTypes】筛选出人材机
      // let rcjs = deRcj.filter(k => rjTypes.includes(k.kind));

      // 赋值计算基数设
      cgCostDe.baseNum = { def: deMathBase };
      cgCostDe.formula = deMathBase;
      cgCostDe.caculatePrice = 1;

      //计算费用定额的人材机的合价和合计数量
      //人材机明细：合计数量=定额工程量*消耗量*计算基数
      if (is22De) {
        // 这里单独处理22定额  如果是超高的22定额  计算基数默认为1
        cgCostDe.baseNum = 1;
        cgCostDe.quantityExpression = ConstantUtil.EXP_CG_RGHJ;
        cgCostDe.quantityExpressionNbr = await this.service.yuSuanProject.baseBranchProjectOptionService.initCurrentUpdateLine4cg(constructId, singleId, unitId, cgCostDe, null, caculateParam);
        if (cgCostDe.quantityExpression) {
          const regex = /\b\d+\b/;
          let match = cgCostDe.unit.match(regex);
          if (match) {
            const number = parseInt(match[0]);
            cgCostDe.quantity = NumberUtil.numberScale(NumberUtil.divide(cgCostDe.quantityExpressionNbr, number), 6);
          } else {
            cgCostDe.quantity = NumberUtil.numberScale(NumberUtil.divide(cgCostDe.quantityExpressionNbr, 1), 6);
          }
        }
      }
      // 在超高的工程量发生变化后  重新计算其下的人材机的合计数量和合价
      await this.service.yuSuanProject.zsProjectCgCostMathService.computeCgRcjTotalNumberAndTotal(is22De, deRcj, cgCostDe, deMathBase);

      // 再处理是否需要措施中人工费调整
      await this.service.yuSuanProject.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(constructId, singleId, unitId, cgCostDe);

      //计算单价构成
      this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId,
        cgCostDe.sequenceNbr, true, PricingFileFindUtils.getModuleData(constructId, singleId, unitId, cgCostDe.sequenceNbr), false);
    }
  }

  /**
   * 获取超高自动记取时的基数定额
   */
  async getCgBaseDeList(data, arg) {
    let baseDeList = [];
    let cacheDeList = data.filter(k => k.kind === BranchProjectLevelConstant.de && !ObjectUtils.isEmpty(k.up));
    if (ObjectUtils.isNotEmpty(cacheDeList)) {
      baseDeList = baseDeList.concat(cacheDeList);
    }
    // 查询出当前超高的基数定额和清单等列表数据
    let zsBaseDeList = await this.service.yuSuanProject.constructCostMathService.getZsBaseDeList(arg);
    if (ObjectUtils.isEmpty(zsBaseDeList)) {
      return baseDeList;
    }
    // 找出不在缓存中的数据
    const newDataList = zsBaseDeList.filter(itemA => !data.some(itemB => itemB.sequenceNbr === itemA.sequenceNbr));
    if (ObjectUtils.isEmpty(newDataList)) {
      return baseDeList;
    }
    // 不在缓存中的定额
    const newDeDataList = newDataList.filter(k => k.kind === BranchProjectLevelConstant.de);
    if (ObjectUtils.isEmpty(newDeDataList)) {
      return baseDeList;
    }
    for (const de of newDeDataList) {
      // 如果这个定额的父级是缓存中的清单、分部、单位工程   那么直接使用缓存中的父级的【檐高层数】和【超高记取类型】
      // 如果这个定额的父级不是缓存中的数据，那么就一级一级递归往上找  直到找到在缓存中的上级或者到了单位工程层级之后，直接使用这个上的【檐高层数】和【超高记取类型】
      this.findCacheData(de, zsBaseDeList, data, baseDeList, de);
    }
    return baseDeList;
  }


  findCacheData(de, zsBaseDeList, data, baseDeList, current) {
    // 先查询current的父级在不在缓存中
    const cacheParentData = data.find(item => item.sequenceNbr == current.parentId);
    if (ObjectUtils.isNotEmpty(cacheParentData)) {
      // 在缓存中找到了父级  直接使用父级的【檐高层数】和【超高记取类型】
      this.setCgItemData(de, cacheParentData, baseDeList);
      return;
    }
    // 缓存中没有就去所有的里面找出这条数据  然后查他的上级
    const parentData = zsBaseDeList.find(item => item.sequenceNbr == current.parentId);
    if (ObjectUtils.isNotEmpty(parentData)) {
      if (ObjectUtils.isEmpty(parentData.parentId)) {
        // 说明是顶级单位工程  直接使用【檐高层数】和【超高记取类型】
        this.setCgItemData(de, parentData, baseDeList);
        return;
      }
      this.findCacheData(de, zsBaseDeList, data, baseDeList, parentData);
    }
  }

  setCgItemData(de, item, baseDeList) {
    let newDe = {};
    newDe.sequenceNbr = de.sequenceNbr;
    newDe.bdName = de.bdName;
    newDe.bdCode = de.bdCode;
    newDe.fxCode = de.fxCode;
    newDe.name = de.name;
    newDe.kind = de.kind;
    newDe.type = de.type;
    newDe.quantity = de.quantity;
    newDe.parentId = de.parentId;
    newDe.upOrDown = 'up';
    newDe.up = item.up;
    newDe.value = item.value;
    baseDeList.push(newDe);
  }

  /**
   * 费用定额范围
   */
  costDeScope(constructId, singleId, unitId) {
    //let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //单价措施定额
    let deListDjcs = PricingFileFindUtils.getDeByDjcs(constructId, singleId, unitId);
    //总价措施定额
    let deListZjcs = PricingFileFindUtils.getDeByZjcs(constructId, singleId, unitId);
    //分部分项定额
    let deListFbfx = PricingFileFindUtils.getDeByfbfx(constructId, singleId, unitId);
    //安文费定额
    let deListAwf = PricingFileFindUtils.getDeByAwf(constructId, singleId, unitId);

    let array = [];
    array.push(...deListDjcs);
    array.push(...deListZjcs);
    array.push(...deListFbfx);
    array.push(...deListAwf);
    return array;
  }


  /**
   * 查询河北省装饰装修工程消耗量定额（2012）下挂的定额 b1-b7
   */
  async getDecorationDe(constructId, singleId, unitId) {
    return await this.service.yuSuanProject.baseDeService.selectDecorationDe(constructId, singleId, unitId);
  }

  //重新修改人材机计算合计数量以及合价
  updateTotalNumber(cacheBaseDeList, unit, rcjList, baseDeList, cgCostDe) {
    let { sequenceNbr, spId, constructId } = unit;
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    //获取计税方式 '1'?'一般计税':'简易计税'
    let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
    //循环基数定额
    //计算基数
    let deMathBase = 0;
    for (const de of cacheBaseDeList) {
      //记取类型
      let { value } = de;
      //获取基数定额工程量
      // let { quantity } = baseDeList.find(k => k.sequenceNbr === de.sequenceNbr);   // 这行历史代码感觉有问题  这个工程量取的永远都是缓存的上次的工程量
      const baseDe = baseDeList.find(k => k.sequenceNbr === de.sequenceNbr);
      const { quantity } = PricingFileFindUtils.getDeById(constructId, spId, sequenceNbr, baseDe.sequenceNbr);
      let rjTypes = PricingFileFindUtils.is22UnitById(constructId, spId, sequenceNbr) ? [RcjTypeEnum['Rengong'].code] : [RcjTypeEnum['Rengong'].code, RcjTypeEnum['Jixie'].code];
      //获取基础定额人工和机械的明细
      let rcjs = rcjList.filter(k => k.deId === de.sequenceNbr && rjTypes.includes(k.kind));
      if (!ObjectUtils.isEmpty(rcjs)) {

        let rSum = 0;
        let jSum = 0;

        //计算基础定额人工和机械的定额价合计
        rcjs.forEach(k => {
          //机械
          if (k.kind === 3) {
            //解析并且下沉
            if (k.levelMark != RcjLevelMarkConstant.NO_SINK && k.markSum == 1) {
              let { rDetail, cDetail, jDetail } = PricingFileFindUtils.getRcjDetailGroup(unit, k);
              //人
              if (!ObjectUtils.isEmpty(rDetail)) {
                const RSum = rDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, quantity), 0);
                rSum = NumberUtil.add(NumberUtil.numberScale(RSum, 2), rSum);
              }
              //机 在22定额标准下,不合计机械基数费用
              if (!ObjectUtils.isEmpty(jDetail) && !is22Unit) {
                const JSum = jDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, quantity), 0);
                jSum = NumberUtil.add(NumberUtil.numberScale(JSum, 2), jSum);
              }
            } else {
              const JSum = NumberUtil.multiplyParams(k.dePrice, k.resQty, quantity);
              jSum = NumberUtil.add(JSum, jSum);
            }
          }


          if (k.kind === 1) {
            //人
            const RSum = NumberUtil.multiplyParams(k.dePrice, k.resQty, quantity);
            rSum = NumberUtil.add(RSum, rSum);
          }

        });
        //计算费用定额的计算基数
        deMathBase += NumberUtil.multiply(rSum + jSum, value);
      }
    }
    // 如果定额的单位为 “100工日” 这种， 需要给计算基数除100
    const regex = /\b\d+\b/;
    let match = cgCostDe.unit.match(regex);
    if (match) {
      const number = parseInt(match[0]);
      deMathBase = NumberUtil.numberScale(NumberUtil.divide(deMathBase, number), 2);
    }
    return deMathBase;
  }

  /**
   * 垂运记取
   * @param arg
   */
  async cyCost(arg, changeDeIds) {
    let { constructId, singleId, unitId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //分部分项定额
    let deByfbfx = PricingFileFindUtils.getDeByfbfx(constructId, singleId, unitId);
    //单价措施定额
    let deByDjcs = PricingFileFindUtils.getDeByDjcs(constructId, singleId, unitId);
    let deList = [];
    deList.push(...deByfbfx);
    deList.push(...deByDjcs);

    //获取标准基数定额
    let baseDeList = await this.getDecorationDe(constructId, singleId, unitId);
    let baseDeIdList = baseDeList.map(k => k.sequenceNbr);
    //获取到项目里的垂运基数定额
    deList = deList.filter(k => baseDeIdList.includes(k.standardId));
    if (ObjectUtils.isNotEmpty(deList)) {
      // 筛选不属于总价包干的基数定额
      deList = deList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    // if (ObjectUtils.isEmpty(deList)) {
    //     return;
    // }
    //获取垂运缓存
    let result = unit.cyCostMathCache;
    let ds = [];
    let dx = [];
    if (ObjectUtils.isEmpty(result)) {
      ds.push(...deList);
    } else {
      //只筛选定额
      let data = result.data.filter(k => k.kind === StepItemCostLevelConstant.de);
      //根据定额选择的以上以下分组  值为 up/down
      let group = ArrayUtil.group(data, 'upOrDown');
      //地下
      let dxList = group.get(GroundTypeConstant.DOWN);
      if (!ObjectUtils.isEmpty(dxList)) {
        let dxIdList = dxList.map(k => k.sequenceNbr);
        dxList = deList.filter(k => dxIdList.includes(k.sequenceNbr));
        let dsList = deList.filter(k => !dxIdList.includes(k.sequenceNbr));
        dx.push(...dxList);
        ds.push(...dsList);
      } else {
        ds.push(...deList);
      }
    }

    //获取到当前项目的所有人材机数据
    let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);

    if (ObjectUtils.isEmpty(rcjList)) {
      return;
    }
    let dsIdList = ds.map(k => k.sequenceNbr);
    let dxIdList = dx.map(k => k.sequenceNbr);
    //求和
    let dsReduce = rcjList.filter(k => k.kind === 1 && k.unit === '工日' && dsIdList.includes(k.deId))
      .map(k => Number(k.totalNumber)).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
    let dxReduce = rcjList.filter(k => k.kind === 1 && k.unit === '工日' && dxIdList.includes(k.deId))
      .map(k => Number(k.totalNumber)).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
    let dsNumber = NumberUtil.numberScale(dsReduce, 6);
    let dxNumber = NumberUtil.numberScale(dxReduce, 6);

    let itemBillProjects = unit.itemBillProjects.getAllNodes();
    let measureProjectTables = unit.measureProjectTables.getAllNodes();

    let allDe = [];
    allDe.push(...itemBillProjects);
    allDe.push(...measureProjectTables);
    if (ObjectUtils.isNotEmpty(allDe)) {
      // 筛选不属于总价包干的定额
      allDe = allDe.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    //获取垂运的费用定额
    //地上
    let dsCostDe = allDe.filter(k => k.quantityExpression === 'DSZSGR');
    dsCostDe.forEach(k => {
      // if (ObjectUtils.isEmpty(ds)){
      //     k.quantityExpression = 0;
      // }

      k.quantityExpressionNbr = dsNumber;
      const regex = /\b\d+\b/;
      let match = k.unit.match(regex);
      if (match) {
        const number = parseInt(match[0]);
        k.quantity = NumberUtil.numberScale(NumberUtil.divide(k.quantityExpressionNbr, number), 6);
      } else {
        k.quantity = NumberUtil.numberScale(NumberUtil.divide(k.quantityExpressionNbr, 1), 6);
      }

      let rcjs = this.service.yuSuanProject.rcjProcess.getRcjListByDeId(k.sequenceNbr, constructId, singleId, unitId);
      if (!ObjectUtils.isEmpty(rcjs)) {
        this.service.yuSuanProject.rcjProcess.reCaculateRcjPrice(k, rcjs, constructId, singleId, unitId);

        let rcj = rcjs[0];
        let constructProjectRcj = new ConstructProjectRcj();
        constructProjectRcj.type = rcj.type;
        constructProjectRcj.materialName = rcj.materialName;
        constructProjectRcj.specification = rcj.specification;
        constructProjectRcj.unit = rcj.unit;
        constructProjectRcj.dePrice = rcj.dePrice;
        this.service.yuSuanProject.unitPriceService.caculateDeByRcj(constructId, singleId, unitId, rcj);
      }

      //this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, k.sequenceNbr, true, deList);
      changeDeIds.add(k.sequenceNbr);
    });
    //地下
    let dxCostDe = allDe.filter(k => k.quantityExpression === 'DXZSGR');
    dxCostDe.forEach(k => {
      // if (ObjectUtils.isEmpty(dx)){
      //     k.quantityExpression = 0;
      // }
      k.quantityExpressionNbr = dxNumber;
      const regex = /\b\d+\b/;
      let match = k.unit.match(regex);
      if (match) {
        const number = parseInt(match[0]);
        k.quantity = NumberUtil.numberScale(NumberUtil.divide(k.quantityExpressionNbr, number), 6);
      } else {
        k.quantity = NumberUtil.numberScale(NumberUtil.divide(k.quantityExpressionNbr, 1), 6);
      }
      let rcjs = this.service.yuSuanProject.rcjProcess.getRcjListByDeId(k.sequenceNbr, constructId, singleId, unitId);
      if (!ObjectUtils.isEmpty(rcjs)) {
        this.service.yuSuanProject.rcjProcess.reCaculateRcjPrice(k, rcjs, constructId, singleId, unitId);

        let rcj = rcjs[0];
        let constructProjectRcj = new ConstructProjectRcj();
        constructProjectRcj.type = rcj.type;
        constructProjectRcj.materialName = rcj.materialName;
        constructProjectRcj.specification = rcj.specification;
        constructProjectRcj.unit = rcj.unit;
        constructProjectRcj.dePrice = rcj.dePrice;
        constructProjectRcj.libraryCode = rcj.libraryCode;
        this.service.yuSuanProject.unitPriceService.caculateDeByRcj(constructId, singleId, unitId, constructProjectRcj);
      }
      changeDeIds.add(k.sequenceNbr);
    });
    let DSZSGR = unit.unitCostCodePrices.find(k => k.code === 'DSZSGR');
    let DXZSGR = unit.unitCostCodePrices.find(k => k.code === 'DXZSGR');
    DSZSGR.price = NumberUtil.numberScale(dsReduce, 6);
    DXZSGR.price = NumberUtil.numberScale(dxReduce, 6);
  }

  /**
   * 安装记取
   *
   * 自动记取时对于手动添加的安装费用定额：
   * 1.添加到：其他总价措施（基数定额的筛选范围直接使用 指定措施清单）
   * 2.添加到：安文费（计算基数为0）
   * 3.添加到：分部分项（基数定额的筛选范围直接使用 对应清单记取）
   * 4.添加到：单价措施（基数定额的筛选范围直接使用 对应清单记取）
   */
  async azCost(arg, changeDeIds) {
    let { constructId, singleId, unitId } = arg;

    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let is22De = PricingFileFindUtils.is22Unit(unit);

    //获取安装的费用定额
    //单价措施定额
    let deListDjcs = PricingFileFindUtils.getDeByDjcs(constructId, singleId, unitId);
    //总价措施定额
    let deListZjcs = PricingFileFindUtils.getDeByZjcs(constructId, singleId, unitId);
    //安文费
    let deListAwf = PricingFileFindUtils.getDeByAwf(constructId, singleId, unitId);
    //分部分项定额
    let deListFbfx = PricingFileFindUtils.getDeByfbfx(constructId, singleId, unitId);

    //自动记取过的费用定额集合
    let autoCostDeScope = this.costDeScope(constructId, singleId, unitId);
    // 这个isAutoCost为true表示是手动记取出来的费用定额
    let autoCostList = autoCostDeScope.filter(k => k.isAutoCost && k.isCostDe === DePropertyTypeConstant.AZ_DE);
    if (ObjectUtils.isNotEmpty(autoCostList)) {
      // 筛选不属于总价包干的定额
      autoCostList = autoCostList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }

    let result = unit.azCostMathCache;
    let typeMap = new Map();
    //获取缓存中的费用定额设置信息.
    if (!ObjectUtils.isEmpty(result)) {
      for (let key of Object.keys(result.data)) {
        // 分为【安装工程】和【房屋修缮】
        let array = [];
        let baseDeMap = new Map();
        for (const item of result.data[key]) {
          let { classLevelList, feeCode, baseDeList } = item;
          classLevelList.forEach(k => {
            array.push(k.isDefaultRow);
          });
          baseDeMap.set(feeCode, baseDeList);
        }
        typeMap.set(key, {
          defaultRowArray: array,
          baseDeMap: baseDeMap
        });
      }
    }

    //项目中自动记取方式的费用定额重新计算
    if (ObjectUtils.isNotEmpty(autoCostList)) {
      for (const costDe of autoCostList) {
        //该费用定额的缓存设置信息  这一行的作用是在缓存中查找到对应的费用定额基本信息  比如【超高费】的【第二册 电气设备安装工程】的【1~14超高费(60层/200m以下)(电气设备安装工程)】
        let array = [];
        let baseDeMap = new Map();
        if (typeMap.size > 0) {
          array = typeMap.get('az').defaultRowArray;
          baseDeMap = typeMap.get('az').baseDeMap;
          if (this.service.yuSuanProject.azCostMathService.isFwxs(costDe.libraryCode)) {
            array = typeMap.get('fwxs').defaultRowArray;
            baseDeMap = typeMap.get('fwxs').baseDeMap;
          }
        }
        // 由于【房屋修缮的垂直运输费】标准费用定额(base_anzhuang_rate)的deCode都是【5-77】,导致这里不能只用deCode和feeCode查询，所以需要加上deName作为判断条件
        let baseDe = await this.app.appDataSource.getRepository(is22De ? BaseDe2022 : BaseDe).findOne({
          where: { sequenceNbr: costDe.standardId }
        });
        let costDeSetInfo = array.find(k => k.deCode === costDe.bdCode && parseInt(k.feeCode) === costDe.value && k.deName == baseDe.deName);
        let selectBaseDeList = unit.itemBillProjects.getAllNodes();
        // 过滤出用户在页面上选择的基数定额
        const baseDeList = baseDeMap.get(costDe.value.toString());
        if (ObjectUtils.isNotEmpty(baseDeList)) {
          selectBaseDeList = selectBaseDeList.filter(item => baseDeList.includes(item.sequenceNbr));
        }
        //根据前端选择的费用定额，在分部分项里面查询对应的符合条件基数定额有哪些
        let baseDeArray = this.service.yuSuanProject.azCostMathService.getCostDeByBaseDe(selectBaseDeList, costDeSetInfo, is22De, costDe);
        if (ObjectUtils.isNotEmpty(baseDeArray)) {
          // 筛选不属于总价包干的定额
          baseDeArray = baseDeArray.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
          if (ObjectUtils.isNotEmpty(result.outputType) && result.outputType == '1') {
            // 如果清单费用按每个分部分别计取  那么基数定额和费用定额就是同一个分部下的
            baseDeArray = baseDeArray.filter(baseDe => baseDe.parent.parentId == costDe.parent.parentId);
          }
        }
        // 重新计算人材机数据以及单价构成
        await this.azUpdateTotalNumber(unit, costDe,
          baseDeArray.map(k => k.sequenceNbr), costDeSetInfo,
          PricingFileFindUtils.getModuleData(constructId, singleId, unitId, costDe.sequenceNbr));
        changeDeIds.add(costDe.sequenceNbr);
      }
    }

    //手动记取费用定额
    //获取到所有的费用定额默认设置
    let anZhuangRateList = await this.getBaseAnZhuang(is22De);

    //分部分项下 手动：对应清单记取   !k.isAutoCost表示不是手动记取出来的安装费用定额  也就是手动添加的费用定额
    let fbfxCodeList = deListFbfx.filter(k => !k.isAutoCost && k.isCostDe === DePropertyTypeConstant.AZ_DE);
    if (ObjectUtils.isNotEmpty(fbfxCodeList)) {
      // 筛选不属于总价包干的定额
      fbfxCodeList = fbfxCodeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    if (ObjectUtils.isNotEmpty(fbfxCodeList)) {
      for (const costDe of fbfxCodeList) {
        let array = [];
        if (typeMap.size > 0) {
          array = typeMap.get('az').defaultRowArray;
          if (this.service.yuSuanProject.azCostMathService.isFwxs(costDe.libraryCode)) {
            array = typeMap.get('fwxs').defaultRowArray;
          }
        }
        //获取到该清单下的所有定额
        let qdByDeList = deListFbfx.filter(k => k.parentId === costDe.parentId && k.value === 0);

        let costDeSet = array.find(k => k.deCode === costDe.bdCode);
        if (ObjectUtils.isEmpty(costDeSet)) {
          costDeSet = anZhuangRateList.find(k => parseInt(k.feeCode) === costDe.value && k.deCode === costDe.bdCode);
        }

        //根据前端选择的费用定额，在分部分项里面查询对应的符合条件基数定额有哪些
        let baseDeArray = this.service.yuSuanProject.azCostMathService.getCostDeByBaseDe(qdByDeList, costDeSet, is22De);
        if (ObjectUtils.isNotEmpty(baseDeArray)) {
          // 筛选不属于总价包干的定额
          baseDeArray = baseDeArray.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
        }
        // 重新计算人材机数据以及单价构成
        await this.azUpdateTotalNumber(unit, costDe,
          baseDeArray.map(k => k.sequenceNbr), costDeSet,
          PricingFileFindUtils.getModuleData(constructId, singleId, unitId, costDe.sequenceNbr));

        changeDeIds.add(costDe.sequenceNbr);
      }
    }
    //单价措施
    let djcsCodeList = deListDjcs.filter(k => !k.isAutoCost && k.isCostDe === DePropertyTypeConstant.AZ_DE);
    if (ObjectUtils.isNotEmpty(djcsCodeList)) {
      // 筛选不属于总价包干的定额
      djcsCodeList = djcsCodeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    if (ObjectUtils.isNotEmpty(djcsCodeList)) {
      for (const costDe of djcsCodeList) {
        let array = [];
        if (typeMap.size > 0) {
          array = typeMap.get('az').defaultRowArray;
          if (this.service.yuSuanProject.azCostMathService.isFwxs(costDe.libraryCode)) {
            array = typeMap.get('fwxs').defaultRowArray;
          }
        }
        //获取到该清单下的所有定额
        let qdByDeList = deListDjcs.filter(k => k.parentId === costDe.parentId && k.value === 0);
        let costDeSet = array.find(k => k.deCode === costDe.bdCode);
        if (ObjectUtils.isEmpty(costDeSet)) {
          costDeSet = anZhuangRateList.find(k => parseInt(k.feeCode) === costDe.value && k.deCode === costDe.bdCode);
        }
        //从该清单下的定额集合中筛选出那些是安装费用定额对应的基数定额
        let baseDeArray = this.service.yuSuanProject.azCostMathService.getCostDeByBaseDe(qdByDeList, costDeSet, is22De);
        if (ObjectUtils.isNotEmpty(baseDeArray)) {
          // 筛选不属于总价包干的定额
          baseDeArray = baseDeArray.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
        }
        // 重新计算人材机数据以及单价构成
        await this.azUpdateTotalNumber(unit, costDe,
          baseDeArray.map(k => k.sequenceNbr), costDeSet,
          PricingFileFindUtils.getModuleData(constructId, singleId, unitId, costDe.sequenceNbr));

        changeDeIds.add(costDe.sequenceNbr);
      }
    }
    //其他总价措施 手动套取：指定措施清单
    let zjcsCodeList = deListZjcs.filter(k => !k.isAutoCost && k.isCostDe === DePropertyTypeConstant.AZ_DE);
    if (ObjectUtils.isNotEmpty(zjcsCodeList)) {
      // 筛选不属于总价包干的定额
      zjcsCodeList = zjcsCodeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    if (ObjectUtils.isNotEmpty(zjcsCodeList)) {
      for (const costDe of zjcsCodeList) {
        let array = [];
        if (typeMap.size > 0) {
          array = typeMap.get('az').defaultRowArray;
          if (this.service.yuSuanProject.azCostMathService.isFwxs(costDe.libraryCode)) {
            array = typeMap.get('fwxs').defaultRowArray;
          }
        }
        let costDeSet = array.find(k => k.deCode === costDe.bdCode);
        if (ObjectUtils.isEmpty(costDeSet)) {
          costDeSet = anZhuangRateList.find(k => parseInt(k.feeCode) === costDe.value && k.deCode === costDe.bdCode);
        }
        //在分部分项里面查询对应的符合条件基数定额有哪些
        let baseDeArray = this.service.yuSuanProject.azCostMathService.getCostDeByBaseDe(deListFbfx, costDeSet, is22De);
        if (ObjectUtils.isNotEmpty(baseDeArray)) {
          // 筛选不属于总价包干的定额
          baseDeArray = baseDeArray.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
        }
        // 重新计算人材机数据以及单价构成
        await this.azUpdateTotalNumber(unit, costDe,
          baseDeArray.map(k => k.sequenceNbr), costDeSet,
          PricingFileFindUtils.getModuleData(constructId, singleId, unitId, costDe.sequenceNbr));

        changeDeIds.add(costDe.sequenceNbr);
      }
    }

    //安文费 手动套取：计算基数为0
    let awfCodeList = deListAwf.filter(k => !k.isAutoCost && k.isCostDe === DePropertyTypeConstant.AZ_DE);
    if (ObjectUtils.isNotEmpty(awfCodeList)) {
      // 筛选不属于总价包干的定额
      awfCodeList = awfCodeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    if (ObjectUtils.isNotEmpty(awfCodeList)) {
      for (const costDe of awfCodeList) {
        // 安文费的不需要看基数定额  因为计算基数都按照0算
        // 重新计算人材机数据以及单价构成
        await this.azAwfUpdateTotalNumber(unit, costDe, PricingFileFindUtils.getModuleData(constructId, singleId, unitId, costDe.sequenceNbr));
        changeDeIds.add(costDe.sequenceNbr);
      }
    }
  }

  /**
   * 单独处理安装的安文费下的计算基数相关
   * 安文费下的安装费用定额计算基数都是0  所有人材机的合计数量合价直接给0就行
   */
  async azAwfUpdateTotalNumber(unit, costDe, titleData) {
    let { sequenceNbr, spId, constructId } = unit;
    let rcjList = PricingFileFindUtils.getRcjList(constructId, spId, sequenceNbr);
    const deRcjArr = rcjList.filter(rcj => rcj.deId == costDe.sequenceNbr);
    for (const item of deRcjArr) {
      if (item.materialCode !== ConstantUtil.CODE_RGF_ADJUST) {
        // 措施中人工费调整不进行修改
        item.marketPrice = 1;
        item.dePrice = 1;
      }
      //合计数量
      item.totalNumber = 0;
      //合价
      item.total = 0;
    }
    //赋值计算基数
    costDe.caculatePrice = 1;
    costDe.baseNum = { 1: 0, 2: 0, 3: 0 };
    // 检测是否需要措施中人工费调整
    await this.service.yuSuanProject.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(constructId, spId, sequenceNbr, costDe);

    //计算单价构成
    this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(constructId, spId, sequenceNbr,
      costDe.sequenceNbr, true, titleData, false);
  }

  async azUpdateTotalNumber(unit, costDe, deIdList, selectDe, titleData) {
    let { calculateBase } = selectDe;
    let { sequenceNbr, spId, constructId } = unit;

    let deDataList = [];
    let deFbfx = PricingFileFindUtils.getDeByfbfx(constructId, spId, sequenceNbr);
    let deByDjcs = PricingFileFindUtils.getDeByDjcs(constructId, spId, sequenceNbr);
    let deByZjcs = PricingFileFindUtils.getDeByZjcs(constructId, spId, sequenceNbr);
    deDataList.push(...deFbfx);
    deDataList.push(...deByDjcs);
    deDataList.push(...deByZjcs);

    //获取到基数定额详情数据集合
    let deList = deDataList.filter(k => deIdList.includes(k.sequenceNbr));

    //获取单位下所有人材机数据
    let rcjList = PricingFileFindUtils.getRcjList(constructId, spId, sequenceNbr);

    //筛选基数定额的人材机数据
    let baseRcjs = rcjList.filter(k => deIdList.includes(k.deId) && this.service.yuSuanProject.azCostMathService.calculateBaseHandler(calculateBase, selectDe.allocationMethod).includes(k.kind));

    //根据分摊选择算 费率
    let mathRate = this.service.yuSuanProject.azCostMathService.allocationMethodCostRate(selectDe, this.service.yuSuanProject.azCostMathService.calculateBaseHandler(calculateBase, selectDe.allocationMethod));
    //分别算人材机基数
    let {
      rBase,
      cBase,
      jBase
    } = this.service.yuSuanProject.azCostMathService.rcjBaseCost(selectDe, baseRcjs, deList, mathRate, unit, costDe);

    let deMathBase = null;
    let { rRate, cRate, jRate } = mathRate;
    //如果材料的费率为0，则删除该条材料
    for (let i = rcjList.length - 1; i >= 0; i--) {
      let item = rcjList[i];
      let { kind, sequenceNbr, deId } = item;
      //找到费用定额人材机
      if (deId === costDe.sequenceNbr) {
        //人
        if (kind === 1) {
          deMathBase = rBase;
        }
        //机
        if (kind === 3) {
          deMathBase = jBase;
        }
        //材料
        if (![1, 3, 4].includes(kind)) {
          deMathBase = cBase;
        }
        if (item.materialCode !== ConstantUtil.CODE_RGF_ADJUST) {
          // 措施中人工费调整不进行修改
          item.marketPrice = 1;
          item.dePrice = 1;
        }
        //合计数量
        item.totalNumber = NumberUtil.numberScale(NumberUtil.multiplyParams(item.resQty, costDe.quantity, deMathBase), 4);
        //合价
        item.total = NumberUtil.numberScale(NumberUtil.multiply(item.totalNumber, item.marketPrice));
      }
    }
    //赋值计算基数
    costDe.caculatePrice = 1;
    costDe.baseNum = { 1: rBase, 2: cBase, 3: jBase };

    // 检测是否需要措施中人工费调整
    await this.service.yuSuanProject.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(constructId, spId, sequenceNbr, costDe);

    //计算单价构成
    this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(constructId, spId, sequenceNbr,
      costDe.sequenceNbr, true, titleData, false);
  }

  async getBaseAnZhuang(is22De) {
    let anZhuangRateList = await this.app.appDataSource.getRepository(is22De ? BaseAnZhuangRate2022 : BaseAnZhuangRate).find();
    return anZhuangRateList;
  }

  /**
   * 其他总价措施记取
   * @param arg
   * @return {Promise<void>}
   */
  async qtzjCost(arg, changeDeIds) {
    let { constructId, singleId, unitId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取参数缓存
    let zjcsCostMathCache = unit.zjcsCostMathCache;
    //获取总价措施费用定额
    let costDeList = this.costDeScope(constructId, singleId, unitId).filter(k => k.isCostDe === DePropertyTypeConstant.ZJCS_DE);
    if (ObjectUtils.isNotEmpty(costDeList)) {
      // 筛选不属于总价包干的定额
      costDeList = costDeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    if (ObjectUtils.isEmpty(costDeList)) {
      return;
    }
    //总价措施定额
    let deListZjcs = PricingFileFindUtils.getDeByZjcs(constructId, singleId, unitId);
    //获取总价措施需要的基数定额数据
    let baseDeList = this.service.yuSuanProject.constructCostMathService.getOtherZJCSUseDe(unit);
    if (ObjectUtils.isNotEmpty(baseDeList)) {
      // 筛选不属于总价包干的定额
      baseDeList = baseDeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    //处理基数定额并且根据基数定额施工组织措施类别对于基数定额进行分组（处理随主工程的情况）
    //主取费文件
    let mainFeeFile = PricingFileFindUtils.getMainFeeFile(constructId, singleId, unitId);
    let baseDeMap = await this.service.yuSuanProject.constructCostMathService.baseDeGroup(baseDeList, unit, mainFeeFile);
    //计算--基数定额计算基数
    let costBase = this.service.yuSuanProject.constructCostMathService.costBase(baseDeMap, unit, zjcsCostMathCache);
    //获取单位下所有人材机数据
    let rcjList = PricingFileFindUtils.getRcjList(unit.constructId, unit.spId, unit.sequenceNbr);

    //计算“措施项目人工费调整”市场价
    let unitRs = unit.constructProjectRcjs.filter(item => item.materialCode == ConstantUtil.ZHYG_LEVEL_STR_2);
    if (ObjectUtils.isEmpty(unitRs) && ObjectUtils.isNotEmpty(unit.rcjDetailList)) {
      unitRs = unit.rcjDetailList.filter(item => item.materialCode == ConstantUtil.ZHYG_LEVEL_STR_2);
    }
    let basePolicydocument = await this.app.appDataSource.manager.getRepository(BasePolicyDocument).findOne({
      where:
        {
          sequenceNbr: unit.rgfId
        }
    });
    let rgfMarketPriceLevel2 = ObjectUtils.isNotEmpty(unitRs) ? unitRs[0].marketPrice : basePolicydocument.zhygLevel2;
    let priceDifferenceProjectRcjMarketPrice = NumberUtil.numberScale2((/* 当前综合二类工市场价*/rgfMarketPriceLevel2 - tempSettings.PRICE_DE_ZONGHEERLEIGONG) / tempSettings.PRICE_DE_ZONGHEERLEIGONG);


    //循环总价措施费用定额
    for (const costDe of costDeList) {
      changeDeIds.add(costDe.sequenceNbr);
      let zjcs = deListZjcs.find(k => k.sequenceNbr === costDe.sequenceNbr);
      let deMathBase = 0;
      if (!ObjectUtils.isEmpty(zjcs)) {
        //该费用定额在其他总价措施下，正常计算
        //获取计算基数
        deMathBase = costBase[costDe.rateName];
        if (ObjectUtils.isEmpty(deMathBase)) {
          deMathBase = 0;
        }
        // 判断是否计算基数乘以0.5
        //雨季施工增加费   冬季施工增加费
        if (!ObjectUtils.isEmpty(zjcsCostMathCache)) {
          if (zjcsCostMathCache.heatingFee) {
            if (costDe.zjcsClassCode === '1') {
              deMathBase = NumberUtil.multiply(deMathBase, 0.5);
            }
          }
          if (zjcsCostMathCache.rainySeasonConstruction) {
            if (costDe.zjcsClassCode === '2') {
              deMathBase = NumberUtil.multiply(deMathBase, 0.5);
            }
          }
        }
      }
      //获取费用定额的人材机数据
      let costDeRcjs = rcjList.filter(k => k.deId === costDe.sequenceNbr);
      //人材机明细：合计数量=定额工程量*消耗量*计算基数
      costDeRcjs.forEach(k => {
        if (k.materialCode == ConstantUtil.CODE_RGF_ADJUST) {
          k.marketPrice = priceDifferenceProjectRcjMarketPrice;
        }
        //合计数量
        k.totalNumber = NumberUtil.numberScale(NumberUtil.divide100(NumberUtil.multiplyParams(k.resQty, costDe.quantity, deMathBase)), 4);
        //合价
        if (k.materialCode === ConstantUtil.CODE_RGF_ADJUST) {
          // 如果是费用定额的措施中人工费调整这一条人材机  那么“合价”=该条费用人材机的“合计数量”*其“市场价”
          k.total = NumberUtil.numberScale(NumberUtil.numberScale(NumberUtil.multiply(k.totalNumber, k.marketPrice)), 2);
        } else {
          k.total = NumberUtil.numberScale(NumberUtil.multiply(k.totalNumber, k.dePrice), 2);
        }
      });
      //赋值计算基数
      costDe.formula = deMathBase;
      costDe.caculatePrice = 1;
      costDe.baseNum = { def: deMathBase };

      // 检测是否需要措施中人工费调整
      await this.service.yuSuanProject.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(constructId, singleId, unitId, costDe);
      // //计算单价构成
      this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(unit.constructId, unit.spId, unit.sequenceNbr,
        costDe.sequenceNbr, true, PricingFileFindUtils.getModuleData(constructId, singleId, unitId, costDe.sequenceNbr), false);
    }
  }

  /**
   * 安文费记取
   * @param arg
   * @return {Promise<void>}
   */
  async awfCost(arg, changeDeIds) {
    if (ObjectUtils.isEmpty(changeDeIds)) {
      changeDeIds = new Set();
    }

    let { constructId, singleId, unitId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

    // 删除历史的安文费费用定额
    await this.service.yuSuanProject.constructCostMathService.clearCostDe(unit, [DePropertyTypeConstant.AWF_DE], changeDeIds);
    if (unit.fixationSecurityFee == '1') {
      // 如果设置了固定安文费  就不再记取安文费了
      return;
    }

    // 获取安文费清单
    const awfQdList = this.service.yuSuanProject.constructCostMathService.zjcsList(constructId, singleId, unitId, ConstructionMeasureTypeConstant.AWF);
    if (ObjectUtils.isEmpty(awfQdList)) {
      return;
    }
    // 获取安文费基数定额
    let awfBaseDeList = this.service.yuSuanProject.constructCostMathService.getAwfUseDe(unit);
    if (ObjectUtils.isNotEmpty(awfBaseDeList)) {
      // 筛选不属于总价包干的定额
      awfBaseDeList = awfBaseDeList.filter(async item => !(await this.checkIsZjbg(constructId, singleId, unitId, item)));
    }
    //将基数定额根据取费文件分组
    let baseDeGroupByCostMajorName = ArrayUtil.group(awfBaseDeList, 'costMajorName');
    const szgcValue = baseDeGroupByCostMajorName.get(ConstantUtil.TITLE_WITH_MARJOR_PROJECT);
    if (ObjectUtils.isNotEmpty(szgcValue)) {
      // 如果根据costMajorName分组后存在“随主工程”  就需要把随主工程的这一组转为单位工程的主取费文件
      let mainFeeFile = PricingFileFindUtils.getMainFeeFileByUnitObj(unit);
      baseDeGroupByCostMajorName.set(mainFeeFile.feeFileName, szgcValue);
    }
    // 获取安文费费用定额
    const awfCostDeList = await this.service.yuSuanProject.constructCostMathService.getAwfCostDe(unit);
    if (ObjectUtils.isEmpty(awfCostDeList)) {
      return;
    }
    for (const awfQd of awfQdList) {
      const qd = this.service.yuSuanProject.constructCostMathService.confirmQdData(awfQd, unit, ConstructionMeasureTypeConstant.AWF);
      if (ObjectUtils.isEmpty(qd)) {
        continue;
      }
      // 在当前的安文费清单下添加安文费费用定额
      await this.service.yuSuanProject.constructCostMathService.hanldeAddAwfCostDe(awfCostDeList, baseDeGroupByCostMajorName, unit, qd);
    }
  }

  /**
   * 检查一个定额是不是总价包干的结算方式
   * 这个方法中的settlementMethod == 2   表示是不是总价包干
   * 这是结算的一个兼容代码  只有在结算中的时候才会生效  其他时候settlementMethod应该都是undefined
   */
  async checkIsZjbg(constructId, singleId, unitId, node) {
    if (ObjectUtils.isEmpty(node)) {
      return false;
    }
    if (node.kind == BranchProjectLevelConstant.de) {
      if (ObjectUtils.isNotEmpty(node.parent)) {
        return node.parent.settlementMethod == 2;
      } else {
        const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        const nodeParent = unit.measureProjectTables.getNodeById(node.parentId);
        return nodeParent.settlementMethod == 2;
      }
    } else {
      if (node.kind == BranchProjectLevelConstant.qd) {
        return node.settlementMethod == 2;
      }
    }
    return false;
  }

  async fxtjCostMatch(args, changeDeIds) {
    let { constructId, singleId, unitId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    // 根据缓存判断哪些房修土建费用需要自动计取
    if (ObjectUtils.isEmpty(unit.fxtjCache)) {
      return;
    }
    args.changeDeIds = changeDeIds;
    let fxtjMatchFlagArr = [];
    if (ObjectUtils.isNotEmpty(unit.fxtjCache.cgCache)) {
      fxtjMatchFlagArr.push(FxtjCostConstants.CG);
    }
    if (ObjectUtils.isNotEmpty(unit.fxtjCache.czyxCache)) {
      fxtjMatchFlagArr.push(FxtjCostConstants.CZYS);
    }
    if (ObjectUtils.isNotEmpty(unit.fxtjCache.zxxjxCache)) {
      fxtjMatchFlagArr.push(FxtjCostConstants.ZXXJX);
    }
    if (ObjectUtils.isNotEmpty(unit.fxtjCache.gcsdfCache)) {
      fxtjMatchFlagArr.push(FxtjCostConstants.GCSDF);
    }
    if (ObjectUtils.isEmpty(fxtjMatchFlagArr)) {
      return;
    }
    // 依次自动记取不同的房修土建费用
    for (const fxtjMatchFlag of fxtjMatchFlagArr) {
      args.feeType = fxtjMatchFlag;
      await new FxtjCostMatchContext(fxtjMatchFlag).autoCostMatch(args);
    }
  }

}

AutoCostMathService.toString = () => '[class AutoCostMathService]';
module.exports = AutoCostMathService;
