'use strict';

const {Service, Log} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {SqlUtils} = require("../utils/SqlUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {BaseDeRelationVariableDescribe2022} = require("../model/BaseDeRelationVariableDescribe2022");
const {TaxCalculationMethodEnum} = require("../enum/TaxCalculationMethodEnum");

/**
 * 国标定额service
 */
class BaseDeRelationVariableDescribe2022Service extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseDeRelationVariableDescribe2022Dao = this.app.appDataSource.manager.getRepository(BaseDeRelationVariableDescribe2022);


    /**
     * 获取父定额的规则组id
     */
    async queryGroupiD(parentDe){
        //获取父定额的规则组id
        let result= await this.baseDeRelationVariableDescribe2022Dao.findOne({
            where: {
                deId: parentDe.standardId
            }
        });
        return  ObjectUtils.isNotEmpty(result)?result.groupid:null;
    }

}

BaseDeRelationVariableDescribe2022Service.toString = () => '[class BaseDeRelationVariableDescribe2022Service]';
module.exports = BaseDeRelationVariableDescribe2022Service;
