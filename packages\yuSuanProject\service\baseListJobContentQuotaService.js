'use strict';

const {Service, Log} = require('../../../core');
const {BaseListJobContentQuota} = require("../model/BaseListJobContentQuota");
const {ObjectUtils} = require("../utils/ObjectUtils");

/**
 * 清单指引service
 */
class BaseListJobContentQuotaService extends Service {

    constructor(ctx) {
        super(ctx);
        this._baseListJobContentQuotaDao = this.app.appDataSource.manager.getRepository(BaseListJobContentQuota);
    }

    /**
     * 清单指引定额列表
     * @param baseListModel
     * @return {Promise<jobContent: BaseListJobContentQuota[]>}
     */
    async listQdGuideDeArray(baseListModel) {
        let listId = baseListModel.sequenceNbr; // 清单id
        // 根据清单id查清单指引
        let qdGuideDeArray = await this._baseListJobContentQuotaDao.find({
            where: {listId: listId},
            order: {sortNo: 'ASC'}
        });
        if (ObjectUtils.isEmpty(qdGuideDeArray)) {
            return [];
        }
        // 过滤掉jobContext为空的数据
        qdGuideDeArray = qdGuideDeArray.filter(i => ObjectUtils.isNotEmpty(i.jobContent));
        if (ObjectUtils.isEmpty(qdGuideDeArray)) {
            return [];
        }

        // 根据清单工作内容分组
        const groupMap = qdGuideDeArray.reduce((result, currentItem) => {
            // 清单的工作内容作为map的key
            (result[currentItem.jobContent] = result[currentItem.jobContent] || []).push(currentItem);
            return result;
        }, {});

        // 注释原map结构 return groupMap;
        // 返回前端更容易适配的结构。
        return Object.keys(groupMap).map(key => {
            return {
                jobContent: key,
                children: groupMap[key]
            }
        })
    }


}

BaseListJobContentQuotaService.toString = () => '[class BaseListJobContentQuotaService]';
module.exports = BaseListJobContentQuotaService;
