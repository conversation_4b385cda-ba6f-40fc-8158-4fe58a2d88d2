node_modules
out/
logs/
run/
.idea/
package-lock.json
data/
.vscode/launch.json
public/electron/
public/dist/
frontend/dist
frontendUi/dist
frontendUi/components.d.ts
build/extraResources/pricing.db
build/extraResources/shenHeYuSuanProject.db
build/extraResources/PreliminaryEstimate.db
build/extraResources/jieSuanProject.db
build/extraResources/jre
build/extraResources/pdfUtil.jar
electron/model/*.js.map
electron/model/*.js
electron/model/loadPrice/*.js.map
electron/model/loadPrice/*.js
packages/PreliminaryEstimate/models/**/*.js
packages/PreliminaryEstimate/models/*/*.js
packages/PreliminaryEstimate/models/**/*.js.map
packages/PreliminaryEstimate/models/*/*.js.map
packages/PreliminaryEstimate/controller/conversionDeController.js
packages/PreliminaryEstimate/controller/conversionDeController.js.map
packages/PreliminaryEstimate/service/gsConversionDeProcess.js
packages/PreliminaryEstimate/service/gsConversionDeProcess.js.map
packages/PreliminaryEstimate/service/gsConversionDeService.js
packages/PreliminaryEstimate/service/gsConversionDeService.js.map
packages/PreliminaryEstimate/enums/ConversionSourceEnum.js
packages/PreliminaryEstimate/enums/ConversionSourceEnum.js.map
data-source.js
data-source.js.map
test
*.js.map

electron/service/conversionDeService.js
electron/service/conversionDeProcess.js
electron/controller/conversionDeController.js
electron/enum/ConversionSourceEnum.js
electron/enum/*.js.map
packages/shenHeYuSuanProject/model/*.js.map
packages/shenHeYuSuanProject/model/*.js
packages/jieSuanProject/model/*.js.map
packages/jieSuanProject/model/*.js

packages/yuSuanProject/model/*.js.map
packages/yuSuanProject/model/*.js
packages/yuSuanProject/model/loadPrice/*.js.map
packages/yuSuanProject/model/loadPrice/*.js
packages/yuSuanProject/service/conversionDeService.js
packages/yuSuanProject/service/conversionDeProcess.js
packages/yuSuanProject/controller/conversionDeController.js
packages/yuSuanProject/enum/ConversionSourceEnum.js
packages/yuSuanProject/enum/*.js.map
packages/yuSuanProject/kernel/*.js
packages/yuSuanProject/rcj_new/*.js
packages/yuSuanProject/template/feebuild/*.js
packages/yuSuanProject/template/feebuild/*.js.map


*.lock
