class ExcelSheetList{

}


ExcelSheetList.TBXZ = "填表须知";
ExcelSheetList.FM = "封面1 工程量清单";
ExcelSheetList.FY = "扉页1 工程量清单";
ExcelSheetList.SHEET1 = "表1-1 工程量清单编制说明";
ExcelSheetList.SHEET3 = "表1-3 工程项目总价表";
ExcelSheetList.SHEET5 = "表1-5 单位工程费汇总表";
ExcelSheetList.SHEET6 = "表1-6 分部分项工程量清单与计价表";
ExcelSheetList.SHEET6_1 = "分部分项工程量清单";
ExcelSheetList.SHEET6_2 = "计价表";
ExcelSheetList.SHEET7 = "表1-7 单价措施项目工程量清单与计价表";
ExcelSheetList.SHEET7_1 = "单价措施项目工程量清单";
ExcelSheetList.SHEET7_2 = "计价表";
ExcelSheetList.SHEET8 = "表1-8 总价措施项目清单与计价表";
ExcelSheetList.SHEET8_1 = "总价措施";
ExcelSheetList.SHEET8_2 = "计价表";
ExcelSheetList.SHEET9 = "表1-9 其他项目清单与计价表";
ExcelSheetList.SHEET10 = "表1-10 暂列金额明细表";
ExcelSheetList.SHEET11 = "表1-11 暂估价表";
ExcelSheetList.SHEET12 = "表1-12 总承包服务费计价表";
ExcelSheetList.SHEET13 = "表1-13 计日工表";
ExcelSheetList.SHEET14 = "表1-14 招标人供应材料、设备明细表";
ExcelSheetList.SHEET15 = "表1-15 主要材料、设备明细表";
ExcelSheetList.SHEET16 = "材料、机械、设备增值税计算表";
ExcelSheetList.SHEET17 = "材料、机械、设备增值税计算表（实体）";
ExcelSheetList.SHEET18 = "材料、机械、设备增值税计算表（措施）";
ExcelSheetList.SHEET19 = "增值税进项税额计算汇总表";

ExcelSheetList.SINGSHEET4 = "表1-4 单项工程费汇总表";
ExcelSheetList.SINGSHEET5 = "表1-5 单项工程费汇总表";

ExcelSheetList.VISACLAIMS = "签证与索赔计价表";


module.exports = {
    ExcelSheetList: ExcelSheetList
};