{"$id": "https://example.com/person.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "title": "UpcTemplate", "type": "object", "properties": {"qfCode": {"type": "string", "description": "取费模板code", "required": true}, "standard": {"type": "string", "description": "12标准还是22", "required": true}, "type": {"type": "string", "description": "费用类型别名称", "required": true}, "typeCode": {"type": "string", "description": "费用类型别", "required": true}, "code": {"type": "string", "description": "费用代号", "required": true}, "name": {"type": "string", "description": "名称", "required": true}, "caculateBase": {"type": "string", "description": "计算基数", "required": true}, "desc": {"type": "string", "description": "描述", "required": true}, "rate": {"description": "费率", "required": true}}}