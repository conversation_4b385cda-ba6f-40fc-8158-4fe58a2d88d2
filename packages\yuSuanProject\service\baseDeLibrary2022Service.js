'use strict';

const {Service, Log} = require('../../../core');
const {Snowflake} = require("../utils/Snowflake");
const {BaseDeLibrary2022} = require("../model/BaseDeLibrary2022");
const {ObjectUtils} = require("../utils/ObjectUtils");

/**
 * 定额册 service
 * @class
 */
class BaseDeLibrary2022Service extends Service {

    constructor(ctx) {
        super(ctx);
        this._baseDeLibrary2022Dao = this.app.appDataSource.manager.getRepository(BaseDeLibrary2022);
    }

    /**
     * 根据定额标准查定额册
     * @param deStandardId
     * @returns {BaseDeLibrary2022[]|Error}
     */
    async listByDeStandard(deStandardId) {
        if (ObjectUtils.isEmpty(deStandardId)) {
            throw new Error("必传参数定额标准为空");
        }

        return await this._baseDeLibrary2022Dao.find({
            where: {deStandardId: deStandardId},
            order: {sortNo: "ASC"}
        });
    }

    /**
     * 根据定额册编码查定额册
     * @param libraryCode
     * @returns {BaseDeLibrary2022|Error}
     */
    async getByLibraryCode(libraryCode) {
        if (ObjectUtils.isEmpty(libraryCode)) {
            throw new Error("必传参数定额册编码为空");
        }

        return await this._baseDeLibrary2022Dao.findOneBy({libraryCode: libraryCode});
    }

}

BaseDeLibrary2022Service.toString = () => '[class BaseDeLibrary2022Service]';
module.exports = BaseDeLibrary2022Service;
