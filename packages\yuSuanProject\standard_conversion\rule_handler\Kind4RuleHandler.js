const RuleHandler = require("./ruleHandler");

class Kind4RuleHandler extends RuleHandler {
    deCodeUpdateInfo() {
        let blackSubArray = [];

        for (let handler of this.rule.mathHandlers) {
            blackSubArray.push(`[${handler.oriMath}]`);
        }

        return {redStr: null, blackStr: blackSubArray.join(",")}
    }

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = this.formatConversionExplain(this.rule.relation);
    }

    deNameUpdateInfo(rule){
        return this.formatConversionExplain(rule.relation);
    }
}

module.exports = Kind4RuleHandler;
