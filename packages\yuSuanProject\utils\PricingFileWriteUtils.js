const os = require("os");
const AdmZip = require("adm-zip");
const crypto = require("crypto");
const {writeFile} = require("fs");
const {ConstructProject} = require("../model/ConstructProject");
const {Snowflake} = require("../utils/Snowflake");
const {getRepository} = require("typeorm");
const password = 'myPassword';
const fs = require('fs');
const {PricingFileFindUtils} = require("./PricingFileFindUtils");
const {ObjectUtils} = require("./ObjectUtils");
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const {NumberUtil} = require("./NumberUtil");
const JSZip = require('jszip');
const {ResponseData} = require("./ResponseData");
const {DateUtils} = require("./DateUtils");
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");

/**
 * ysf文件写入操作工具类
 */
class PricingFileWriteUtils {



    /**
     * 将文件对象数据写入到内存中
     * @param obj
     */
    writeToMemory(obj) {
        global.constructProject = global.constructProject || {};
        global.constructProject[obj.sequenceNbr]= {isUpdate: true, proJectData: obj};
    }
    /**
     * 只有在初始化工程项目的时候才会使用该方法
     * @param obj
     * @return {*}
     */
    async creatYsfFile(obj) {
        if (!obj instanceof ConstructProject) {
            throw new Error("参数有误");
        }
        obj.fileCode = Snowflake.nextId();
        let data = ObjectUtils.toJsonString(obj);
        //data = this.encryptData(data);
        // 将数据写入文件
        await writeFile(obj.path, data, (err) => {
            if (err) {
                console.error('写入文件时发生错误：', err);
                return;
            }
            console.log('数据已成功写入文件！');
        });
        return obj;
    }

    /**
     * 更新ysf文件
     * @param obj
     * @return {Promise<*>}
     */
    async updateYsfFile(obj) {
        if (!obj instanceof ConstructProject) {
            throw new Error("参数有误");
        }
        let data = ObjectUtils.toJsonString(obj);
        // 将数据写入文件
        await writeFile(obj.path, data, (err) => {
            if (err) {
                console.error('写入文件时发生错误：', err);
                return;
            }
            console.log('数据已成功写入文件！');
        });
        return obj;
    }

    /**
     * 写入编制说明数据
     * @param obj 编制说明对象
     * @param levelType 工程类型  1：工程 2：单项 3：单位
     * @param constructId 工程ID
     * @param unitId 单位ID
     * @return {Promise<void>}
     */
    async writeOrganizationInstructions(obj,levelType, constructId, singleId,unitId) {
        let fileObj = await PricingFileFindUtils.getProjectObjById(constructId);
        if (1 == levelType) {
            //工程项目
             fileObj.organizationInstructions = obj;
        }
        if (2 == fileObj.biddingType){
            //单位工程
            let unitProjects = fileObj.unitProjects;
            let unitProject = unitProjects.find((item ) => item.sequenceNbr === unitId);
            unitProject.organizationInstructions = obj;
        }else {
            //单项
            let singleProject = PricingFileFindUtils.getOneFromSingleProjects(fileObj.singleProjects, singleId);
            let unitProject = singleProject.unitProjects.find((item ) => item.sequenceNbr === unitId);
            unitProject.organizationInstructions =obj;
        }

    }

    /**
     * 新建单位工程信息
     * @param obj 单位对象
     * @param constructId 工程项目ID
     * @param singleId 单项工程ID
     * @return {Promise<void>}
     */
    async writeUnitProJect(obj, constructId,singleId) {
        let fileObj = await PricingFileFindUtils.getProjectObjById(constructId);

        if (2 ==fileObj.biddingType){
            fileObj.unitProjects.push(obj);
        }else {
            let singleProject = PricingFileFindUtils.getOneFromSingleProjects(fileObj.singleProjects, singleId);
            if(singleProject.unitProjects){
                singleProject.unitProjects.push(obj);
            }else {
                let unitProjectsList =new Array();
                unitProjectsList.push(obj)
                singleProject.unitProjects=unitProjectsList
            }

        }
        //更新文件
        await this.creatYsfFile(fileObj);
    }





    /**
     * 更新工程项目基本信息
     * @param projectOverviewList
     * @param constructId
     * @returns {Promise<void>}
     */
    async updateConstructProjectJBXX(projectOverviewList,constructId){
        let ysfObj =await PricingFileFindUtils.getProjectObjById(constructId);
        ysfObj.constructProjectJBXX = projectOverviewList
        //更新文件
        //await this.writeToMemory(ysfObj);
    }

    /**
     * 更新单位工程项目费用代码
     * @param unitCostCodePrices
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async updateUnitCostCodePrice(unitCostCodePrices, constructId, singleId, unitId) {
        let unitProject =await PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unitProject.unitCostCodePrices = unitCostCodePrices;
    }

    /**
     * 更新单位工程费用汇总
     * @param unitCostSummarys
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async updateUnitCostSummary(unitCostSummarys, constructId, singleId, unitId) {
        let unitProject =await PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unitProject.unitCostSummarys = unitCostSummarys;

    }


    /**
     * 更新单位项目基本信息/工程特征
     * @param projectOverviewList
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type （0：基本信息1：工程特征）
     * @returns {Promise<void>}
     */
     updateUnitProjectJBXXOrXMTZ(projectOverviewList,constructId,singleId,unitId,type){
        let constructProject = PricingFileFindUtils.getProjectObjById(constructId);
        if(constructProject.biddingType === ConstructBiddingTypeConstant.unitProject){
            //如果是 单位工程项目 直接在工程项目下筛选 单位工程
            let unitProject = constructProject.unitProjects;
            if(type===0){
                unitProject.unitJBXX =projectOverviewList
            }else {
                unitProject.unitGCTZ =projectOverviewList
            }
        }else {
            let unitProject = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            if(type===0){
                unitProject.unitJBXX =projectOverviewList
            }else {
                unitProject.unitGCTZ =projectOverviewList
            }
        }
    }

    /**
     *
     * @param constructId
     * @param singleId
     */
    countSingleProject(constructId,spId){
        let singleProject = PricingFileFindUtils.getSingleProject(constructId,spId);

        if(ObjectUtils.isEmpty(singleProject)){
            return
        }

        let unitProjects = singleProject.unitProjects;
        if(ObjectUtils.isEmpty(unitProjects)){
            return
        }

        let total = 0;
        let gfee  = 0;
        let safeFee  = 0;
        let sbfTax = 0;
        for (let i = 0; i < unitProjects.length; i++) {
            let unitProject = unitProjects[i];
            total = NumberUtil.add(total,unitProject.gczj);
            gfee = NumberUtil.add(gfee,unitProject.gfee);
            safeFee = NumberUtil.add(safeFee,unitProject.safeFee);
            sbfTax = NumberUtil.add(sbfTax,unitProject.sbfsj);
        }
        singleProject.total = NumberUtil.numberScale2(total);
        singleProject.gfee = NumberUtil.numberScale2(gfee);
        singleProject.safeFee = NumberUtil.numberScale2(safeFee);
        singleProject.sbfTax = NumberUtil.numberScale2(sbfTax);

    }

    //从下往上重新计算单项工程的属性值
    countSingleProjectFromBottom(constructId,singleId){


        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);

        if(ObjectUtils.isEmpty(singleProject)){
            return
        }

        let unitProjects = singleProject.unitProjects;
        //如果是中间层级的单项 累计下一层级的单项之和
        if(ObjectUtils.isEmpty(unitProjects)&& ObjectUtils.isNotEmpty(singleProject.subSingleProjects)){
            let total = 0;
            let gfee  = 0;
            let safeFee  = 0;
            let sbfTax = 0;
            for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
                let subSingleProject = singleProject.subSingleProjects[i];
                total = NumberUtil.add(total,subSingleProject.total);
                gfee = NumberUtil.add(gfee,subSingleProject.gfee);
                safeFee = NumberUtil.add(safeFee,subSingleProject.safeFee);
                sbfTax = NumberUtil.add(sbfTax,subSingleProject.sbfTax);
            }
            singleProject.total = NumberUtil.numberScale2(total);
            singleProject.gfee = NumberUtil.numberScale2(gfee);
            singleProject.safeFee = NumberUtil.numberScale2(safeFee);
            singleProject.sbfTax = NumberUtil.numberScale2(sbfTax);

        }else if (ObjectUtils.isNotEmpty(unitProjects)){   //如果是含有单位工程的这一层级单项 进行计算
            let total = 0;
            let gfee  = 0;
            let safeFee  = 0;
            let sbfTax = 0;
            for (let i = 0; i < unitProjects.length; i++) {
                let unitProject = unitProjects[i];
                total = NumberUtil.add(total,unitProject.gczj);
                gfee = NumberUtil.add(gfee,unitProject.gfee);
                safeFee = NumberUtil.add(safeFee,unitProject.safeFee);
                sbfTax = NumberUtil.add(sbfTax,unitProject.sbfsj);
            }
            singleProject.total = NumberUtil.numberScale2(total);
            singleProject.gfee = NumberUtil.numberScale2(gfee);
            singleProject.safeFee = NumberUtil.numberScale2(safeFee);
            singleProject.sbfTax = NumberUtil.numberScale2(sbfTax);
        }

        //找到单项的父级单项id
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        let parentSpId = this.getParentSpId(projectObjById.singleProjects,null,singleId);

        this.countSingleProjectFromBottom(constructId,parentSpId);
    }

    //寻找目标单项的父级单项Id  第一次调用 parentId置为null
    //selfId不能为工程项目下的第一层级单项  否则返回null
    getParentSpId(subSingleProjects,parentId,selfId) {
        for (let i = 0; i < subSingleProjects.length; i++) {
            let singleProject = subSingleProjects[i];
            if (singleProject.sequenceNbr == selfId) {
                return parentId;
            }
            if (ObjectUtils.isNotEmpty(singleProject.subSingleProjects)) {
                let result = this.getParentSpId(singleProject.subSingleProjects,singleProject.sequenceNbr,selfId);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }
    

    //加密数据
    encryptData(data) {
        const cipher = crypto.createCipher('aes-256-cbc', password);
        let encryptedData = cipher.update(data, 'utf8', 'hex');
        encryptedData += cipher.final('hex');
        return encryptedData;
    }

    // 解密数据
    decryptData(encryptedData) {
        const decipher = crypto.createDecipher('aes-256-cbc', password);
        let decryptedData = decipher.update(encryptedData, 'hex', 'utf8');
        decryptedData += decipher.final('utf8');
        return decryptedData;
    }


    writeUserHistoryFile(path,data) {
        fs.writeFileSync(path, ObjectUtils.toJsonString(data));
    }

    /**
     * 写入用户文件列表历史数据
     * @param path
     * @param data
     */
    writeUserHistoryListFile(obj) {
        return ProjectFileUtils.writeUserHistoryListFile(obj);
        // //获取用户文件列表数据
        // let userHistoryData = PricingFileFindUtils.userHistoryData();
        // let idKey = PricingFileFindUtils.userInfoData();
        //
        // let userHistoryFileList = userHistoryData[idKey];
        // if (ObjectUtils.isEmpty(userHistoryFileList)){
        //     userHistoryData[idKey] = [];
        // }
        //
        // let result = userHistoryData[idKey].filter(k =>k.path === obj.path && k.sequenceNbr ===obj.sequenceNbr);
        // //历史记录路径
        // let userPath = PricingFileFindUtils.userHistoryDataPath();
        //
        // //如果是新打开的一个文件 则需要先入记录到表里面
        // if (ObjectUtils.isEmpty(result)){
        //     //重新刷新所有的项目ID
        //     let constructId = Snowflake.nextId();
        //     obj.sequenceNbr = constructId;
        //     // obj.path = obj.path;
        //     ObjectUtils.updatePropertyValue(obj, 'constructId', constructId);
        //     result = {
        //         sequenceNbr:constructId,
        //         constructName:obj.constructName,
        //         path:obj.path,
        //         openTime:DateUtils.now('YYYY-MM-DD HH:mm:ss')
        //     };
        //     userHistoryData[idKey].push(result);
        // }else {
        //     result[0].openTime = DateUtils.now('YYYY-MM-DD HH:mm:ss');
        // }
        // //数据写入历史文件中
        // this.writeUserHistoryFile(userPath,userHistoryData);
        // return obj;
    }


}

module.exports = {
    PricingFileWriteUtils: new PricingFileWriteUtils()
};
