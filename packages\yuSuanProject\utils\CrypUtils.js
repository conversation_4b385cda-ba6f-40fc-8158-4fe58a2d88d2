
const {ObjectUtil} = require("../../../common/ObjectUtil");
const crypto = require("crypto");

class CryptoUtils{
    static algorithm = "aes-256-cbc";
    static key = Buffer.from("kldsjglkm*(jkfjriwe9sdl<>8995849", "utf8");
    static iv = Buffer.from("fdsl(*U*()R94859", "utf8");
    static inputEncoding = "utf8";
    static outputEncoding = "hex";
    // static outputEncoding = "latin1";
    // static outputEncoding = "base64";
    objectHash(objJson,sequenceNbr,flag) {
        //let startTime = performance.now();
        const hash = crypto.createHash('sha1');
        hash.update(objJson);
        let value = hash.digest('hex');
        // let endTime = performance.now();
        // let executionTime = endTime - startTime;
        // console.log('值是'+value);
        // console.log('HASD时间：', executionTime.toFixed(2), '毫秒');
        if (!flag){
            return value;
        }
        if (ObjectUtil.isEmpty(global.editProMap)){
            global.editProMap = new Map();
        }
        global.editProMap.set(sequenceNbr,value);
        return value;
    }

//加密数据
//     encryptAESData(data) {
//         const cipher = crypto.createCipher(CryptoUtils.algorithm, CryptoUtils.aesPassword);
//         let encryptedData = cipher.update(data, 'utf8', 'hex');
//         encryptedData += cipher.final('hex');
//         return encryptedData;
//     }
//
//     // 解密数据
//     decryptAESData(encryptedData) {
//         const decipher = crypto.createDecipher('aes-256-cbc', CryptoUtils.aesPassword);
//         let decryptedData = decipher.update(encryptedData, 'hex', 'utf8');
//         decryptedData += decipher.final('utf8');
//         return decryptedData;
//     }

    //加密数据
    encryptAESData(content) {
        let cipher = crypto.createCipheriv(CryptoUtils.algorithm, CryptoUtils.key, CryptoUtils.iv);
        let crypted = cipher.update(content, CryptoUtils.inputEncoding, CryptoUtils.outputEncoding);
        crypted += cipher.final(CryptoUtils.outputEncoding);
        return crypted;
    }

    // 解密数据
    decryptAESData(content) {
        let decipher = crypto.createDecipheriv(CryptoUtils.algorithm, CryptoUtils.key, CryptoUtils.iv);
        let decoded = decipher.update(content, CryptoUtils.outputEncoding, CryptoUtils.inputEncoding);
        decoded += decipher.final(CryptoUtils.inputEncoding);
        return decoded;
    }
}

module.exports = {
    CryptoUtils: new CryptoUtils()
};

