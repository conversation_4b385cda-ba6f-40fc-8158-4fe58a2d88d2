const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const _ = require('lodash');
const {NumberUtil} = require("../../utils/NumberUtil");
const {fbFillBaseRule, fbFillRules} = require("./rules/fbAndOtherRule");
const {LifeFactory, Organ} = require("@valuation/rules-engine");
const {baseFn, ccodes} = require("./rules/chargecode");

/**
 * 计算分部费用
 */
class UPCCupmuteOther extends LifeFactory {
    constructor(fbId, constructId, singleId, unitId, allData) {
        super();
        this.fbId = fbId;
        this.fb = {};
        this.children = [];
        this.constructId = constructId;
        this.singleId = singleId;
        this.unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        this.allData = allData;
        this.upcTemplateList = [];
    }

    static getInstance({constructId, singleId, unitId, allData}, pointLine) {
        return new UPCCupmuteOther(pointLine.sequenceNbr, constructId, singleId, unitId, allData);
    }

    prepare() {
        this.fb = this.allData.getNodeById(this.fbId);
        this.children = this.fb ? this.fb.children : [];
        this.getUpcTemplateList();
    }

    cupmute() {
        for (const argumentsKey in this.upcTemplateGroupList) {
            this.realCupmute(this.upcTemplateGroupList[argumentsKey]);
        }
        this.unit.feeBuild[this.fb.sequenceNbr] = this.upcTemplateList;
        let rules={};
        /*this.analyzeBaseFn(fbFillBaseRule);
        this.analyzeCoreRules(fbFillRules);*/
        for (let baseIten in fbFillBaseRule){
            let itemFn = fbFillBaseRule[baseIten];
            rules[baseIten]=()=>{
                let result = itemFn();
                let cell =   { name: baseIten, column: result.cloumn, from: result.type=="item"?"runtime":result.type };
                if(result.kind){
                    cell.kind = result.kind;
                }
                return cell;
            };
        }
        for (let busItenkey in fbFillRules){
            let busItem = fbFillRules[busItenkey];
            let rule = Organ.create({name:busItenkey,description:busItem.name,gene:busItem.mathFormula});
            rules[busItenkey]=rule;
        }
        this.addRulesAndInitialize(rules);
        for (const key in fbFillRules) {
            this.fb[key] = NumberUtil.numberScale(this.create(key), 2);
        }

    }

    realCupmute(list) {
        let structure = {displayUnitPrice: 0, displayAllPrice: 0}
        for (const structureKey in list[0]) {
            structure[structureKey] = list[0][structureKey];
        }
        structure.name = structure.type;
        if (list) {
            let unitPrice = NumberUtil.numberScale(_.sumBy(list, 'unitPrice'), 2);
            let allPrice = NumberUtil.numberScale(_.sumBy(list, 'allPrice'), 2);
            structure = Object.assign(structure, {
                unitPrice,
                allPrice,
                displayUnitPrice: unitPrice,
                displayAllPrice: allPrice
            });
            this.upcTemplateList.push(structure);
        }
    }

    getUpcTemplateList() {
        if (!this.unit.feeBuild) {
            this.unit.feeBuild = {};
        }
        //合并单价构成数据
        let arr = [];
        if (this.children.length > 0) {
            this.children.forEach(item => {
                let list = this.unit.feeBuild[item.sequenceNbr];
                if (list && list.length > 0) {
                    list = list.filter(item => !_.isUndefined(item));
                    arr = arr.concat(list)
                }
            });
        }
        //按类型分组
        this.upcTemplateGroupList = _.groupBy(arr, item => item && item.typeCode);
    }
    getChildData({cloumn}) {
        let value = [];
        if (this.children.length > 0) {
            this.children.forEach(item => {
                if (typeof cloumn == "function") {
                    value.push(item);
                } else {
                    value.push(item[cloumn] || 0);
                }
            });
        }
        return value.length > 0 ? value : 0;
    }

    getCellValue(cell) {
        let {from:type, kind, column:cloumn}=cell
        let value = 0;
        switch (type) {
            case "QD": {
                value = this.getChildData({type, kind, cloumn});
                break;
            }
        }
        return value;
    }

}

module.exports = {
    UPCCupmuteOther
}
