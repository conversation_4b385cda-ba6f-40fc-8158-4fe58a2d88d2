"use strict"

const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const { Service } = require("../../../core")

const { BaseManageRate, BaseManageRate2022} = require("../model/BaseManageRate")

/**
 * 取费文件关联关系表 service
 * @class
 */
class BaseManageRateService extends Service {
	constructor(ctx) {
		super(ctx)
	}

	// /**
	//  * 根据RateCode查询数据
	//  * @param rateCode
	//  * @return {Promise<void>}
	//  */
	// async queryByRateCode(rateCode, precastRate="15%≤预制率＜30%") {
	// 	let result = await this.app.appDataSource
	// 		.getRepository(BaseManageRate)
	// 		.find({
	// 			where: {
	// 				rateCode: rateCode,
	// 			},
	// 		})
	// 	if (result.length == 1) {
	// 		return result[0]
	// 	}
	// 	let baseManageRate = result.find((item) => item.precastRate === precastRate)
	// 	return baseManageRate
	// }

	//taxCalculationMethod 计税方式
	async queryByQfCode(qfcode,unitIs2022, precastRate="15%≤预制率＜30%",taxCalculationMethod) {
		let result ;
		if (qfcode !="ZPSHNTJGGC") {
			result = await this.app.appDataSource
				.getRepository(unitIs2022 ? BaseManageRate2022 : BaseManageRate)
				.findOne({
					where: {
						qfCode: qfcode,
					},
				});
		}else {
			result = await this.app.appDataSource
				.getRepository(unitIs2022 ? BaseManageRate2022 : BaseManageRate)
				.findOne({
					where: {
						qfCode: qfcode,
						precastRate:precastRate
					},
				});

		}

		if (unitIs2022){
			//简易计税
			if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code){
				//管理费 费率
				result.managementFee1 = parseFloat(result.managementFee1Jyjs);
				result.managementFee2 = parseFloat(result.managementFee2Jyjs);
				result.managementFee3 = parseFloat(result.managementFee3Jyjs);

				//利润费率
				result.profit1 = parseFloat(result.profit1Jyjs);
				result.profit2 = parseFloat(result.profit2Jyjs);
				result.profit3 = parseFloat(result.profit3Jyjs);

			}else if (taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code){
				//一般计税

				//管理费 费率
				result.managementFee1 = parseFloat(result.managementFee1Ybjs);
				result.managementFee2 = parseFloat(result.managementFee2Ybjs);
				result.managementFee3 = parseFloat(result.managementFee3Ybjs);

				//利润费率
				result.profit1 = parseFloat(result.profit1Ybjs);
				result.profit2 = parseFloat(result.profit2Ybjs);
				result.profit3 = parseFloat(result.profit3Ybjs);
			}
		}

		return result
	}
}

BaseManageRateService.toString = () => "[class BaseManageRateService]"
module.exports = BaseManageRateService
