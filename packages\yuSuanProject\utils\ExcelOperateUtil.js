const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("./ObjectUtils");
const ExcelEnum = require("../enum/ExcelEnum");
const {DateUtils} = require("./DateUtils");
const XLSX = require('xlsx');
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const Excel = require("exceljs");
const xeUtils = require("xe-utils");
const {Snowflake} = require("../utils/Snowflake");
const _ = require('lodash');
class ExcelOperateUtil {

    constructor() {
        this.VER_2007 = 2007;
        this.VER_2003 = 2003;
    }

    /**
     * 判断cell是否在某个合并单元格中，如果存在返回合并单元格名字
     * @param merges
     * @param cell
     * @returns fan
     */
    async getMergeName(merges, cell) {
        let result = null;
        let {row, col} = cell;

        for (let r in merges) {
            let model = merges[r].model;
            if (row >= model.top && row <= model.bottom && col >= model.left && col <= model.right) {
                result = r;
                break;
            }
        }

        return result;
    }


    /**
     * 找到sheet中对应值的cell定位 如果有多个相同值 只返回第一个
     * @param worksheet
     * @param value
     * @returns
     */
    async findValueCell(worksheet, value) {
        let results = [];
        let merges = worksheet.merges;
        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });
        let filter = results.filter(k => k.value == value);

        return filter[0];
    }


    /**
     * 找到sheet中包含该值的cell定位 返回为集合形式 可能为多个
     * @param worksheet
     * @param containValue
     * @returns
     */
    async findContainValueCell(worksheet, containValue) {
        let results = [];
        let merges = worksheet.merges;
        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });
        let filter = results.filter(k => {
            if (k.value==null|| typeof k.value != "string") return false;
            try {
                return k.value.includes(containValue);
            } catch (e) {
                console.log(e.stackTrace);
            }
        });
        return filter;
    }

    /**
     * 获取workbook对象某一个sheet的合并单元格信息
     * @param workbook
     * @param sheetName
     * @returns
     */
    async getMerges(workbook, sheetName) {
        const worksheet = workbook.getWorksheet(sheetName);
        return worksheet._merges;
    }

    /**
     * 获取workbook对象的某一个sheet表
     * @param workbook
     * @param sheetName
     * @returns {Worksheet}
     */
    async getSheet(workbook, sheetName){
        return workbook.getWorksheet(sheetName);
    }

    /**
     * 根据excel路径  读取excel的某一个sheet
     * @param excelPath
     * @param sheetName
     * @returns {Promise<Worksheet>}
     */
    async read(excelPath, sheetName) {
        // read from a file
        let workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(excelPath)

        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(sheetName);
        return worksheet;
    }

    /**
     * 根据excel路径 读取不同excel版本的workbook对象
     * @param excelPath
     * @param options
     * @returns {Promise<any>}
     */
    async readToWorkBook(excelPath, options = {}) {
        // read from a file
        if(options.version === this.VER_2003){
            let workbook =  XLSX.readFile(excelPath);
            return workbook;
        }else{
            let workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(excelPath)
            return workbook;
        }
    }

    /**
     * 读取excel文件中某一个sheet的内容
     * @param excelPath  excel文件路径
     * @param sheetName  sheet名字
     * @param options {version：[this.VER_2003|this.VER_2007]}
     * @returns {Promise<*[]>} sheet中每行内容，List<Object>
     */
    async readSheetContent(excelPath, sheetName, options = {}){
        // read from a file
        return this.readSheetContentByWorkBook(await this.readToWorkBook(excelPath, options), sheetName, options)
    }

    /**
     * 读取excel对象某一个sheet的内容
     * @param workbook  excel workbook对象
     * @param sheetName  sheet名字
     * @param options {version：[this.VER_2003|this.VER_2007]}
     * @returns {*[]} sheet中每行内容，List<Object>
     */
    readSheetContentByWorkBook(workbook, sheetName, options = {}){
        if(options.version === this.VER_2003){
            return  this.readSheetContentByWorkBook2003(workbook, sheetName);
        }else{
            return this.readSheetContentByWorkBook2007(workbook, sheetName);
        }
    }

    /**
     * 读取2003版本excel对象某一个sheet的内容
     * @param workbook  excel workbook对象
     * @param sheetName  sheet名字
     * @returns {*[]} sheet中每行内容，List<Object>
     */
    readSheetContentByWorkBook2003(workbook, sheetName){
        //console.log(workbook);
        let sheetContent = workbook.Sheets[sheetName];
        if(ObjectUtils.isEmpty(sheetContent)){
            return [];
        }
        let results = [];

        for(let k in sheetContent){
            if(k.startsWith("!") || !sheetContent.hasOwnProperty(k)){
                continue;
            }

            let matches = k.match(/[a-zA-Z]+/);
            if(ObjectUtils.isEmpty(matches)){
                continue;
            }
            let colName = matches[0];
            let row = k.substring(colName.length);
            if(ObjectUtils.isEmpty(colName) || ObjectUtils.isEmpty(row)){
                continue;
            }

            colName = colName.toLowerCase();
            let col = colName.charCodeAt(0) - 97 + 1;

            results[row] = results[row] || new Map();
            results[row].set(col, sheetContent[k].w);
        }

        return results.filter(m => ObjectUtils.isNotEmpty(m));
    }

    /**
     * 读取2007版本excel对象某一个sheet的内容
     * @param workbook  excel workbook对象
     * @param sheetName  sheet名字
     * @returns {*[]} sheet中每行内容，List<Object>
     */
    readSheetContentByWorkBook2007(workbook, sheetName){
        let results = [];
        // 获取工作表
        const worksheet = workbook.getWorksheet(sheetName);
        if(ObjectUtils.isEmpty(worksheet)){
            return results;
        }

        const merges = worksheet._merges;

        worksheet.eachRow(row =>{
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                if(value instanceof  Date){
                    value = DateUtils.format(value);
                }
                let mergeName = null;
                if(isMerged){
                    mergeName = this.getMergeName(merges, cell);
                    if(mergeName != null && mergeName != address){
                        value = null;
                    }
                }
                //console.log(cell.isMerged, "  ", cell.address, ": ", cell.value)
                //console.log(isMerged, "  ", address, ": ", value, ", row: ", row, ", col: ", col)
                rowContentMap.set(col, value);
            })
            results.push(rowContentMap);
        });
        // console.log(results)
        return results;
    }

    /**
     * 行高自适应方法
     * @param workSheet
     * @param headArgs  对象里应包含 headStartNum 和 headEndNum属性 表示表头的起始和结束行号
     */
    async fitHeight(workSheet,headArgs){
        let  headStartNum = 0;
        let headEndNum = 0;
        if (headArgs != null) {
            headStartNum = headArgs['headStartNum'];
            headEndNum = headArgs['headEndNum'];
        }else {
            headArgs = {};
            headStartNum = 1;
            headEndNum = 4;
            headArgs['headStartNum'] = headStartNum;
            headArgs['headEndNum'] = headEndNum;
        }

        let mergeMap = new Map(Object.entries(workSheet._merges));
        let fontSize = 13;
        //行高自适应
        for(let i = headEndNum+1; i<=workSheet._rows.length; i++){
            let minHeight = 0;
            let fitRight = true;//这里预设为false 就会保留初始模板的空白行高度 为true针对空白行统统高度为0

            for (let j = 0; j < workSheet.getRow(i)._cells.length; j++) {
                let cell = workSheet.getRow(i)._cells[j];
                let celltextValue = cell.model.value;
                if (!celltextValue) {
                    continue;
                }
                fitRight = true;
                if (typeof celltextValue === 'number') {
                    celltextValue = String(celltextValue);
                }
                let contents;
                try {
                    contents = celltextValue.split("\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
                } catch (e) {
                    console.log(e.stackTrace);
                }
                let mergeName = await this.getMergeName(workSheet._merges, cell);
                let mergeLength = 0;//得到该cell的宽度大小
                if (mergeName != null) {
                    let value = mergeMap.get(mergeName).model;
                    for (let m = value.left; m <= value.right; m++) {
                        mergeLength +=workSheet.getRow(i)._cells[m-1]._column.width;
                    }
                } else {
                    mergeLength =cell._column.width;
                }
                // let rowWordNum = Math.trunc(mergeLengthRatio * ExcelEnum.A4Width / ((fontSize / 72) * 10)) //每一列能够存放的字数
                //rowWordNum若为0  说明该列的列宽很窄  会造成递归死循环
                let rowWordNum = Math.trunc(mergeLength / ((fontSize / 72) * 10)) //每一列能够存放的字数
                if(rowWordNum == 0) continue;
                let rowSpace = 2;//行间距
                let rowNumTotal = 0;
                for (let j = 0; j < contents.length; j++) {
                    let rowText = contents[j];
                    if (!rowText && rowText.length == 0) {
                        continue;
                    }
                    // "垂直运输费 ±0.00以下 四层以内"  类似这种问题 在excel中会展示三行 实际计算是两行  18/9 此时满除就多加一行
                    let rowNum = Math.ceil(rowText.length / rowWordNum);
                    //优化处理  如果单行字数超过五  考虑到单元格的两侧边界距离  实际每行能存放的字数进行减二
                    if (rowNum >= 2 && rowNum * rowWordNum == rowText.length) {
                        rowNum++;
                    }
                    rowNumTotal += rowNum;
                }
                let newMinHeight = ((fontSize) + rowSpace) * rowNumTotal+8;   //计算出该Cell列 的最小适应高度  加4是上下的总共边距

                if (minHeight < newMinHeight) {
                    minHeight = newMinHeight; //得到该行的最大行高
                }
            }
            if (fitRight) {
                workSheet.getRow(i).height = minHeight;
            }
        }
    }

    async copyRowsWithIndex(rowNumber,rowInsertNum,workSheet){
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let headArrayValues = [];
        let row = workSheet._rows[rowNumber-1];
        headArrayValues.push(row.values);
        workSheet.insertRows(rowInsertNum,headArrayValues,'o');
        await this.resetMerges(workSheet,rowInsertNum);
        let row1 = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < row1._cells.length; i++) {
            row1._cells[i].style = workSheet._rows[rowNumber-1]._cells[i].style;
        }
        row1.height = workSheet._rows[rowNumber-1].height;
        //遍历该行的所有合并单元格  按照该行的方式进行合并
        //当前插入行与模板行的行距
        let distanceRow = rowInsertNum - rowNumber;


        for (let m = 0;m<row1._cells.length;m++){
            //获取模板行的合并单元格
            let mergeName = await this.getMergeName(workSheet._merges,row._cells[m]);
            if (mergeName!=null){
                let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                if (rowInsertNum == bottom+distanceRow){
                    workSheet.unMergeCells([top+distanceRow,left,bottom+distanceRow,right]);
                    workSheet.mergeCells([top+distanceRow,left,bottom+distanceRow,right]);
                }
            }
        }

    }


    // async testDemo(constructId,singleId,unitId) {
    //
    //     const workbookData = {
    //         id: 'workbook-01',
    //         sheetOrder: ['sheet1'],
    //         name: 'universheet',
    //         locale: 'zhCN',
    //         styles: {
    //             kJ66Ef: {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 bg: {
    //                     rgb: 'rgb(208,208,208)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             ArRIyI: {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: null,
    //                     l: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 bg: {
    //                     rgb: 'rgb(208,208,208)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '7tGYlm': {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             SsagpX: {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     l: null,
    //                     r: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             fgkvbf: {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '8SPpMY': {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     l: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '-dwAwy': {
    //                 bd: {
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '2aFP8r': {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             kvJCu3: {
    //                 bd: {
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     l: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             NaHwuW: {
    //                 bd: {
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: null,
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             bfITOz: {
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //                 bd: {
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                 },
    //             },
    //             '7GJLOe': {
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 0,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //                 bd: {
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                 },
    //             },
    //             hHkIyp: {
    //                 bd: {
    //                     l: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     r: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     t: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                     b: {
    //                         cl: {
    //                             rgb: 'rgb(0,0,0)',
    //                         },
    //                         s: 1,
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '9leVoS': {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 bg: {
    //                     rgb: 'rgb(208,208,208)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             _OTDvu: {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //             },
    //             l0ddfj: {
    //                 bd: {
    //                     l: null,
    //                     t: null,
    //                 },
    //             },
    //             k02dan: {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             VkRi5j: {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             YuXRg4: {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             AOp4bI: {
    //                 bd: {
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             T07rvv: {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             ey5O3H: {
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 0,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //                 bd: {
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //             },
    //             wvpkVV: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 bg: {
    //                     rgb: 'rgb(208,208,208)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             D000zA: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //             },
    //             OeU1SF: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             B43KY6: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             VxHV_O: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             ix1m_k: {
    //                 bd: {
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '0hkq56': {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '4g_xR8': {
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 0,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //                 bd: {
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //             },
    //             N2xf_e: {
    //                 bd: {
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 bg: {
    //                     rgb: 'rgb(208,208,208)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             dyr2sf: {
    //                 bd: {
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //             },
    //             LOLbqo: {
    //                 bd: {
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             aUvkTT: {
    //                 bd: {
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '30Gtx1': {
    //                 bd: {
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '15uVvE': {
    //                 bd: {
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '2DTLNR': {
    //                 bd: {
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             lbWUPC: {
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 0,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //                 bd: {
    //                     l: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 3,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //             },
    //             bp19pG: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             a9Cz6q: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: null,
    //                 },
    //             },
    //             F6Ch1m: {
    //                 bd: {
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '4gFHC_': {
    //                 bd: {
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                     r: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             JXj7rd: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             uawV7s: {
    //                 bd: {
    //                     t: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             QWtcqT: {
    //                 bd: {
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                     r: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             xDp71G: {
    //                 bd: {
    //                     b: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '9ysczg': {
    //                 bd: {
    //                     b: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             UyqoYj: {
    //                 bd: {
    //                     t: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             DRu9e4: {
    //                 bd: {
    //                     t: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             NsDgCR: {
    //                 bd: {
    //                     t: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             hwHVeK: {
    //                 bd: {
    //                     b: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             wG7n7H: {
    //                 bd: {
    //                     b: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 8,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             M9iyos: {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             osL2Ej: {
    //                 bd: {
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             MHjJk8: {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 16,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '46oSUa': {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: null,
    //                 },
    //             },
    //             Q3kYlQ: {
    //                 bd: {
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             zH2s7e: {
    //                 bd: {
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             nyd2RT: {
    //                 bd: {
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 13,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             'RO3R-9': {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             '-Cmxo7': {
    //                 bd: {
    //                     t: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 9,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             IyklH5: {
    //                 bd: {
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     b: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: null,
    //                     t: null,
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             kD28gr: {
    //                 bd: {
    //                     b: {
    //                         s: 2,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 2,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 2,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //             nqOWTI: {
    //                 bd: {
    //                     b: {
    //                         s: 2,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     l: {
    //                         s: 1,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     r: {
    //                         s: 2,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                     t: {
    //                         s: 2,
    //                         cl: {
    //                             rgb: '#35322B',
    //                         },
    //                     },
    //                 },
    //                 cl: {
    //                     rgb: 'rgb(0, 0, 0)',
    //                 },
    //                 fs: 11,
    //                 ht: 2,
    //                 vt: 2,
    //                 bl: 0,
    //                 it: 0,
    //                 ff: '宋体',
    //                 tb: 1,
    //             },
    //         },
    //         sheets: {
    //             sheet1: {
    //                 id: 'sheet1',
    //                 cellData: {
    //                     0: {
    //                         0: {
    //                             s: '9leVoS',
    //                             v: '',
    //                             t: 1,
    //                         },
    //                         1: {
    //                             s: '_OTDvu',
    //                             t: 1,
    //                         },
    //                         2: {
    //                             s: '_OTDvu',
    //                             t: 1,
    //                         },
    //                         3: {
    //                             v: '',
    //                             t: 1,
    //                             s: '9leVoS',
    //                         },
    //                         4: {
    //                             s: '9leVoS',
    //                             t: 1,
    //                         },
    //                         5: {
    //                             v: '',
    //                             t: 1,
    //                             s: '9leVoS',
    //                         },
    //                         6: {
    //                             s: '9leVoS',
    //                             t: 1,
    //                         },
    //                         7: {
    //                             t: 1,
    //                             s: '9leVoS',
    //                         },
    //                     },
    //                     1: {
    //                         0: {
    //                             v: '`分部分项工程量清单与计价表`',
    //                             t: 1,
    //                             s: 'k02dan',
    //                         },
    //                         1: {
    //                             s: 'k02dan',
    //                         },
    //                         2: {
    //                             s: 'k02dan',
    //                         },
    //                         3: {
    //                             s: 'k02dan',
    //                         },
    //                         4: {
    //                             s: 'k02dan',
    //                         },
    //                         5: {
    //                             s: 'MHjJk8',
    //                         },
    //                         6: {
    //                             s: 'MHjJk8',
    //                         },
    //                         7: {
    //                             s: 'MHjJk8',
    //                         },
    //                         8: {
    //                             s: 'l0ddfj',
    //                         },
    //                     },
    //                     2: {
    //                         0: {
    //                             s: 'VkRi5j',
    //                             v: '`工程名称:`+{工程名称\\单项名称\\单位名称}',
    //                             t: 1,
    //                         },
    //                         1: {
    //                             s: '_OTDvu',
    //                         },
    //                         2: {
    //                             s: '_OTDvu',
    //                         },
    //                         3: {
    //                             s: '_OTDvu',
    //                         },
    //                         4: {
    //                             s: '46oSUa',
    //                         },
    //                         5: {
    //                             s: 'YuXRg4',
    //                             v: '`第`+{页码}+`页` + `共` +{总页数}+`页`',
    //                             t: 1,
    //                         },
    //                         6: {
    //                             s: 'YuXRg4',
    //                         },
    //                         7: {
    //                             s: 'VkRi5j',
    //                         },
    //                         8: {
    //                             s: 'l0ddfj',
    //                         },
    //                     },
    //                     3: {
    //                         0: {
    //                             s: 'AOp4bI',
    //                             v: '`序号`',
    //                             t: 1,
    //                         },
    //                         1: {
    //                             s: 'AOp4bI',
    //                             v: '`项目编码`',
    //                             t: 1,
    //                         },
    //                         2: {
    //                             s: 'AOp4bI',
    //                             v: '`项目名称`',
    //                             t: 1,
    //                         },
    //                         3: {
    //                             s: 'AOp4bI',
    //                             v: '`项目特征`',
    //                             t: 1,
    //                         },
    //                         4: {
    //                             s: 'AOp4bI',
    //                             v: '`单位`',
    //                             t: 1,
    //                         },
    //                         5: {
    //                             s: 'IyklH5',
    //                             v: '`工程数量`',
    //                             t: 1,
    //                         },
    //                         6: {
    //                             s: 'zH2s7e',
    //                             v: '`金额`',
    //                             t: 1,
    //                         },
    //                         7: {
    //                             s: 'Q3kYlQ',
    //                         },
    //                         8: {
    //                             s: 'l0ddfj',
    //                         },
    //                     },
    //                     4: {
    //                         0: {
    //                             s: 'AOp4bI',
    //                         },
    //                         1: {
    //                             s: 'AOp4bI',
    //                         },
    //                         2: {
    //                             s: 'AOp4bI',
    //                         },
    //                         3: {
    //                             s: 'AOp4bI',
    //                         },
    //                         4: {
    //                             s: 'AOp4bI',
    //                         },
    //                         5: {
    //                             s: 'AOp4bI',
    //                         },
    //                         6: {
    //                             v: '`综合单价`',
    //                             t: 1,
    //                             s: 'Q3kYlQ',
    //                         },
    //                         7: {
    //                             v: '`合价`',
    //                             t: 1,
    //                             s: 'Q3kYlQ',
    //                         },
    //                         8: {
    //                             s: 'l0ddfj',
    //                         },
    //                     },
    //                     5: {
    //                         0: {
    //                             v: '',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         1: {
    //                             v: '[BM]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         2: {
    //                             v: '[MC]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         3: {
    //                             v: '',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         4: {
    //                             v: '',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         5: {
    //                             v: '',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         6: {
    //                             v: '',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         7: {
    //                             v: '[ZHHJ]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         8: {
    //                             s: 'l0ddfj',
    //                         },
    //                     },
    //                     6: {
    //                         0: {
    //                             v: '{连打记录号}',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         1: {
    //                             v: '[BM]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         2: {
    //                             v: '[MC]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         3: {
    //                             v: '[XMTZ]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         4: {
    //                             v: '[DW]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         5: {
    //                             v: '[GCL]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         6: {
    //                             v: '[ZHDJ]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         7: {
    //                             v: '[ZHHJ]',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         8: {
    //                             s: 'l0ddfj',
    //                         },
    //                     },
    //                     7: {
    //                         0: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         1: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         2: {
    //                             v: '`本页小计`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         3: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         4: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         5: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         6: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         7: {
    //                             v: 'SUM([ZHHJ])',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         8: {
    //                             s: 'l0ddfj',
    //                         },
    //                     },
    //                     8: {
    //                         0: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         1: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         2: {
    //                             v: '`合 计`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         3: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         4: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         5: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         6: {
    //                             v: '`/`',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                         7: {
    //                             v: 'SUM([ZHHJ])',
    //                             t: 1,
    //                             s: 'AOp4bI',
    //                         },
    //                     },
    //                     9: {
    //                         0: {
    //                             v: '',
    //                             t: 1,
    //                             s: '9leVoS',
    //                         },
    //                         1: {
    //                             s: '9leVoS',
    //                         },
    //                         2: {
    //                             s: '9leVoS',
    //                         },
    //                         3: {
    //                             v: '',
    //                             t: 1,
    //                             s: '9leVoS',
    //                         },
    //                         4: {
    //                             s: '9leVoS',
    //                         },
    //                         5: {
    //                             v: '',
    //                             t: 1,
    //                             s: '9leVoS',
    //                         },
    //                         6: {
    //                             s: '9leVoS',
    //                         },
    //                         7: {
    //                             s: '9leVoS',
    //                         },
    //                     },
    //                 },
    //                 name: 'Sheet1',
    //                 hidden: 0,
    //                 rowCount: 10,
    //                 columnCount: 8,
    //                 tabColor: '',
    //                 zoomRatio: 1,
    //                 freeze: {
    //                     startRow: -1,
    //                     startColumn: -1,
    //                     ySplit: 0,
    //                     xSplit: 0,
    //                 },
    //                 scrollTop: 0,
    //                 scrollLeft: 0,
    //                 defaultColumnWidth: 88,
    //                 defaultRowHeight: 24,
    //                 mergeData: [
    //                     {
    //                         startRow: 12,
    //                         endRow: 12,
    //                         startColumn: 0,
    //                         endColumn: 2,
    //                     },
    //                     {
    //                         startRow: 12,
    //                         endRow: 12,
    //                         startColumn: 3,
    //                         endColumn: 4,
    //                     },
    //                     {
    //                         startRow: 12,
    //                         endRow: 12,
    //                         startColumn: 5,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 13,
    //                         endRow: 13,
    //                         startColumn: 0,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 14,
    //                         endRow: 14,
    //                         startColumn: 0,
    //                         endColumn: 3,
    //                     },
    //                     {
    //                         startRow: 14,
    //                         endRow: 14,
    //                         startColumn: 4,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 15,
    //                         endRow: 16,
    //                         startColumn: 0,
    //                         endColumn: 0,
    //                     },
    //                     {
    //                         startRow: 15,
    //                         endRow: 16,
    //                         startColumn: 1,
    //                         endColumn: 1,
    //                     },
    //                     {
    //                         startRow: 15,
    //                         endRow: 16,
    //                         startColumn: 2,
    //                         endColumn: 2,
    //                     },
    //                     {
    //                         startRow: 15,
    //                         endRow: 16,
    //                         startColumn: 3,
    //                         endColumn: 3,
    //                     },
    //                     {
    //                         startRow: 15,
    //                         endRow: 16,
    //                         startColumn: 4,
    //                         endColumn: 4,
    //                     },
    //                     {
    //                         startRow: 15,
    //                         endRow: 16,
    //                         startColumn: 5,
    //                         endColumn: 5,
    //                     },
    //                     {
    //                         startRow: 15,
    //                         endRow: 15,
    //                         startColumn: 6,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 23,
    //                         endRow: 23,
    //                         startColumn: 0,
    //                         endColumn: 2,
    //                     },
    //                     {
    //                         startRow: 23,
    //                         endRow: 23,
    //                         startColumn: 3,
    //                         endColumn: 4,
    //                     },
    //                     {
    //                         startRow: 23,
    //                         endRow: 23,
    //                         startColumn: 5,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 1,
    //                         endRow: 1,
    //                         startColumn: 0,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 11,
    //                         endRow: 11,
    //                         startColumn: 5,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 11,
    //                         endRow: 11,
    //                         startColumn: 3,
    //                         endColumn: 4,
    //                     },
    //                     {
    //                         startRow: 11,
    //                         endRow: 11,
    //                         startColumn: 0,
    //                         endColumn: 2,
    //                     },
    //                     {
    //                         startRow: 0,
    //                         endRow: 0,
    //                         startColumn: 0,
    //                         endColumn: 2,
    //                     },
    //                     {
    //                         startRow: 0,
    //                         endRow: 0,
    //                         startColumn: 3,
    //                         endColumn: 4,
    //                     },
    //                     {
    //                         startRow: 0,
    //                         endRow: 0,
    //                         startColumn: 5,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 2,
    //                         endRow: 2,
    //                         startColumn: 0,
    //                         endColumn: 4,
    //                     },
    //                     {
    //                         startRow: 2,
    //                         endRow: 2,
    //                         startColumn: 5,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 3,
    //                         endRow: 3,
    //                         startColumn: 6,
    //                         endColumn: 7,
    //                     },
    //                     {
    //                         startRow: 3,
    //                         endRow: 4,
    //                         startColumn: 0,
    //                         endColumn: 0,
    //                     },
    //                     {
    //                         startRow: 3,
    //                         endRow: 4,
    //                         startColumn: 1,
    //                         endColumn: 1,
    //                     },
    //                     {
    //                         startRow: 3,
    //                         endRow: 4,
    //                         startColumn: 2,
    //                         endColumn: 2,
    //                     },
    //                     {
    //                         startRow: 3,
    //                         endRow: 4,
    //                         startColumn: 3,
    //                         endColumn: 3,
    //                     },
    //                     {
    //                         startRow: 3,
    //                         endRow: 4,
    //                         startColumn: 4,
    //                         endColumn: 4,
    //                     },
    //                     {
    //                         startRow: 3,
    //                         endRow: 4,
    //                         startColumn: 5,
    //                         endColumn: 5,
    //                     },
    //                 ],
    //                 rowData: {
    //                     0: {
    //                         h: 21,
    //                         hd: 0,
    //                         ah: 24,
    //                         field: 'pageEyeBrow',//页眉
    //                     },
    //                     1: {
    //                         h: 74.9999885559082,
    //                         hd: 0,
    //                         ia: 0,
    //                         field: 'header',//表头
    //                     },
    //                     2: {
    //                         h: 21,
    //                         hd: 0,
    //                         field: 'sheetEyeBrow',//表眉
    //                     },
    //                     3: {
    //                         h: 21,
    //                         hd: 0,
    //                         field: 'headLine',//标题
    //                     },
    //                     4: {
    //                         h: 21,
    //                         hd: 0,
    //                         field: 'headLine',//标题
    //                     },
    //                     5: {
    //                         h: 21,
    //                         hd: 0,
    //                         field: '分部分项分部行',
    //                     },
    //                     6: {
    //                         h: 21,
    //                         hd: 0,
    //                         field: '分部分项清单',
    //                         parentName: '分部分项分部行',
    //                     },
    //                     7: {
    //                         h: 21,
    //                         hd: 0,
    //                         field: 'pageStatistic',//页统计类型   本页小计
    //                     },
    //                     8: {
    //                         h: 21,
    //                         hd: 0,
    //                         field: 'sheetStatistic',//表统计类型
    //                     },
    //                     9: {
    //                         h: 24,
    //                         hd: 0,
    //                         field: 'pageFoot',//页脚
    //                     },
    //                 },
    //                 columnData: {
    //                     0: {
    //                         w: 88,
    //                         hd: 0,
    //                     },
    //                     1: {
    //                         w: 88,
    //                         hd: 0,
    //                     },
    //                     2: {
    //                         w: 88,
    //                         hd: 0,
    //                     },
    //                     3: {
    //                         w: 88,
    //                         hd: 0,
    //                     },
    //                     4: {
    //                         w: 206.80001831054688,
    //                         hd: 0,
    //                     },
    //                     5: {
    //                         w: 88,
    //                         hd: 0,
    //                     },
    //                     6: {
    //                         w: 88,
    //                         hd: 0,
    //                     },
    //                     7: {
    //                         w: 88,
    //                         hd: 0,
    //                     },
    //                 },
    //                 showGridlines: 1,
    //                 rowHeader: {
    //                     width: 46,
    //                     hidden: 0,
    //                 },
    //                 columnHeader: {
    //                     height: 20,
    //                     hidden: 0,
    //                 },
    //                 selections: ['A1'],
    //                 rightToLeft: 0,
    //             },
    //         },
    //         resources: [
    //             {
    //                 name: 'SHEET_WORKSHEET_PROTECTION_PLUGIN',
    //                 data: '{}',
    //             },
    //             {
    //                 name: 'SHEET_WORKSHEET_PROTECTION_POINT_PLUGIN',
    //                 data: '{}',
    //             },
    //             {
    //                 name: 'SHEET_RANGE_PROTECTION_PLUGIN',
    //                 data: '{}',
    //             },
    //             {
    //                 name: 'SHEET_DEFINED_NAME_PLUGIN',
    //                 data: '{}',
    //             },
    //             {
    //                 name: 'SHEET_DATA_VALIDATION_PLUGIN',
    //                 data: '{"sheet1":[]}',
    //             },
    //             {
    //                 name: 'SHEET_AuthzIoMockService_PLUGIN',
    //                 data: '{}',
    //             },
    //         ],
    //     };
    //     let dataTemplate = new DataTemplate();
    //     dataTemplate.mergeData = workbookData.sheets["sheet1"].mergeData;
    //     for (let cellDataKey in workbookData.sheets["sheet1"].cellData) {
    //         let rowInfo = workbookData.sheets["sheet1"].cellData[cellDataKey];
    //         let rowDTO = new RowDTO();
    //
    //         let rowDatum = workbookData.sheets["sheet1"].rowData[cellDataKey];//约束条件
    //         if (ObjectUtils.isEmpty(rowDatum)) {
    //             break;
    //         }
    //         rowDTO.height = await this.convertPxToRowHeight(rowDatum.h);
    //         rowDTO.rowNum = Number.parseInt(cellDataKey)+1;
    //         rowDTO.field = rowDatum.field;
    //         rowDTO.parentName = rowDatum.parentName;
    //         let statics = 0;
    //         for (let rowInfoKey in rowInfo) {
    //             if (statics== workbookData.sheets["sheet1"].columnCount) break;
    //             statics++;
    //             let cellDTO = new CellDTO();
    //             let cellInfo = rowInfo[rowInfoKey];//前台的数据
    //             let cellStyle = await dataTemplate.convertCellUnitStyle(workbookData.styles[cellInfo.s]);
    //             cellDTO.value = cellInfo.v;
    //             cellDTO.style = cellStyle;
    //             cellDTO.columnNum = Number.parseInt(rowInfoKey)+1;
    //             rowDTO.cellInfoList.push(cellDTO);
    //         }
    //         dataTemplate.rowInfoList.push(rowDTO);
    //     }
    //
    //     for (let columnDataKey in workbookData.sheets["sheet1"].columnData) {
    //         let columnDTO = new ColumnDTO();
    //         columnDTO.colNum = Number.parseInt(columnDataKey)+1;
    //         let widthObject = workbookData.sheets["sheet1"].columnData[columnDataKey];
    //         columnDTO.width = await this.convertPxToColumnWidth(widthObject.w);
    //         dataTemplate.columnInfoList.push(columnDTO);
    //     }
    //     dataTemplate.columnNum = dataTemplate.columnInfoList.length;//一共多少列
    //     dataTemplate.rowNum = dataTemplate.rowInfoList.length;
    //
    //     // let excelPath = this.getProjectRootPath()+"\\build\\extraResources\\excelTemplate\\unit\\单位工程层级.xlsx";
    //     let excelPath = "C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx";
    //     let sheetName = "表1-6 分部分项工程量清单与计价表";
    //
    //     // read from a file
    //     let workbook = new ExcelJS.Workbook();
    //     // const workbookWriter = new Excel.stream.xlsx.WorkbookWriter(excelPath);
    //     await workbook.xlsx.readFile(excelPath)
    //
    //     // 获取第一个工作表
    //     const workSheet = workbook.getWorksheet(sheetName);
    //     // let data = fbFx.filter(item => item.kind === BranchProjectLevelConstant.qd|| item.kind === BranchProjectLevelConstant.fb||item.kind === BranchProjectLevelConstant.zfb);
    //
    //
    //     // 创建一个带有页眉和页脚可写的工作表
    //     // var worksheetWriter =
    //     // workSheet.spliceColumns(6,0);
    //     workSheet.getColumn("F").key = "lala";
    //     workSheet.deleteColumnKey("lala");
    //
    //
    //     const worksheet =  workbook.addWorksheet('My Sheet', {
    //         pageSetup:{paperSize: 9, orientation:'landscape'}
    //     });
    //     await dataTemplate.generateDataTemplate(worksheet);
    //     await dataTemplate.setColumnWidth(worksheet);
    //     await dataTemplate.dealWithMerge(worksheet);
    //
    //     //生成 dataTypeList
    //     let dataTypeList = []    //
    //     let rows = dataTemplate.rowInfoList.filter(item => item.field!="sheetStatistic"&& item.field!="pageStatistic"
    //     && item.field!="headLine" && item.field!="header" && item.field!="pageEyeBrow" && item.field!="sheetEyeBrow" && item.field!="pageFoot" && ObjectUtils.isNotEmpty(item.field)
    //     );
    //     let arrayTree = xeUtils.toArrayTree(rows, {
    //         key: 'field',
    //         parentKey: 'parentName',
    //     });
    //
    //     await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test1-BEFORE.xlsx");
    //     // await workSheetDe._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test1-31.xlsx");
    //
    //     let heightRows = dataTemplate.rowInfoList.filter(item => item.field=="sheetEyeBrow" || item.field=="header"
    //         || item.field=="headLine" || item.field=="pageEyeBrow"
    //     );
    //     let dataRelation = new DataRelation({constructId,singleId,unitId,workSheet:worksheet,headEndIndex:5,dataTemplate:dataTemplate});
    //     await this.generateDataTypeList(arrayTree,dataTypeList,dataRelation);
    //     dataRelation.dataTypeList = dataTypeList;
    //     await dataRelation.dealWithReportData();
    //     await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test1-31.xlsx");
    //
    //
    //     console.log("");
    // }


    async generateDataTypeList(arrayTree,dataTypeList,dataRelation,parent) {
        for (let i = 0; i < arrayTree.length; i++) {
            let object = {};
            dataTypeList.push(object);
            if (ObjectUtils.isEmpty(arrayTree[i].dataSourceType)) { //如果数据源类型为空  认为是数据模板行
                object.data = [{
                    "sequenceNbr":Snowflake.nextId(),
                }];
                object.dataType = "";
            }else if (arrayTree[i].field=="footNote") {   //如果是脚注行
                //脚注行不应有children  但因为前面的数据结构 toArrayTree可能造成存在子级的情况 比如带分部小计的表1-16 这里进行清除
                if (ObjectUtils.isNotEmpty(arrayTree[i].children)) {
                    arrayTree[i].children = null;
                }
                //当前脚注行数据源的数据
                let dataCurrent = await dataRelation.getReportData(arrayTree[i].dataSourceType,arrayTree[i].configObject);
                //如果脚注行为子级 即子带区的脚注行,则数据行数理论上应为父级的元素个数 但有特殊情况 如顶级分部 下面子带区的脚注行为分部分项清单时 该分部下不需要展示脚注行
                if (ObjectUtils.isNotEmpty(arrayTree[i].parentName)) {
                    //得到父级数据源的数据
                    let dataTypeListData = await dataRelation.getDataTypeListData(arrayTree[i].parentName,dataRelation.dataTypeList);
                    /****边界情况***********/
                    //如果子带区脚注行的父级数据源数据为空 则这里给一个默认的父级数据 以便于将脚注行显示出来  ex:带分部小计的表1-6、1-16是如此要求的
                    if (ObjectUtils.isEmpty(dataTypeListData) && parent.field != "dataHeadline") { //脚注行父级为数据标题行时父级数据的赋值已经进行过
                        dataTypeListData = new Array();
                        dataTypeListData.push({
                            "sequenceNbr":ObjectUtils.isNotEmpty(dataCurrent)?dataCurrent[0].parentId:Snowflake.nextId(),
                        });
                    }
                    /****边界情况***********/
                    let  data = [];//表示展示多少条脚注行
                    for (let j = 0; j < dataTypeListData.length; j++) {
                        let object = {};
                        object.sequenceNbr = Snowflake.nextId();
                        object.parentId = dataTypeListData[j].sequenceNbr;
                        if (parent.field == "dataHeadline") { //如果脚注行的父级为数据标题行  则数据标题行对应的数据源有数据时要显示该脚注行
                            data.push(object);
                        }else {
                            //如果父带区数据的children里没有子带区的数据  比如顶级分部 则不需要展示该子带区的脚注行
                            let dataList = dataCurrent.filter(item=> item.parentId == dataTypeListData[j].sequenceNbr);
                            if (ObjectUtils.isNotEmpty(dataList)) {
                                data.push(object);
                            };
                        }
                    }
                    object.data = data;
                }else {
                    object.data = [{
                        "sequenceNbr":Snowflake.nextId(),
                    }];//如此赋值是因为数据只渲染一条
                }
                object.dataType = arrayTree[i].dataSourceType;
            }else if (arrayTree[i].field=="dataHeadline") {  //如果是数据标题行  则只映射标题行的数据 在有多条数据的情况下这里给一条虚拟数据，如果数据源对应的数据为空 则标题行不显示
                //给标题行数据源增加 “标题行数据源”标识 是为了应对 标题行数据源的子级脚注行 如果不增加此标识用 xeUtils.toArrayTree会出现问题
                object.data = await dataRelation.getReportData(arrayTree[i].dataSourceType.replace("标题行数据源",""), arrayTree[i].configObject);
                object.dataType = arrayTree[i].dataSourceType;
                if (object.data.length >= 1) {
                    object.data = [{
                        "sequenceNbr":Snowflake.nextId(),   //数据标题行只显示一行
                    }];
                }
            } else {
                object.data = await dataRelation.getReportData(arrayTree[i].dataSourceType,arrayTree[i].configObject);
                if (ObjectUtils.isNotEmpty(parent) && parent.field == "dataHeadline") {  //如果数据源的父级是标题行数据 需要赋值父级id  防止对具有层级关系的数据进行重排时报错
                    let data = await dataRelation.getDataTypeListData(arrayTree[i].parentName,dataRelation.dataTypeList);//标题行数据的length要么为0,要么为1
                    for (let j = 0; j < object.data.length; j++) {
                        let datum = object.data[j];
                        datum.parentId = data[0].sequenceNbr;
                    }
                }
                object.dataType = arrayTree[i].dataSourceType;
            }
            //以上是赋值 data和dataType两个属性
            object.variable = [];
            object.configObject = arrayTree[i].configObject;//数据源类型的细分配置
            object.children = [];
            object.field = arrayTree[i].field;
            object.rowTemplateNum = arrayTree[i].rowNum;
            for (let j = 0; j < arrayTree[i].cellInfoList.length; j++) {
                let variableObject = {};
                let element = arrayTree[i].cellInfoList[j];
                variableObject.name =element.value;
                variableObject.position = element.columnNum;
                variableObject.precision = element.custom.precision;//精度设置位数
                object.variable.push(variableObject);
            }
            if (ObjectUtils.isNotEmpty(arrayTree[i].children)) {
                await this.generateDataTypeList(arrayTree[i].children,object.children,dataRelation,arrayTree[i]);
            }
        }
    }

    //将px像素转化为行的单位 磅和  宽的单位 0.1英寸
    async convertPxToColumnWidth(pxValue) {
        //A4纸的宽度总像素  (8.27 * ExcelEnum.screenPPI)
        return (pxValue/(8.27 * ExcelEnum.screenPPI))*ExcelEnum.A4Width;
        // static A4Height = (29.6/2.54)*72;//单位 磅;   839.06
        // static A4Width = (20.9/2.54)*10; //单位 0.1英寸   82.28
    }

    async convertPxToRowHeight(pxValue) {
        //A4纸的高度总像素  (11.69英寸 * ExcelEnum.screenPPI)
        return await this.rowHeightToFixed((pxValue/(11.69 * ExcelEnum.screenPPI))*ExcelEnum.A4Height);
    }

    async rowHeightToFixed(height) {
        return Number.parseFloat(height.toFixed(1));
    }

    async colWidthToFixed(width) {
        return Number.parseFloat(width.toFixed(2));
    }

    async addWorkSheet(workbook,workSheet,sheetName) {
        let newWorksheet = workbook.addWorksheet(sheetName,workSheet);
        newWorksheet._rows = _.cloneDeep(workSheet._rows);
        newWorksheet._merges = _.cloneDeep(workSheet._merges);
        newWorksheet._columns = _.cloneDeep(workSheet._columns);
        newWorksheet.rowBreaks = _.cloneDeep(workSheet.rowBreaks);
    }

    /**
     * sheet页插入一行 插入行的格式及合并单元格继承上一行  如插入一行本页小计 或 一行空白行等
     * @param rowInsertNum 插入行的行号  插入行不应该破坏原有的合并单元格结构
     * @param values 插入行的每一列值  ex：[["","","","","","","","","",""]]、[["/","/","本页小计","本页小计","/","/","/","/","/",""]]
     * @param workSheet
     * @param height 设定插入行的行高
     * @returns
     */
    async insertSheetOneRow(rowInsertNum,values,workSheet,height) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        workSheet.insertRows(rowInsertNum,values,'o');
        //重置 _merges
        await this.resetMerges(workSheet,rowInsertNum);
        let rowObject = workSheet.getRow(rowInsertNum);
        rowObject.height = height;
        //对插入行的合并单元格格式 进行处理
        for (let m = 0;m<rowObject._cells.length;m++){
            //获取模板行的合并单元格
            let mergeName = await this.getMergeName(workSheet._merges,workSheet._rows[rowInsertNum-2]._cells[m]);
            if (mergeName!=null && mergeMaps.get(mergeName)!=null){
                let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                workSheet.unMergeCells([rowInsertNum,left,rowInsertNum,right]);
                workSheet.mergeCells([rowInsertNum,left,rowInsertNum,right]);
            }
            rowObject._cells[m].style = workSheet._rows[rowInsertNum-2]._cells[m].style;
        }
    }

    /**
     * 对填充完数据的sheet页进行分页 并在新的一页增加表头
     * @param workSheet
     * @param rowNum 表示每页的起始行号  第一次调用时应传 1
     * @param headArgs 为对象 {headStartNum,headEndNum} 表示表头起始行号和结束行号
     * @param totalPage 第一次调用时应传 0
     * @param horizonOrVertical  “H” 表示横版  “V” 表示竖版
     * @returns 返回总页数
     */
    async pageSplit(workSheet,rowNum,headArgs,totalPage,horizonOrVertical){  //从1 开始   args表头相关参数
        let differ = 0;
        if (horizonOrVertical == "H") {  //表示excel为横版
            differ = (ExcelEnum.A4HeightHorizontal-ExcelEnum.A4TopHorizontal-ExcelEnum.A4BottomHorizontal);
        }else if (horizonOrVertical == "V"){   //表示excel为竖版
            differ = ExcelEnum.A4Height-ExcelEnum.A4Top-ExcelEnum.A4Bottom;
        }else {
            return ;
        }

        totalPage++;
        let {headStartNum,headEndNum}=headArgs;//从1开始
        let height = 0;
        let blankRows = 0;//要求如果分页时经历了空白行  那么分页符应该置在空白行的前面那一行
        for (let i = rowNum-1; i < workSheet._rows.length-1; i++) {
            height +=workSheet._rows[i].height;
            if (workSheet._rows[i].height == 0) {
                blankRows++;
            }
            if (height+workSheet._rows[i+1].height>differ){
                if (blankRows == 0) {
                    let rowLast = workSheet._rows[i];
                    rowLast.addPageBreak();
                }else {
                    let rowBefore = workSheet._rows[i-blankRows];
                    rowBefore.addPageBreak();
                    blankRows = 0;
                }
                /***********边界情况******************/
                let rowInserNum = i+1;
                //分页后进行表头复制
                for (let j = headStartNum; j <= headEndNum; j++) {
                    await this.copyRowsWithIndex(j,rowInserNum+j,workSheet);
                }
                // await workSheet.workbook.xlsx.writeFile("D:\\csClient\\测试\\test.xlsx");
                totalPage = await this.pageSplit(workSheet,rowInserNum+1,headArgs,totalPage,horizonOrVertical);
                break;
            }
        }
        return totalPage;
    }

    /**
     * 给sheet表填充数据
     * @param data
     * @param worksheet
     * @param headEndIndex 表示表头行索引的最大值
     * @param fieldMap map结构  key为该属性的col列值,value为该属性的名称 ex: map.set(1,"dispNo"),
     * map.set(2,"bdCode") 表示模板sheet的第一列填充 dispNo属性,第二列填充 bdCode属性
     * @returns
     */
    async writeDataToSheet(data, worksheet,headEndIndex,fieldMap) {
        let headCount = headEndIndex;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await this.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = await this.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                for (let [key, value] of fieldMap) {
                    if (cell.col == key) {
                        cell.value = data[countRow][value];
                    }
                }
            }
        }
    }
    /**
     * sheet表新增一行后 对原有的merges进行向下重置
     * @param workSheet
     * @param rowInsertNum
     * @returns
     */
    async resetMerges(workSheet,rowInsertNum) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let map = new Map();
        for (let [key, value] of mergeMaps) {
            const regex = /([A-Za-z]+)(\d+)/;

            const matches = key.match(regex);
            if (matches && matches.length >= 2) {
                const part1 = matches[1];
                const part2 = matches[2];
                if (Number(part2) < rowInsertNum) {
                    map.set(key,value);
                    continue;
                }
                let after = Number(part2)+1+"";
                key = part1+after;
            } else {
                // console.log("Invalid string format");
            }
            value.top = value.top+1;
            value.bottom = value.bottom+1;
            map.set(key,value);
        }
        workSheet._merges = {};
        for (let [key, value] of map) {
            workSheet._merges[key] = value;
        }
    }

    /**
     * sheet表删除一行后 对原有的merges进行重置
     * @param workSheet
     * @param rowDelNum
     * @returns
     */
    async resetMergesWhenDel(workSheet,rowDelNum) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let map = new Map();
        for (let [key, value] of mergeMaps) {
            const regex = /([A-Za-z]+)(\d+)/;

            const matches = key.match(regex);
            if (matches && matches.length >= 2) {
                const part1 = matches[1];
                const part2 = matches[2];
                if (Number(part2) < rowDelNum) {
                    map.set(key,value);
                    continue;
                }
                if (Number(part2) == rowDelNum) {
                    continue;
                }
                let after = Number(part2)-1+"";
                key = part1+after;
            } else {
                //console.log("Invalid string format");
            }
            value.top = value.top-1;
            value.bottom = value.bottom-1;
            map.set(key,value);
        }
        workSheet._merges = {};
        for (let [key, value] of map) {
            workSheet._merges[key] = value;
        }
    }

    /**
     * 对单个cell对象的内容水平方向的居中居左居右格式进行设置
     * @param originalStyle  cell对象的原始格式
     * @param cell
     * @param horizontal 值为 "left" "right" "middle"
     * @returns
     */
    async setStyleForCellHorizontal(originalStyle,cell,horizontal) {
        cell.style = {};
        cell.style['font'] = originalStyle.font;
        cell.style['border'] = originalStyle.border;
        cell.style['fill'] = originalStyle.fill;
        let alignment = originalStyle.alignment;
        let newAlignment = {};
        newAlignment['horizontal'] = horizontal;
        newAlignment['vertical'] = alignment.vertical;
        cell.style['alignment'] = newAlignment;
    }

    /**
     * 输出sheet中每一个cell的格式列表
     */
    async findCellStyleList(workSheet,landScape,dataTemplate) {
        let sheetStyle = new SheetStyle();

        let cellList = [];
        const merges = workSheet._merges;
        for (let j = 0; j < workSheet._rows.length; j++) {
            let rowAA = workSheet._rows[j];
            let rowTemplateNum = dataTemplate.rowDataTypelist[j+1].rowTemplateNum;
            for (let i = 0; i < rowAA._cells.length; i++) {
                let cellAA = rowAA._cells[i];
                let {isMerged, address, value, col, row, style, type, formula, model} = cellAA;
                let cellVo = new CellVo();
                //alignment 水平方向 值为2 为居中  值为1 为居左
                try {
                    cellVo.alignment = await this.getHorizontalAlignment(style);
                } catch (error) {
                    console.log(error.stackTrace);
                }
                if (!ObjectUtils.isEmpty(style) && !ObjectUtils.isEmpty(style.border)&&!ObjectUtils.isEmpty(style.border.bottom) && !ObjectUtils.isEmpty(style.border.bottom.style)) {
                    // cellVo.borderBottom = this.getBorderNumer(style.border.bottom.style);
                    cellVo.borderBottom = 1;
                }else {
                    cellVo.borderBottom = 0;//表示无边框
                }
                if (!ObjectUtils.isEmpty(style) && !ObjectUtils.isEmpty(style.border)&&!ObjectUtils.isEmpty(style.border.left) && !ObjectUtils.isEmpty(style.border.left.style)) {
                    cellVo.borderLeft = 1;
                }else {
                    cellVo.borderLeft = 0;
                }
                if (!ObjectUtils.isEmpty(style) && !ObjectUtils.isEmpty(style.border)&&!ObjectUtils.isEmpty(style.border.right) && !ObjectUtils.isEmpty(style.border.right.style)) {
                    cellVo.borderRight = 1;
                }else {
                    cellVo.borderRight = 0;
                }
                if (!ObjectUtils.isEmpty(style) && !ObjectUtils.isEmpty(style.border)&&!ObjectUtils.isEmpty(style.border.top) && !ObjectUtils.isEmpty(style.border.top.style)) {
                    cellVo.borderTop = 1;
                }else {
                    cellVo.borderTop = 0;
                }
                cellVo.cellType = type;
                cellVo.columnIndex = col-1;
                if (value == null) {
                    cellVo.content = "";
                }else if (typeof value ==='object'){
                    cellVo.content = {};
                    cellVo.content['richText'] = value.richText[0].text;
                }else{
                    cellVo.content = value;
                }
                // cellVo.fontBold =
                if (ObjectUtils.isNotEmpty(style) && style.font!=null){
                    cellVo.fontName = style.font.name;
                    cellVo.fontSize = style.font.size;
                }
                cellVo.rowIndex = row-1;
                cellVo.verticalAlignment = await this.getVerticalAlignment(style);
                cellVo.styleId =  dataTemplate.rowInfoList[rowTemplateNum-1].cellInfoList[i].styleId;
                cellList.push(cellVo);
            }
        }
        sheetStyle.cells = cellList;
        sheetStyle.styles = dataTemplate.styleObject;
        //----------merge-------------------
        function CellMerge(key,firstRow, lastRow, firstCol, lastCol) {
            this.key = key;
            this.firstRow = firstRow;
            this.lastRow = lastRow;
            this.firstCol = firstCol;
            this.lastCol = lastCol;
        }
        let mergesList = [];
        let mergeMap = new Map(Object.entries(merges))
        for (let [key, value] of mergeMap) {
            let cellMerge = new CellMerge(key,value.top-1,value.bottom-1,value.left-1,value.right-1);
            mergesList.push(cellMerge);
        }
        sheetStyle.merges = mergesList;
        //----------------------------------
        function PrintProperty(headerMerge,footerMerge,leftMerge,rightMerge,bottomMerge, topMerge,landSpace) {
            this.headerMerge = headerMerge;
            this.footerMerge = footerMerge;
            this.leftMerge = leftMerge;
            this.rightMerge = rightMerge;
            this.bottomMerge = bottomMerge;
            this.topMerge = topMerge;
            this.landSpace = landSpace;
        }
        let printProperty = new PrintProperty(workSheet.pageSetup.margins.header,
            workSheet.pageSetup.margins.footer,
            workSheet.pageSetup.margins.left,
            workSheet.pageSetup.margins.right,
            workSheet.pageSetup.margins.bottom,
            workSheet.pageSetup.margins.top,
            false
        );
        sheetStyle.print = printProperty;

        if (landScape) {
            printProperty.landSpace = true;//表示为横版
        }
        //-----------row--------------------------
        // fillContentLimitHei = 1043.62;
        function RowUnit(height, rowIndex,ratio) {
            this.height = height;
            this.rowIndex = rowIndex;
            this.ratio = ratio;
        }

        function roundUpToFiveDecimalPlaces(num) {
            // 乘以 10 的五次方，将小数部分移到整数部分
            const multiplier = 1;

            // 向下舍去   1.6 = 1
            const roundedNum = Math.floor(num * multiplier) / multiplier;

            return roundedNum;
        }

        let map = new Map();
        let rowBreakBeforeIndex = 0;
        for (let n = 0; n < workSheet.rowBreaks.length; n++) {
            let rowBreakIndex = workSheet.rowBreaks[n].id-1;
            let slice = workSheet._rows.slice(rowBreakBeforeIndex,rowBreakIndex+1);
            let rowTotalHeight = 0;
            for (let i = 0; i < slice.length; i++) {
                let rowCur = slice[i];
                rowTotalHeight+= rowCur.height;
            }
            map.set(rowBreakBeforeIndex+"+"+rowBreakIndex,rowTotalHeight);
            rowBreakBeforeIndex = rowBreakIndex+1;
        }
        if (ObjectUtils.isNotEmpty(workSheet.rowBreaks)) {
            let index = workSheet.rowBreaks[workSheet.rowBreaks.length-1].id-1;
            let splice = workSheet._rows.slice(index+1,workSheet._rows.length);
            let rowTotalHeight = 0;
            for (let i = 0; i < splice.length; i++) {
                let rowCur = splice[i];
                rowTotalHeight +=rowCur.height;
            }
            map.set((index+1)+"+"+(workSheet._rows.length-1),rowTotalHeight);
        }

        if (workSheet.rowBreaks.length == 0) {  //如果只有一页
            let sliceRows = workSheet._rows.slice(0,workSheet._rows.length);
            let rowTotalHeight = 0;
            for (let i = 0; i < sliceRows.length; i++) {
                let rowCur = sliceRows[i];
                rowTotalHeight +=rowCur.height;
            }
            map.set(0+"+"+(workSheet._rows.length-1),rowTotalHeight);
        }
        let rowsList = [];
        for (let i = 0; i < workSheet._rows.length; i++) {
            let sheetRow = workSheet._rows[i];
            let rowUnit = {};
            let totalHeightPage = await this.getTotalHeightInCurrentPage(i,map);
            let ratio = (sheetRow.height/totalHeightPage);
            if (printProperty.landSpace) {  //如果是横版
                // fillContentLimitHei = 680.38;
                rowUnit = new RowUnit(roundUpToFiveDecimalPlaces(ratio*680.38),sheetRow.number-1,Number(ratio.toFixed(5)));
            }else {
                rowUnit = new RowUnit(roundUpToFiveDecimalPlaces(ratio*1043.62),sheetRow.number-1,Number(ratio.toFixed(5)));
            }
            rowsList.push(rowUnit);
        }
        sheetStyle.rows = rowsList;
        //----------Column-----------------------
        function ColumnUnit(width, columnIndex) {
            this.width = width;
            this.columnIndex = columnIndex;
        }
        let columnList = [];
        for (let i = 0; i < workSheet.columns.length; i++) {
            let column = workSheet.columns[i];
            if (printProperty.landSpace) {  //如果是横版
                columnList.push(new ColumnUnit((column.width/ExcelEnum.A4WidthHorizontal)*1020,column.number-1));
            }else {
                columnList.push(new ColumnUnit((column.width/ExcelEnum.A4Width)*(770-60),column.number-1));
            }

        }
        sheetStyle.columns = columnList;
        //--------组装pageResult----------------------------
        let pageResult = {};
        let sheetBreak = [];
        pageResult['sheetBreak'] = sheetBreak;


        for (let i = 0; i < workSheet.rowBreaks.length; i++) {
            let rowBreak = workSheet.rowBreaks[i];
            let number = rowBreak.id-1;
            sheetBreak.push(number);
        }
        sheetStyle.pageResult = pageResult;
        return sheetStyle;
    }


    async getTotalHeightInCurrentPage(rowIndex,map) {
        let result = 0;
        map.forEach((value, key) => {
            let split = key.split("+");
            let first = Number.parseFloat(split[0]);
            let last = Number.parseFloat(split[1]);
            if (rowIndex >= first && rowIndex <= last)
                result = value;
            }
        )
        return result;
    }

    async getVerticalAlignment(style) {
        if (ObjectUtils.isEmpty(style)||null == style.alignment) return null;
        //top 为3 middle 为1  bottom为2
        if (style.alignment.vertical == 'middle') {
            return 1;
        } else if (style.alignment.vertical == 'top') {
            return 3;
        }else if (style.alignment.vertical == 'bottom') {
            return 2;
        }
    }

    async getHorizontalAlignment(style) {
        if (ObjectUtils.isEmpty(style) || null == style.alignment) return null;
        // 值为2 为居中  值为1 为居左
        if (style.alignment.horizontal == 'center') {
            return 2;
        } else if (style.alignment.horizontal == 'left') {
            return 1;
        } else if (style.alignment.horizontal == 'right') {
            return 3;
        }
    }

    //删除一行后需要进行同步处理
    async resetRowNumWhenDel(workSheet) {
        const regex = /([A-Za-z]+)(\d+)/;
        for (let i = 0; i < workSheet._rows.length; i++) {
            if (workSheet._rows[i]._number != i+1) {
                workSheet._rows[i]._number = i+1;
                //同时需要对cell的address进行重置
                //在空白行填充的过程中框架mergeCells方法会对后续行的address和_merges内容不对的进行重置
                //但删除一行后如果不填充 address还是原来删除前的比较大的值 就会造成excel不显示该行
                for (let j = 0; j < workSheet._rows[i]._cells.length; j++) {
                    let address = workSheet._rows[i]._cells[j]._address;
                    const matches = address.match(regex);
                    const part1 = matches[1];
                    workSheet._rows[i]._cells[j]._address = part1+workSheet._rows[i]._number;
                    workSheet._rows[i]._cells[j].model.address = part1+workSheet._rows[i]._number;
                    //对cell的master进行重置
                    if (workSheet._rows[i]._cells[j].model.master != null) {
                        let name = await this.getMergeName(workSheet._merges,workSheet._rows[i]._cells[j]);
                        workSheet._rows[i]._cells[j].model.master = name;
                    }
                }
            }
        }
    }
}
module.exports = {
    ExcelOperateUtil: new ExcelOperateUtil()
};




