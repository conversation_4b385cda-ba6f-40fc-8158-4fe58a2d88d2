const DePropertyTypeConstant = require("../../../enum/DePropertyTypeConstant");
const {ObjectUtils} = require("../../../utils/ObjectUtils");
const {NumberUtil} = require("../../../utils/NumberUtil");

//获取计算基数
let getJSJS=({is2022,de,kind})=>{
    //计算基数默认为 1
    let caculateBase=1;

    if (de.isCostDe !== DePropertyTypeConstant.NON_COST_DE && ObjectUtils.isNotEmpty(de.baseNum)) {
        if (ObjectUtils.isNotEmpty(de.baseNum["def"])) {
            caculateBase = de.baseNum["def"];
        }
        else if (ObjectUtils.isNotEmpty(de.baseNum[kind])) {
            caculateBase = de.baseNum[kind];
        }
    }

    return caculateBase;
}

let getResQty=(is2022,jiany<PERSON>,rcj,de)=>{

    let  caculateQty = Number(rcj.resQty);
    // 如果是定额类型的人材机 消耗量如果没有 或者是0 默认值是1
    if(de.rcjFlag==1){
        caculateQty=1;
    }
    let caculateUnit =1;
    //如果是安装的时候 跳过这个 % 的处理
    if (rcj.unit === '%' && de.isCostDe!=DePropertyTypeConstant.AZ_DE) {
        caculateUnit = 0.01;
    }
    caculateQty = NumberUtil.multiply(caculateQty, caculateUnit);
    //人材机父子及判断 如果父级人材机存在  消耗量 需要重新计算
    if (rcj.parent) {
        caculateQty = NumberUtil.multiply(caculateQty, rcj.parent.resQty);
    }
    return caculateQty;
}
//获取人材机 甲供数量
let getDonorMaterialNumber = (is2022, jianyi, rcj) => {
    return rcj.ifDonorMaterial=="1"?rcj.donorMaterialNumber:0;
}

// 甲定人材机市场价
let getJDSCJ=(is2022,jianyi,rcj)=>{
    return rcj.ifDonorMaterial=="2"?rcj.marketPrice:0;
}

// 暂估人材机市场价
let getZGSCJ=(is2022,jianyi,rcj)=>{
    return rcj.ifProvisionalEstimate==1?rcj.marketPrice:0;
}

module.exports = {getDonorMaterialNumber,getResQty,getJSJS,getJDSCJ, getZGSCJ}
