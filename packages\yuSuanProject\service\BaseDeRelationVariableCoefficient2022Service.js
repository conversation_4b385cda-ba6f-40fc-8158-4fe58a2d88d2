'use strict';


const {NumberUtil} = require("../utils/NumberUtil");
const {Service, Log} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {SqlUtils} = require("../utils/SqlUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {BaseDeRelationVariableCoefficient2022} = require("../model/BaseDeRelationVariableCoefficient2022");
const {TaxCalculationMethodEnum} = require("../enum/TaxCalculationMethodEnum");

/**
 * 计算规则
 */
class BaseDeRelationVariableCoefficient2022Service extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseDeRelationVariableCoefficient2022Dao = this.app.appDataSource.manager.getRepository(BaseDeRelationVariableCoefficient2022);
    baseDeRelationVariableDescribe2022Service=this.service.yuSuanProject.baseDeRelationVariableDescribe2022Service;

    /**
     * 获取父定额的变量数据
     */
    async queryVariableCoefficient(parentDe,qd){
        //获取父定额的规则组id
        let groupid =await this.baseDeRelationVariableDescribe2022Service.queryGroupiD(parentDe);
        if(ObjectUtils.isEmpty(groupid)){
            return  null;
        }
        let result= await this.baseDeRelationVariableCoefficient2022Dao.find({
            where: {
                groupid: groupid
            }
        });
        //根据根据父定额工程量修改
        result.forEach(gz=>{
            if(gz.variableCode=="GCL" && parentDe.quantityExpression=="QDL"){
                gz.value=qd.quantityExpressionNbr;
                gz.quantityExpressio="QDL";
            }else {
                //将默然值付给value
                gz.value=gz.default;
            }
        })
        let index=1
        for(const r of result){
            if(ObjectUtils.isEmpty(r.value)){
                r.value=NumberUtil.numberScale6(r.default);
            }
            r.id=index;
            index++;
        }
        //计算其他规则数据
        result=await this.updateVariableCoefficient(result);
        return  result;
    }


    /**
     * 修改父定额的变量数据
     */
    async updateVariableCoefficient(variableCoefficient){
        //groupid==3 的不需要计算
        if(variableCoefficient[0].groupid==3){
            return variableCoefficient;
        }
        //计算其他规则数据
        let varr=[];
        let  valueMap=new Map();
        valueMap.set("Π",3.1415926);
        varr.push("Π");
        let gcl;
        for(const vf of variableCoefficient){
             if(vf.ifEditable==0){
                 valueMap.set(vf.variableCode,vf.value);
                 varr.push(vf.variableCode)
                 if(vf.variableCode=="GCL"){
                     gcl=vf.value;
                 }
             }
        }
        //计算每一个不可修改的数据值
        for(const vf of variableCoefficient){
            if(vf.ifEditable==1){
               //替换计算公式
                let formula = vf.formula.replaceAll("（","(").replaceAll("）",")").replaceAll("【","(").replaceAll("】",")");
                let split = formula.split("=");
                formula=split[1];
                //替换所有变量
                for (let varrElement of varr) {
                    formula=  formula.replaceAll(varrElement, ObjectUtils.isEmpty(valueMap.get(varrElement))?0:valueMap.get(varrElement));
                }
                //计算数值
                vf.value=NumberUtil.numberScale6(NumberUtil.multiply(eval(formula),1));
            }
        }
        return  variableCoefficient;
    }





}

BaseDeRelationVariableCoefficient2022Service.toString = () => '[class BaseDeRelationVariableCoefficient2022Service]';
module.exports = BaseDeRelationVariableCoefficient2022Service;
