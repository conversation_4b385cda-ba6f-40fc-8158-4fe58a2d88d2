//kind(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)
const DePropertyTypeConstant = require("../../../enum/DePropertyTypeConstant");
const {ObjectUtils} = require("../../../utils/ObjectUtils");
const {getDonorMaterialNumber, getResQty, getJSJS, getJDSCJ, getZGSCJ} = require("./baseRule");
const {NumberUtil} = require("../../../utils/NumberUtil");
const Handlebars = require("handlebars");
const {Gene} = require("@valuation/rules-engine");
const kemap = {
    "材料费": "CLF",
    "机械费": "JXF",
};
const fntemplate = Handlebars.compile( `
            const zipped = _.zip({{arr1}}, {{arr2}});
           return _.sumBy(zipped, pair => pair[0] * pair[1]*{{value1}});
        `,{ noEscape: true })

const baseFn = {
    //人工市场价
    "RGF_SCJ": () => {

        return {
            "type": "rcj",
            "kind": 1,
            "cloumn": "marketPrice"
        };
    },
    //人工消耗量
    "RG_XHL": () => {
        return {
            "type": "rcj",
            "kind": 1,
            "cloumn": getResQty
        };
    },
    //计算基数
    "RG_JSJS": () => {
        return {
            "type": "de",
            "kind": 1,
            "cloumn": getJSJS
        };
    },
    //TODO 未受理规则 暂定  0 防寒子目定额价直接费
    /* "FHZMZJF_DEJ":()=>{
         return {
             "type":"de",
             "kind":1,
             "cloumn":()=>{
                 return 0;
             }
         };
     },*/
    "DEGCL": () => {
        //9999 代表从的中取值
        return {
            "type": "de",
            "cloumn": "quantity"
        };
    },
    "ZJFPRICE": () => {
        //9999 代表从的中取值
        return {
            "type": "de",
            "cloumn": "zjfPrice"
        };
    },
    "JSRATE": () => {
        return {
            "type": "item",
            "cloumn": (item) => {
                if (!item.rate) return 1;
                return item.rate ? item.rate / 100 : 1;
            },
        };
    },

    //材料市场价
    "CL_SCJ": () => {
        return {
            "type": "rcj",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": "marketPrice"
        };
    },
    //材料消耗量
    "CL_XHL": () => {
        return {
            "type": "rcj",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": getResQty
        };
    },
    //材料消耗量
    "ALL_CL_XHL": () => {
        return {
            "type": "rcj",
            "kind": [2, 5, 6, 7, 8, 9, 10],
            "cloumn": getResQty
        };
    },
    //计算基数
    "CL_JSJS": () => {
        return {
            "type": "de",
            "kind": 2,
            "cloumn": getJSJS
        };
    },

    //机械费市场价
    "JX_SCJ": () => {
        return {
            "type": "rcj",
            "kind": 3,
            "cloumn": "marketPrice"
        };
    },
    //机械消耗量
    "JX_XHL": () => {
        return {
            "type": "rcj",
            "kind": 3,
            "cloumn": getResQty
        };
    },
    //计算基数
    "JXF_JSJS": () => {
        return {
            "type": "de",
            "kind": 3,
            "cloumn": getJSJS
        };
    },
    //主材费市场价
    "ZC_SCJ": () => {
        return {
            "type": "rcj",
            "kind": 5,
            "cloumn": "marketPrice"
        };
    },
    //主材消耗量
    "ZC_XHL": () => {
        return {
            "type": "rcj",
            "kind": 5,
            "cloumn": getResQty
        };
    },
    //计算基数
    "ZC_JSJS": () => {
        return {
            "type": "de",
            "kind": 5,
            "cloumn": getJSJS
        };
    },
    //设备市场价
    "SB_SCJ": () => {
        return {
            "type": "rcj",
            "kind": 4,
            "cloumn": "marketPrice"
        };
    },
    //设备消耗量
    "SB_XHL": () => {
        return {
            "type": "rcj",
            "kind": 4,
            "cloumn": getResQty
        };
    },
    //计算基数
    "SB_JSJS": () => {
        return {
            "type": "de",
            "kind": 4,
            "cloumn": getJSJS
        };
    },

    //人工定额价
    "RG_DEJ": () => {

        return {
            "type": "rcj",
            "kind": 1,
            "cloumn": "dePrice"
        };
    },
    //材料定额价
    "CL_DEJ": () => {
        return {
            "type": "rcj",
            "kind": 2,
            "cloumn": "dePrice"
        };
    },
    //机械费定额价
    "JX_DEJ": () => {
        return {
            "type": "rcj",
            "kind": 3,
            "cloumn": "dePrice"
        };
    },
    //主材定额价
    "ZC_DEJ": () => {
        return {
            "type": "rcj",
            "kind": 5,
            "cloumn": "dePrice"
        };
    },
    //设备定额价
    "SBF_DEJ": () => {
        return {
            "type": "rcj",
            "kind": 4,
            "cloumn": "dePrice"
        };
    },
    "GR_XHL_HJ": () => {
        return {
            "type": "rcj",
            "kind": 1,
            "cloumn": (is2022, jianyi, rcj) => {
                let caculateQty = Number(rcj.resQty);
                if (rcj.unit === '工日') {
                    return caculateQty;
                }
                return 0;
            }
        };
    },
    //TODO 人材机 人工费类型  中甲供数量
    "RCJ_JG_RGSL": () => {
        //从人材机汇总中取值 前提勾选甲供 否则都为0
        //ifDonorMaterial  1是甲供
        return {
            "type": "rcj",
            "kind": 1,
            "cloumn": getDonorMaterialNumber//甲供数量
        };
    },

    //TODO 人材机 人工费类型 中对应数量
    "RCJ_RGSL": () => {
        //从人材机汇总中取值
        return {
            "type": "rcj",
            "kind": 1,
            "cloumn": "totalNumber"//数量
        };
    },

    //人材机材料类型 中甲供数量
    "RCJ_JG_CLSL": () => {
        //从人材机汇总中取值 前提勾选甲供 否则都为0
        return {
            "type": "rcj",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": getDonorMaterialNumber
        };
    },

    // 人材机 材料类型  中对应数量
    "RCJ_CLSL": () => {
        //从人材机汇总中取值
        return {
            "type": "rcj",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": "totalNumber"
        };
    },

    //人材机 机械类型 中甲供数量
    "RCJ_JG_JXSL": () => {
        //从人材机汇总中取值 前提勾选甲供 否则都为0
        return {
            "type": "rcj",
            "kind": 3,
            "cloumn": getDonorMaterialNumber
        };
    },
    //人材机 机械类型  中对应数量
    "RCJ_JXSL": () => {
        //从人材机汇总中取值
        return {
            "type": "rcj",
            "kind": 3,
            "cloumn": "totalNumber"
        };
    },

    //人材机 主材类型 中甲供数量
    "RCJ_JG_ZCSL": () => {
        return {
            "type": "rcj",
            "kind": 5,
            "cloumn": getDonorMaterialNumber
        };
    },

    //TODO 人材机 主材类型  中对应数量
    "RCJ_ZCSL": () => {
        //从人材机汇总中取值
        return {
            "type": "rcj",
            "kind": 5,
            "cloumn": "totalNumber"
        };
    },

    //TODO 人材机 设备类型 中甲供数量
    "RCJ_JG_SBSL": () => {
        //从人材机汇总中取值 前提勾选甲供 否则都为0
        return {
            "type": "rcj",
            "kind": 4,
            "cloumn": getDonorMaterialNumber
        };
    },

    //TODO 人材机 设备类型  中对应数量
    "RCJ_SBSL": () => {
        //从人材机汇总中取值
        return {
            "type": "rcj",
            "kind": 4,
            "cloumn": "totalNumber"
        };
    },
    // 甲定人工费市场价
    "JD_RGF_SCJ": () => {
        //ifDonorMaterial  2是甲定
        return {
            "type": "rcj",
            "kind": 1,
            "cloumn": getJDSCJ
        };
    },
    // 甲定材料费市场价
    "JD_CLF_SCJ": () => {
        //ifDonorMaterial  2是甲定
        return {
            "type": "rcj",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": getJDSCJ
        };
    },
    // 甲定机械费市场价
    "JD_JXF_SCJ": () => {
        //ifDonorMaterial  2是甲定
        return {
            "type": "rcj",
            "kind": 3,
            "cloumn": getJDSCJ
        };
    },
    // 甲定主材市场价
    "JD_ZC_SCJ": () => {
        //ifDonorMaterial  2是甲定
        return {
            "type": "rcj",
            "kind": 5,
            "cloumn": getJDSCJ
        };
    },
    // 甲定设备费市场价
    "JD_SB_SCJ": () => {
        //ifDonorMaterial  2是甲定
        return {
            "type": "rcj",
            "kind": 4,
            "cloumn": getJDSCJ
        };
    },
    "ZG_ALL_CL_SCJ": () => {
        //ifProvisionalEstimate  1是暂估
        return {
            "type": "rcj",
            "kind": [2, 5, 6, 7, 8, 9, 10],
            "cloumn": getZGSCJ
        };
    },
    "ZG_SB_SCJ": () => {
        //ifProvisionalEstimate  1是暂估
        return {
            "type": "rcj",
            "kind": 3,
            "cloumn": getZGSCJ
        };
    },
    //其他材料费单价
    "CL_QTFY_DJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": "qtDjPrice"//marketPrice
        };
    },

    "CL_QTFY_HJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": "total"
        };
    },
    //其他材料费定额价
    "CL_QTFY_DEJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": "dePrice"
        };
    },
    //其他材料费总价
    "CL_QTFY_ZJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": [2, 6, 7, 8, 9, 10],
            "cloumn": "total"
        };
    },
    ///其他机械费总价
    "JX_QTFY_ZJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": 3,
            "cloumn": "total"
        };
    },
    ///其他机械费单价
    "JX_QTFY_DJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": 3,
            "cloumn": "qtDjPrice"
        };
    },
    "JX_QTFY_HJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": 3,
            "cloumn": "total"
        };
    },
    ///其他机械费单价
    "JX_QTFY_DEJ": () => {
        return {
            "type": "SPECIAL_RCJ",
            "kind": 3,
            "cloumn": "dePrice"
        };
    },
}

const ccodes = {
    "ZJF": {
        "name": "直接费",
        "mathFormula": "RGF+CLF+JXF+ZCF",
    },
    "RGF": {
        "name": "人工费",
        "mathFormula":Gene.from(["RGF_SCJ","RG_XHL","RG_JSJS"],({RGF_SCJ,RG_XHL,RG_JSJS,_})=>{
            const zipped = _.zip(RGF_SCJ, RG_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*RG_JSJS);
        })//Σ（人工市场价*人工消耗量*计算基数）
    }
    ,
    "CLF_ZC": {
        "name": "正常材料费",
        "mathFormula": Gene.from(["CL_SCJ","CL_XHL","CL_JSJS"],({CL_SCJ,CL_XHL,CL_JSJS,_})=>{
            const zipped = _.zip(CL_SCJ, CL_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*CL_JSJS);
        }),//Σ（材料市场价*材料消耗量*计算基数）[不包括类别为“主材”的材料]
    }
    ,
    "CLF": {
        "name": "材料费",
        "mathFormula": "CLF_ZC+_.sum(CL_QTFY_DJ)",//Σ（材料市场价*材料消耗量*计算基数）[不包括类别为“主材”的材料]
    },
    "JXF_ZC": {
        "name": "正常机械费",
        "mathFormula":Gene.from(["JX_SCJ","JX_XHL","JXF_JSJS"],({JX_SCJ,JX_XHL,JXF_JSJS,_})=>{
            const zipped = _.zip(JX_SCJ, JX_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*JXF_JSJS);
        })//Σ（机械费市场价*机械消耗量*计算基数）
    },
    "JXF": {
        "name": "机械费",
        "mathFormula": "JXF_ZC+_.sum(JX_QTFY_DJ)",
    }
    ,
    "ZCF": {
        "name": "主材费",
        "mathFormula":Gene.from(["ZC_SCJ","ZC_XHL","ZC_JSJS"],({ZC_SCJ,ZC_XHL,ZC_JSJS,_})=>{
            const zipped = _.zip(ZC_SCJ, ZC_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*ZC_JSJS);
        })//Σ（主材市场价*主材消耗量*计算基数）
    }
    ,
    "SBF": {
        "name": "设备费",
        "mathFormula":Gene.from(["SB_SCJ","SB_XHL","SB_JSJS"],({SB_SCJ,SB_XHL,SB_JSJS,_})=>{
            const zipped = _.zip(SB_SCJ, SB_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*SB_JSJS);
        })//Σ（设备市场价*设备消耗量*计算基数）
    },
    "GR": {
        "name": "工日合计",
        "mathFormula": "_.sum(GR_XHL_HJ)",//Σ（类型为人工且单位为工日的消耗量）
    }
    ,
    "RGF_DEJ": {
        "name": "人工费定额价",
        "mathFormula":Gene.from(["RG_DEJ","RG_XHL","RG_JSJS"],({RG_DEJ,RG_XHL,RG_JSJS,_})=>{
            const zipped = _.zip(RG_DEJ, RG_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*RG_JSJS);
        })//Σ（人工定额价*人工消耗量*计算基数）
    }
    ,
    "CLF_DEJ_ZC": {
        "name": "正常材料费定额价",
        "mathFormula":Gene.from(["CL_DEJ","CL_XHL","CL_JSJS"],({CL_DEJ,CL_XHL,CL_JSJS,_})=>{
            const zipped = _.zip(CL_DEJ, CL_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*CL_JSJS);
        })//Σ（材料定额价*材料消耗量*计算基数）[不包括类别为“主材”的材料]
    },
    "CLF_DEJ": {
        "name": "材料费定额价",
        "mathFormula": Gene.from(["DEGCL","CL_QTFY_HJ","CLF_DEJ_ZC"],({DEGCL,CL_QTFY_HJ,CLF_DEJ_ZC,_})=>{
            if(DEGCL&&CL_QTFY_HJ.length>0){
                return CLF_DEJ_ZC+ _.sum(CL_QTFY_HJ.map(i=>i/DEGCL));
            }
            return CLF_DEJ_ZC;
        }),//Σ（材料定额价*材料消耗量*计算基数）[不包括类别为“主材”的材料]
    }
    ,
    "JXF_DEJ_ZC": {
        "name": "正常机械费定额价",
        "mathFormula":Gene.from(["JX_DEJ","JX_XHL","JXF_JSJS"],({JX_DEJ,JX_XHL,JXF_JSJS,_})=>{
            const zipped = _.zip(JX_DEJ, JX_XHL);
            return _.sumBy(zipped, pair => pair[0] * pair[1]*JXF_JSJS);
        })//Σ（机械费定额价*机械消耗量*计算基数）
    },
    "JXF_DEJ": {
        "name": "机械费定额价",
        "mathFormula":Gene.from(["DEGCL","JX_QTFY_HJ","JXF_DEJ_ZC"],({DEGCL,JX_QTFY_HJ,JXF_DEJ_ZC,_})=>{
            if(DEGCL&&JX_QTFY_HJ.length>0){
                return JXF_DEJ_ZC+ _.sum(JX_QTFY_HJ.map(i=>i/DEGCL));
            }
            return JXF_DEJ_ZC;
        }),//Σ（机械费定额价*机械消耗量*计算基数）
    },

    "RCJJC": {
        "name": "人材机价差",
        "mathFormula": "RGFJC+CLFJC+JXFJC+ZCFJC",//RGFJC(单价)+CLFJC(单价)+JXFJC(单价)+ZCFJC(单价)
    },
    "RGFJC": {
        "name": "人工费价差",
        "mathFormula":Gene.from(["RGF_SCJ","RG_XHL","RG_DEJ"],({RGF_SCJ,RG_XHL,RG_DEJ,_})=>{
            const zipped = _.zip(RGF_SCJ, RG_XHL);
            const a1= _.sumBy(zipped, pair => pair[0] * pair[1]);
            const zipped1 = _.zip(RG_DEJ, RG_XHL);
            const a2= _.sumBy(zipped1, pair => pair[0] * pair[1]);
            return a1- a2;
        })//"(RGF_SCJ*RG_XHL)-(RG_DEJ*RG_XHL) Σ（Σ（人工市场价*人工消耗量）-Σ（人工定额价*人工消耗量）
    }
    ,
    "CLFJC": {
        "name": "材料费价差",
        "mathFormula": Gene.from(["CL_SCJ","CL_XHL","CL_DEJ"],({CL_SCJ,CL_XHL,CL_DEJ,_})=>{
            const zipped = _.zip(CL_SCJ, CL_XHL);
            const a1= _.sumBy(zipped, pair => pair[0] * pair[1]);
            const zipped1 = _.zip(CL_DEJ, CL_XHL);
            const a2= _.sumBy(zipped1, pair => pair[0] * pair[1]);
            return a1- a2;
        })//(CL_SCJ*CL_XHL)-(CL_DEJ*CL_XHL) Σ（材料市场价*材料消耗量）-Σ（材料定额价*材料消耗量）[不包括类别为“主材”的材料]
    }
    ,
    "JXFJC": {
        "name": "机械费价差",
        "mathFormula":  Gene.from(["JX_SCJ","JX_XHL","JX_DEJ"],({JX_SCJ,JX_XHL,JX_DEJ,_})=>{
            const zipped = _.zip(JX_SCJ, JX_XHL);
            const a1= _.sumBy(zipped, pair => pair[0] * pair[1]);
            const zipped1 = _.zip(JX_DEJ, JX_XHL);
            const a2= _.sumBy(zipped1, pair => pair[0] * pair[1]);
            return a1- a2;
        })//"(JX_SCJ*JX_XHL)-(JX_DEJ*JX_XHL)",//Σ（机械市场价*机械消耗量）-Σ（机械定额价*机械消耗量）
    }
    ,
    "ZCFJC": {
        "name": "主材费价差",
        "mathFormula":Gene.from(["ZC_SCJ","ZC_XHL","ZC_DEJ"],({ZC_SCJ,ZC_XHL,ZC_DEJ,_})=>{
            const zipped = _.zip(ZC_SCJ, ZC_XHL);
            const a1= _.sumBy(zipped, pair => pair[0] * pair[1]);
            const zipped1 = _.zip(ZC_DEJ, ZC_XHL);
            const a2= _.sumBy(zipped1, pair => pair[0] * pair[1]);
            return a1- a2;
        })// "(ZC_SCJ*ZC_XHL)-(ZC_DEJ*ZC_XHL)",//Σ（主材市场价*主材消耗量）-Σ（主材定额价*主材消耗量）
    }
    ,
    "SBFJC": {
        "name": "设备费价差",
        "mathFormula":Gene.from(["SB_SCJ","SB_XHL","SBF_DEJ"],({SB_SCJ,SB_XHL,SBF_DEJ,_})=>{
            const zipped = _.zip(SB_SCJ, SB_XHL);
            const a1= _.sumBy(zipped, pair => pair[0] * pair[1]);
            const zipped1 = _.zip(SBF_DEJ, SB_XHL);
            const a2= _.sumBy(zipped1, pair => pair[0] * pair[1]);
            return a1- a2;
        })// "(SB_SCJ*SB_XHL)-(SBF_DEJ*SB_XHL)",//Σ（设备市场价*设备消耗量）-Σ（设备定额价*设备消耗量）
    }
    ,
    "JGRGF": {
        "name": "甲供人工费",
        "mathFormula":Gene.from(["RCJ_JG_RGSL","RCJ_RGSL","RGF_SCJ","RG_XHL"],({RCJ_JG_RGSL,RCJ_RGSL,RGF_SCJ,RG_XHL,_})=>{
            const zipped = _.zip(RCJ_JG_RGSL, RCJ_RGSL,RGF_SCJ,RG_XHL);
            const a1= _.sumBy(zipped, pair => (pair[0] / (pair[1]||1))*pair[2]*pair[3]);
            return a1;
        })// `(RCJ_JG_RGSL/(RCJ_RGSL||1))*RGF_SCJ*RG_XHL`,//Σ（人材机汇总中甲供人工数量/人材机汇总中对应人工数量）*当前定额中人工市场价*当前定额中人工消耗量
    }
    ,
    "JGCLF": {
        "name": "甲供材料费",
        "mathFormula": Gene.from(["RCJ_JG_CLSL","RCJ_CLSL","CL_SCJ","CL_XHL"],({RCJ_JG_CLSL,RCJ_CLSL,CL_SCJ,CL_XHL,_})=>{
            const zipped = _.zip(RCJ_JG_CLSL, RCJ_CLSL,CL_SCJ,CL_XHL);
            return _.sumBy(zipped, pair => (pair[0] / (pair[1]||1))*pair[2]*pair[3]);
        })//`(RCJ_JG_CLSL/(RCJ_CLSL||1))*CL_SCJ*CL_XHL`,//Σ（当前定额下材料甲供数量/当前定额下材料数量）*当前定额中材料市场价*当前定额中材料消耗量
    },
    "JGJXF": {
        "name": "甲供机械费",
        "mathFormula":  Gene.from(["RCJ_JG_JXSL","RCJ_JXSL","JX_SCJ","JX_XHL"],({RCJ_JG_JXSL,RCJ_JXSL,JX_SCJ,JX_XHL,_})=>{
            const zipped = _.zip(RCJ_JG_JXSL, RCJ_JXSL,JX_SCJ,JX_XHL);
            return _.sumBy(zipped, pair => (pair[0] / (pair[1]||1))*pair[2]*pair[3]);
        })//`(RCJ_JG_JXSL/(RCJ_JXSL||1))*JX_SCJ*JX_XHL`,//Σ（人材机汇总中甲供材料数量/人材机汇总中对应材料数量）*当前定额中对应材料市场价*当前定额中材料消耗量
    },
    "JGZCF": {
        "name": "甲供主材费",
        "mathFormula": Gene.from(["RCJ_JG_ZCSL","RCJ_ZCSL","ZC_SCJ","ZC_XHL"],({RCJ_JG_ZCSL,RCJ_ZCSL,ZC_SCJ,ZC_XHL,_})=>{
            const zipped = _.zip(RCJ_JG_ZCSL, RCJ_ZCSL,ZC_SCJ,ZC_XHL);
            return _.sumBy(zipped, pair => (pair[0] / (pair[1]||1))*pair[2]*pair[3]);
        })// `(RCJ_JG_ZCSL/(RCJ_ZCSL||1))*ZC_SCJ*ZC_XHL`,//Σ（人材机汇总中甲供主材数量/人材机汇总中对应主材数量）*当前定额中对应主材市场价*当前定额中主材消耗量
    },
    "JGSBF": {
        "name": "甲供设备费",
        "mathFormula": Gene.from(["RCJ_JG_SBSL","RCJ_SBSL","SB_SCJ","SB_XHL"],({RCJ_JG_SBSL,RCJ_SBSL,SB_SCJ,SB_XHL,_})=>{
            const zipped = _.zip(RCJ_JG_SBSL, RCJ_SBSL,SB_SCJ,SB_XHL);
            return _.sumBy(zipped, pair => (pair[0] / (pair[1]||1))*pair[2]*pair[3]);
        })//`(RCJ_JG_SBSL/(RCJ_SBSL||1))*SB_SCJ*SB_XHL`,//Σ（人材机汇总中甲供主材数量/人材机汇总中对应主材数量）*当前定额中对应主材市场价*当前定额中主材消耗量
    }
    , "FHZMZJF_DEJ": {
        "name": "防寒子目定额价直接费",
        "mathFormula": `RGF_DEJ+CLF_DEJ+JXF_DEJ`,//Σ（设置为暂估的且类别为“材料/主材”的市场价*消耗量）
    }
    ,
    "JDRGF": {
        "name": "甲定人工费",
        "mathFormula": Gene.from(["JD_RGF_SCJ","RG_XHL","RG_JSJS"],({JD_RGF_SCJ,RG_XHL,RG_JSJS,_})=>{
            const zipped = _.zip(JD_RGF_SCJ, RG_XHL,RG_JSJS);
            return _.sumBy(zipped, pair => pair[0]*pair[1]*pair[2]);
        })//`JD_RGF_SCJ*RG_XHL*RG_JSJS`,//Σ（人工市场价*人工消耗量*计算基数）
    },
    "JDCLF": {
        "name": "甲定材料费",
        "mathFormula": Gene.from(["JD_CLF_SCJ","CL_XHL","CL_JSJS"],({JD_CLF_SCJ,CL_XHL,CL_JSJS,_})=>{
            const zipped = _.zip(JD_CLF_SCJ, CL_XHL,CL_JSJS);
            return _.sumBy(zipped, pair => pair[0]*pair[1]*pair[2]);
        })//`JD_CLF_SCJ*CL_XHL*CL_JSJS`,//Σ（材料市场价*人工消耗量*计算基数）
    },
    "JDJXF": {
        "name": "甲定机械费",
        "mathFormula": Gene.from(["JD_JXF_SCJ","JX_XHL","JXF_JSJS"],({JD_JXF_SCJ,JX_XHL,JXF_JSJS,_})=>{
            const zipped = _.zip(JD_JXF_SCJ, JX_XHL,JXF_JSJS);
            return _.sumBy(zipped, pair => pair[0]*pair[1]*pair[2]);
        })//`JD_JXF_SCJ*JX_XHL*JXF_JSJS`,//Σ（机械费市场价*机械消耗量*计算基数）
    },
    "JDZCF": {
        "name": "甲定主材费",
        "mathFormula":  Gene.from(["JD_ZC_SCJ","ZC_XHL","ZC_JSJS"],({JD_ZC_SCJ,ZC_XHL,ZC_JSJS,_})=>{
            const zipped = _.zip(JD_ZC_SCJ, ZC_XHL,ZC_JSJS);
            return _.sumBy(zipped, pair => pair[0]*pair[1]*pair[2]);
        })//`JD_ZC_SCJ*ZC_XHL*ZC_JSJS`,//Σ（主材市场价*主材消耗量*计算基数）
    },
    "JDSBF": {
        "name": "甲定设备费",
        "mathFormula": Gene.from(["JD_SB_SCJ","SB_XHL","SB_JSJS"],({JD_SB_SCJ,SB_XHL,SB_JSJS,_})=>{
            const zipped = _.zip(JD_SB_SCJ,SB_XHL,SB_JSJS);
            return _.sumBy(zipped, pair => pair[0]*pair[1]*pair[2]);
        }),//`JD_SB_SCJ*SB_XHL*SB_JSJS`,//Σ（设备市场价*设备消耗量*计算基数）
    },
    "ZGJCLFHJ": {
        "name": "暂估价材料费合计",
        "mathFormula":Gene.from(["ZG_ALL_CL_SCJ","ALL_CL_XHL"],({ZG_ALL_CL_SCJ,ALL_CL_XHL,_})=>{
            const zipped = _.zip(ZG_ALL_CL_SCJ,ALL_CL_XHL);
            return _.sumBy(zipped, pair => pair[0]*pair[1]);
        })// `ZG_ALL_CL_SCJ*ALL_CL_XHL`,//Σ（设置为暂估的且类别为“材料/主材”的市场价*消耗量）
    }
    ,
    "ZGJSBFHJ": {
        "name": "暂估价设备费合计",
        "mathFormula":Gene.from(["ZG_SB_SCJ","SB_XHL"],({ZG_SB_SCJ,SB_XHL,_})=>{
            const zipped = _.zip(ZG_SB_SCJ,SB_XHL);
            return _.sumBy(zipped, pair => pair[0]*pair[1]);
        })// `ZG_SB_SCJ*SB_XHL`,//Σ（设置为暂估的且类别为“设备”的市场价*消耗量）
    }
    ,
    "JGCLF_DEJHJ": {
        "name": "甲供材料费定额价合计",
        "mathFormula": Gene.from(["RCJ_JG_CLSL","RCJ_CLSL","CL_DEJ","CL_XHL","RCJ_JG_ZCSL","RCJ_ZCSL","ZC_DEJ","ZC_XHL"],(params)=>{
            const {RCJ_JG_CLSL,RCJ_CLSL,CL_DEJ,CL_XHL,RCJ_JG_ZCSL,RCJ_ZCSL,ZC_DEJ,ZC_XHL,_} = params;
            const zipped = _.zip(RCJ_JG_CLSL,RCJ_CLSL,CL_DEJ,CL_XHL);
            const one= _.sumBy(zipped, pair => (pair[0]/(pair[1]||1))*pair[2]*pair[3]);
            const zipped1 = _.zip(RCJ_JG_ZCSL,RCJ_ZCSL,ZC_DEJ,ZC_XHL);
            const two= _.sumBy(zipped1, pair => (pair[0]/(pair[1]||1))*pair[2]*pair[3])
            return one+two;
        })//`(RCJ_JG_CLSL/(RCJ_CLSL||1))*CL_DEJ*CL_XHL+(RCJ_JG_ZCSL/(RCJ_ZCSL||1))*ZC_DEJ*ZC_XHL`,//Σ【（人材机汇总中甲供材料数量/人材机汇总中对应材料数量）*当前定额中材料定额价*当前定额中材料消耗量】+【（人材机汇总中甲供主材数量/人材机汇总中对应主材数量）*当前定额中主材定额价*当前定额中主材消耗量】
    }
    ,
    "JGCLF_SCJHJ": {
        "name": "甲供材料费市场价合计",
        "mathFormula": "JGCLF+JGZCF",//Σ（甲供材料费+甲供主材费）
    }
    ,
    "JGCLFJCHJ": {
        "name": "甲供材料费价差合计",
        "mathFormula": "JGCLF_DEJHJ+JGCLF_SCJHJ",//Σ（甲供材料费+甲供主材费）
    }
};
let zmdm = ["ZJF", "RGF", "CLF", "JXF", "GR", "ZCF", "SBF", "RGF_DEJ", "CLF_DEJ", "JXF_DEJ", "FHZMZJF_DEJ"];
let rcj = ["RCJJC", "RGFJC", "CLFJC", "JXFJC", "ZCFJC", "SBFJC", "JGRGF", "JGCLF", "JGJXF", "JGZCF", "JDCLF", "JDJXF", "JDZCF", "JDSBF", "JDRGF", "JGSBF", "ZGJCLFHJ", "ZGJSBFHJ", "JGCLF_DEJHJ", "JGCLF_SCJHJ", "JGCLFJCHJ"];
let zmdmArr = [];
let rcjArr = [];

let allArr = [];
zmdm.forEach(item => {
    zmdmArr.push({code: item, ...ccodes[item], unitPrice: 0,mathFormula:""});
});
rcj.forEach(item => {
    rcjArr.push({code: item, ...ccodes[item], unitPrice: 0,mathFormula:""});
});
for (const item in ccodes) {
    allArr.push({code: item, ...ccodes[item], unitPrice: 0,mathFormula:""});
}
const CostCodeTypeEnum = Object.freeze(Object.fromEntries([
    ['ZMDM', {code: 1, desc: '子目代码'}],
    ['RCJ', {code: 2, desc: '人材机'}],
]));

module.exports = {ccodes, baseFn, zmdmArr, rcjArr, allArr, CostCodeTypeEnum, kemap}

