const _ = require('lodash');
const {ccodes, baseFn} = require("../compute/rules/chargecode");
const {AnalyzeCore, UPCTemplateVO} = require("../core/UPCTemplateVO");
const {UPCCupmuteDe} = require("../compute/UPCCupmute");
const handlebars = require('handlebars');
const {Snowflake} = require("../../utils/Snowflake");
const {options2012} = require("../core/baseData");
const {ObjectUtils} = require("../../utils/ObjectUtils");
let editorMap = new Map();
const template = handlebars.compile(`
   function hasCircularReference(obj) {
        JSON.stringify(obj); // 将对象转换为字符串形式进行测试
   }
     {{#each list}}
        let {{this}}={};
     {{/each}}
     {{#each groupList}}
         {{#each list}}
            {{../code}}.{{this}}={{this}};
          {{/each}}
     {{/each}}
      {{#each groupList}}
         hasCircularReference({{code}});
      {{/each}}
`);

class Editorplaygroud extends UPCCupmuteDe {
    constructor({de, constructId, singleId, unitId, optionData, allData}) {
        super(de, constructId, singleId, unitId, allData);
        this.optionData = optionData || {};
        this.sequenceNbr = this.optionData.sequenceNbr || "";//当前需要操作的id
        this.isRefresh = false;//是否重新计算过
        this.isGetRcj = false;//缓存是否获取取费文件
        this.isGetFlv = false;//缓存是否获取费率文件
    }

    static create({de, constructId, singleId, unitId, optionData, allData}) {
        if (editorMap.has(de.sequenceNbr)) {
            let editor = editorMap.get(de.sequenceNbr);
            editor.optionData = optionData;
            editor.sequenceNbr = optionData.sequenceNbr;
            return editor;
        }
        return new Editorplaygroud({de, constructId, singleId, unitId, optionData, allData});
    }

    static get(deid) {
        return editorMap.get(deid);
    }

    static remove(deid) {
        return editorMap.delete(deid);
    }

    prepare(useMainFile) {
        //获取取费文件
        if (!this.isGetRcj) {
            this.rcjContext.getRcjList();
            this.isGetRcj = true;
        }
        if (!this.isGetFlv) {
            this.getFeeFile(useMainFile);
            this.isGetFlv = true;
        }
        this.getUpcTemplateList();
    }

    //准备数据 获取单价构成数据
    getUpcTemplateList() {
        if (this.upcTemplateList.length == 0) {
            this.upcTemplateList = _.cloneDeep(this.unit.feeBuild[this.de.sequenceNbr]);
        }
    }

    getOption(value) {
        for (const {label, value: v} of options2012) {
            if (v == value) {
                return label;
            }
        }
    }

    resetRate() {
        //应用的时候  规费  安文费 默认走取费表里取
        //规费 安文费
        this.upcTemplateList.forEach((item, index) => {
            let rate = "";
            switch (item.typeCode) {
                case "UPC_AWF": {//安文费
                    rate = this.unitFeeFile.anwenRateBase;
                    item.isLock = true;
                    break;
                }
                case "UPC_GF": {//规费
                    rate = this.unitFeeFile.fees
                    item.isLock = true;
                    break;
                }
                default : {
                    rate = item.rate;
                    break;
                }
            }
            item.rate = rate;
        });
    }
    //查找并替换
    findAndSwap() {
        let {column, value} = this.optionData;
        this.upcTemplateList.forEach((item, index) => {
            if (item.sequenceNbr == this.sequenceNbr) {
                item[column] = value;
                if (column == "typeCode") {
                    item.type = this.getOption(value);
                }
            }
        });
        if (column == "typeCode" && ["UPC_AWF", "UPC_GF"].includes(value)) {
            this.resetRate();
        }
        this.findAndSwapDesc();
    }

    /*更新名称 基数说明*/
    findAndSwapDesc() {
        //获取依赖
        let items = {};
        let currSelect = {};
        this.upcTemplateList.forEach((item, index) => {
            items[item.code] = item.name;
            if (item.sequenceNbr == this.sequenceNbr) {
                currSelect = item;
            }
        });
        this.upcTemplateList.forEach((item, index) => {
            let paramsKey = AnalyzeCore.renderParams(item.caculateBase);
            if (paramsKey) {
                paramsKey = _.sortBy(paramsKey, "code").reverse();
            }
            if (paramsKey.includes(currSelect.code) || item.sequenceNbr == this.sequenceNbr) {
                let caculateBase = _.cloneDeep(item.caculateBase);
                paramsKey.forEach(key => {
                    if (items[key]) {
                        caculateBase = caculateBase.replace(new RegExp(key, 'g'), items[key]);
                    }
                    if (ccodes[key]) {
                        caculateBase = caculateBase.replace(new RegExp(key, 'g'), ccodes[key].name);
                    }
                });
                item.desc = caculateBase;
            }

        });
    }

    updateRow() {
        this.updateCheck()
        this.findAndSwap();
        let {column} = this.optionData;
        if (column != "name") {
            this.isRefresh = true;
            this.instanceMap = {};
            this.analyze();
            this.realCupmute();
        }
    }

    deleteRow() {
        let {sequenceNbr} = this.optionData;
        //检查依赖项 如果有依赖则不能删除
        this.deleteCheckAndSave();
        this.analyze();
        this.realCupmute();
        this.isRefresh = true;
    }

    insertRow() {
        let {sequenceNbr} = this.optionData;
        let index = _.findIndex(this.upcTemplateList, item => item.sequenceNbr == sequenceNbr);
        let item = _.cloneDeep(this.upcTemplateList[index]);
        for (const itemKey in item) {
            item[itemKey] = "";
        }
        item.sequenceNbr = Snowflake.nextId();
        this.upcTemplateList.splice(index + 1, 0, item);
    }

    changeIndex(op) {
        let {sequenceNbr} = this.optionData;
        if (op) {//向上移动
            let index = _.findIndex(this.upcTemplateList, item => item.sequenceNbr == sequenceNbr);
            if (index > 0) {
                this.upcTemplateList.splice(index - 1, 0, this.upcTemplateList.splice(index, 1)[0]);
            }
        } else {
            let index = _.findIndex(this.upcTemplateList, item => item.sequenceNbr == sequenceNbr);
            if (index < this.upcTemplateList.length - 1) {
                this.upcTemplateList.splice(index + 1, 0, this.upcTemplateList.splice(index, 1)[0]);
            }
        }
    }

    /**
     * 临时计算 并保存到缓存
     * @param isUpdateAWForGF
     */
    cupmutedAndSave() {
        let {option, column, value} = this.optionData;
        switch (option) {
            case "UPDATE": {
                this.updateRow();
                break;
            }
            case "DELETE": {
                this.deleteRow();
                break;
            }
            case "ADD": {
                this.insertRow();
                break;
            }
            case "UP": {
                this.changeIndex(true);
                break;
            }
            case "DOWD": {
                this.changeIndex(false);
                break;
            }

        }
        this.after();
    }

    after() {
        editorMap.set(this.de.sequenceNbr, this);
    }

    /**
     * 删除检查并更新
     * @returns {*}
     */
    deleteCheckAndSave() {
        let removeRow = this.upcTemplateList.filter(item => item.sequenceNbr == this.sequenceNbr)[0];
        let list = this.upcTemplateList.filter(item => item.sequenceNbr != this.sequenceNbr);
        for (let i = 0; i < list.length; i++) {
            //更新编辑的那一行
            let caculateBase = list[i].caculateBase;
            let paramsKey = AnalyzeCore.renderParams(caculateBase);
            if (paramsKey.includes(removeRow.code)) {
                throw new Error("当前删除的行存在依赖项，请先删除依赖项");
            }
        }
        this.upcTemplateList = list;
    }

    /**
     * 编辑检查并更新
     */
    updateCheck() {
        let {option, column, value} = this.optionData;
        if (!column || !value) return;
        switch (column) {
            //编辑code
            case "code": {
                this.checkCodeAndSave();
                break;
            }
            //修改计算基数
            case "caculateBase": {
                this.checkCaculateBaseAndSave();
                break;
            }

        }

    }

    realCheckCaculateBase(paramsKey, caculateBase) {
        //验证计算基数
        try {
            let fn = AnalyzeCore.renderFunction(paramsKey, caculateBase)
            let params = {}
            if (paramsKey.length > 0) {
                paramsKey.forEach(i => {
                    params[i] = 1;
                })
            }
            fn(params);
        } catch (e) {
            throw new Error("计算基数填写有误");
        }
    }
    /**
     * 检查计算基数并更新
     */
    checkCaculateBaseAndSave() {
        let {value} = this.optionData;
        //拿到计算基数
        let caculateBase = value;
        //获取到当前行
        let upc = this.upcTemplateList.filter(item => item.sequenceNbr == this.sequenceNbr)[0];
        if (!upc.code) return;
        let list = this.upcTemplateList.filter(item => item.sequenceNbr != this.sequenceNbr);
        let paramsKey = AnalyzeCore.renderParams(caculateBase);
        if (paramsKey.includes(upc.code)) throw new Error("不能引用自己");
        this.realCheckCaculateBase(paramsKey, caculateBase);
        let grouplist = _.groupBy(list, item => item.code);
        paramsKey.forEach(key => {
            if (!ccodes[key] && !baseFn[key] && !grouplist[key]) throw new Error(key + "变量名不存在，请重新输入");
            if (ObjectUtils.isNotEmpty(grouplist[key]) && ObjectUtils.isEmpty(grouplist[key][0].caculateBase)) {
                throw new Error(key + "对应的计算技术不能为空");
            }
        });
        let dependenceList = [];
        this.upcTemplateList.forEach(item => {
            if (item.sequenceNbr == this.sequenceNbr) {
                dependenceList.push({code: item.code, caculateBase: caculateBase})
            } else {
                dependenceList.push({code: item.code, caculateBase: item.caculateBase});
            }
        });
        //分析依赖关系
        this.analyzeDependence(dependenceList);

    }

    //分析依赖关系
    analyzeDependence(dependenceList) {
        let list = new Set();
        let groupList = [];
        for (let i = 0; i < dependenceList.length; i++) {
            let {code, caculateBase} = dependenceList[i];
            if (ObjectUtils.isEmpty(code) || ObjectUtils.isEmpty(caculateBase)) continue;
            list.add(code);
            let paramsKey = AnalyzeCore.renderParams(caculateBase);
            paramsKey.forEach(item => {
                list.add(item);
            });
            groupList.push({code, list: paramsKey});
        }
        this.checkCircularDependence({list, groupList});
    }

    /**
     * 检查code之间的循环依赖
     * @param data
     */
    checkCircularDependence(data) {
        let fn = new Function(template(data));
        try {
            fn();
        } catch (e) {
            throw new Error("循环依赖了");
        }
    }

    /**
     * 检查单价构成 费用代号 并替换
     */
    checkCodeAndSave() {
        let {value} = this.optionData;
        if (value) {
            let paramsKey = AnalyzeCore.renderParams(value);
            let obj = {};
            paramsKey.forEach(item => {
                obj[item] = 1;
            })

            try {
                let fn = AnalyzeCore.renderFunction(paramsKey, value);
                fn(obj);
            } catch (e) {
                throw new Error("变量名不能为数字起始");
            }
            if (paramsKey.length == 0) throw new Error("变量名不能为数字");
        }

        //获取到不包含当前code 的行
        let updateRow = this.upcTemplateList.filter(item => item.sequenceNbr == this.sequenceNbr)[0];
        let list = this.upcTemplateList.filter(item => item.sequenceNbr != this.sequenceNbr);
        let grouplist = _.groupBy(list, item => item.code);
        if (grouplist[value]) throw new Error("变量名冲突，请重新输入");
        if (ccodes[value]) throw new Error("与系统变量名冲突，请重新输入");
        if (baseFn[value]) throw new Error("与系统变量名冲突，请重新输入");
        //执行更新逻辑
        for (let i = 0; i < list.length; i++) {
            //更新编辑的那一行
            let caculateBase = list[i].caculateBase;
            let paramsKey = AnalyzeCore.renderParams(caculateBase);
            if (paramsKey.includes(updateRow.code)) {
                //把依赖的值修改成依赖后的值
                list[i].caculateBase = caculateBase.replace(updateRow.code, value);
            }
        }
        updateRow.code = value;
    }
}

module.exports = {Editorplaygroud}
