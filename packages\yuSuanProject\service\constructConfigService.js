'use strict';

const { Service,Log } = require('../../../core');
const {SingleProject} = require("../model/SingleProject");
const {Snowflake} = require("../utils/Snowflake");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");

/**
 * 示例服务
 * @class
 */
class ConstructConfigService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /***
     * 获取项目配置信息
     * @param unitId
     * @return {Promise<void>}
     */
    getConstructConfigByConstructId(arg) {
        let constructId = arg.constructId;
        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
        let result = {
            gfId: projectObj.gfId,
            awfId: projectObj.awfId,
            rgfId: projectObj.rgfId,
            qdStandardId:projectObj.qdStandardId,
            deStandardId:projectObj.deStandardId,
            areaCode:projectObj.ssCity,
            constructId:projectObj.sequenceNbr,
            taxMode:projectObj.projectTaxCalculation.taxCalculationMethod,
            deStandardReleaseYear:projectObj.deStandardReleaseYear
        }
        return result;

    }

}

ConstructConfigService.toString = () => '[class ConstructConfigService]';
module.exports = ConstructConfigService;
