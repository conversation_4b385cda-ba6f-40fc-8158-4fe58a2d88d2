'use strict';

const {Service, Log} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {SqlUtils} = require("../utils/SqlUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {BaseDeRelation} = require("../model/BaseDeRelation");
const {BaseDe} = require("../model/BaseDe");
const {TaxCalculationMethodEnum} = require("../enum/TaxCalculationMethodEnum");
const {In} = require("typeorm");
const ConstantUtil = require("../enum/ConstantUtil");
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../utils/NumberUtil");

/**
 * 国标定额service
 */
class BaseDeRelationService extends Service {

    constructor(ctx) {
        super(ctx);
        this._itemBillProjectOptionService = this.service.yuSuanProject.itemBillProjectOptionService;
        this._stepItemCostService = this.service.yuSuanProject.stepItemCostService;
    }

    baseDeRelationDao = this.app.appDataSource.manager.getRepository(BaseDeRelation);


    /**
     * 获取子定额数据
     * @param parentDe   父定额
     * @param deCoefficient  变量描述
     * @param relationContent 字母指引选项   显示所有传all
     * @param updateDeList 修改了工程量的子定额
     */
    async queryChildrenDe(parentDe,deCoefficient,relationContent,updateDeList,qd){
        if(ObjectUtils.isEmpty(deCoefficient)){
            deCoefficient=await this.service.yuSuanProject.baseDeRelationVariableCoefficientService.queryVariableCoefficient(parentDe,qd);
        }
        let code=parentDe.bdCode?parentDe.bdCode:parentDe.fxCode;
        let result= await this.baseDeRelationDao.find({
            where: {
                libraryCode: parentDe.libraryCode,
                deCodeF: code
            }
        });
        //过滤子定额
        if(relationContent!="显示所有"){
            result= result.filter(de=>de.relationContent==relationContent);
        }
        let resultChildrenDe= [];
        let libraryCodeZList=[...new Set( result.map(code=>code.libraryCodeZ))];
        for(const lc of libraryCodeZList){
            let strings = result.filter(z=>z.libraryCodeZ==lc).map(code=>code.deCodeZ);
            let newVar = await  this.service.yuSuanProject.baseDeService.selectDeByLibraryCode(lc,strings);
            resultChildrenDe.push(...newVar);
        }



       let queryVariableMap1 = this.queryVariableMap(deCoefficient);

        resultChildrenDe.forEach(de=>{
            //给子定额设置一个关联的父定额id 方便后期进行关联计算
           if(ObjectUtils.isEmpty(de.parentDeId)){
               de.parentDeId=parentDe.sequenceNbr
           }

           let  quantityExpression;
           //处理修改的子定额工程量
            if(ObjectUtils.isNotEmpty(updateDeList)){
                let find = updateDeList.find(upde=>upde.sequenceNbr==de.sequenceNbr);
                if(ObjectUtils.isEmpty(find)){
                    //获取关联定额
                    let baseDeRelation = result.find(dr=>dr.deCodeZ==de.deCode);
                    quantityExpression=baseDeRelation.quantity;
                    de.quantityExpression=baseDeRelation.quantity;
                }else {
                    quantityExpression=find.quantityExpression;
                    de.quantityExpression=quantityExpression;
                }
            }else {
                //获取关联定额
                let baseDeRelation = result.find(dr=>dr.deCodeZ==de.deCode);
                quantityExpression=baseDeRelation.quantity;
                de.quantityExpression=baseDeRelation.quantity;
            }

           //计算表达式
            let valueMap = queryVariableMap1.valueMap;
            let varr = queryVariableMap1.varr;
            const operatorRegex = /[+\-*\/]/;
            for (let varrElement of varr) {
                //处理  GCL*S1   GCL*S  由于规则中的S 导致的报错
                const parts = de.quantityExpression.split(/[+\-*\/]/);
                if (parts.includes(varrElement)){
                    if(varrElement=="S"){
                        this.replaceStrS(de,valueMap.get(varrElement));
                    }else {
                        de.quantityExpression = de.quantityExpression.replaceAll(varrElement, ObjectUtils.isEmpty(valueMap.get(varrElement)) ? 0 : valueMap.get(varrElement));
                    }

                }
            }
            //计算数值
            de.quantity=NumberUtil.numberScale6(eval( de.quantityExpression));
            de.quantityExpression=quantityExpression;



        });

        //根据
        const groupMap = result.reduce((result, currentItem) => {
            // 清单的工作内容作为map的key
            (result[currentItem.relationContent] = result[currentItem.relationContent] || []).push(currentItem);
            return result;
        }, {});

        let listCode=[...new Set( result.map(d=>d.relationContent))];

        //将groupMap 中的数据换成de数据
        for (const value of listCode) {
            let mapElement = groupMap[value];
            let arr=[];
            for (const de of mapElement) {
                let find = resultChildrenDe.find(d=>d.deCode==de.deCodeZ);
                if(ObjectUtils.isNotEmpty(find)){
                    arr.push(find);
                }

            }
            groupMap[value]=arr;
        }


        let map = Object.keys(groupMap).map(key => {
            return {
                jobContent: key,
                children:    groupMap[key].sort((a, b)=> a.sortNo -b.sortNo),
                sequenceNbr: Snowflake.nextId()
            }
        });


       let resultData=[{
           jobContent:result[0].libraryName,
           children:map,
           sequenceNbr: Snowflake.nextId()
           }
        ];
        this.setDipNo(resultData);
        return  resultData;
   }

   setDipNo(resultData){
        let dispNo=0;
        for(const dc of resultData){
            dispNo++;
            dc.dispNo=dispNo;
            //章节
            if(ObjectUtils.isNotEmpty(dc.children)){
                for(const zj of dc.children){
                    dispNo++;
                    zj.dispNo=dispNo;
                    if(ObjectUtils.isNotEmpty(zj.children)){
                        for(const de of zj.children){
                            if(ObjectUtils.isNotEmpty(de)){
                                dispNo++;
                                de.dispNo=dispNo;
                            }

                        }
                    }
                }
            }
        }

   }


   replaceCharAt(str, index, replacement) {
        return str.slice(0, index) + replacement + str.slice(index + 1);
    }


    replaceStrS(de,value){
        const regex = /S(?!\d)/g;
        let map1 = [...de.quantityExpression.matchAll(regex)].map(match => match.index);
        //表示有S
        if(ObjectUtils.isNotEmpty(map1)){
            de.quantityExpression= this.replaceCharAt(de.quantityExpression,map1[0],value);
            this.replaceStrS(de);
        }else {
            return;
        }
    }




   queryVariableMap(variableCoefficient){
       let  valueMap=new Map();
       let varr=[];
       for(const vf of variableCoefficient){
           valueMap.set(vf.variableCode,vf.value);
           varr.push(vf.variableCode)
       }
       return {
           valueMap:valueMap,
           varr:varr
       };
   }


    /**
     * 获取子目指引
     */
   async getSubitemGuidance(parentDe){
        //根据父定额编码和定额库编码获取relation_content  字段去重
        let code=parentDe.bdCode?parentDe.bdCode:parentDe.fxCode;
        let result= await this.baseDeRelationDao.find({
            where: {
                libraryCode: parentDe.libraryCode,
                deCodeF: code
            }
        });
        let strings = result.map(de=>de.relationContent);
        if(ObjectUtils.isNotEmpty(strings)){
            return  ["显示所有",...new Set(strings)];
        }else {
            return  [...new Set(strings)];
        }

    }


    /**
     * 关联定额的插入子定额数据
     * @param params
     * @returns {Promise<[]>}
     */
    async saveGlDeArray(params) {
        // 在原定额索引插入接口入参的基础上增加 deArray定额数组
        let {deArray, constructId, singleId, unitId, pointLine, kind, indexId, unit, rootLineId, rcjFlag, fbfxOrCsxm,deCoefficient} = params;

        if (ObjectUtils.isEmpty(deArray) || ObjectUtils.isEmpty(deArray[0])) {
            throw new Error("deArray is null");
        }
        if (ObjectUtils.isEmpty(deArray[0].libraryCode)) {
            throw new Error("de libraryCode is null");
        }
        if (ObjectUtils.isEmpty(fbfxOrCsxm)) {
            throw new Error("fbfxOrCsxm is null. fbfxOrCsxm should fbfx or csxm");
        }

        // 选中行，这里将pointLine赋值给selectLine，因为pointLine在用于插入空白行和数据行时所指的行不一致。在插入空白行时，pointLine指的是选中某行（比如清单行或定额行）插入空白行；在插入数据行时，pointLine指的是选中的空白行插入数据。
        let selectLine = pointLine;

        if ("fbfx" == fbfxOrCsxm) {
            // 循环deArray数组，插入子目
            let result = [];
            for (let deItem of deArray) {
                // selectLine是数据定额行，即选中定额数据行插入定额。那么就需先插入空白定额行再在空白行内插入定额数据行
                // 插入空白定额行
                let blankLine = await this._itemBillProjectOptionService.insertLine(constructId, singleId, unitId, selectLine, { 'kind': '04' }, rootLineId);
                // 插入数据行, ps: 此时的pointLine为空白行, 即选中空白行插入数据
                params.pointLine = blankLine.data;
                params.indexId = deItem.sequenceNbr;    // 将定额的id赋值给indexId
                params.libraryCode = deItem.libraryCode;    // 将base定额的libraryCode赋值给libraryCode
                params.unit = deItem.unit;    // 将base定额的unit赋值给unit
                params.kind = blankLine.data.kind;
                let res = await this._itemBillProjectOptionService.fillDataFromIndexPageFullEdition(params);
                await this.updateChildrenDe(res,constructId, singleId, unitId,deItem,1,deCoefficient);
                result.push(res);
            }
            return result;
        } else {
            // csxm
            // 循环deArray数组，插入子目
            let result = [];
            for (let deItem of deArray) {
                // 如果选中行是清单(即选中清单插入定额)时，直接插入定额空白行及定额数据行
                // 选中行是定额，即选中的空白定额行或定额数据行
                // 判断是空白定额行还是定额数据行
                // 判断selectLine是空白定额行，还是定额数据行。
                if (ObjectUtils.isNotEmpty(selectLine.bdCode) || ObjectUtils.isNotEmpty(selectLine.fxCode)) {
                    // selectLine是数据定额行，即选中定额数据行插入定额。那么就需先插入空白定额行再在空白行内插入定额数据行
                    // 插入空白定额行
                    let blankLine =await this._stepItemCostService.save(constructId, singleId, unitId, selectLine, {"kind": "04"}, rootLineId);
                    // 插入数据行, ps: 此时的pointLine为空白行, 即选中空白行插入数据
                    params.pointLine = blankLine.data;
                    params.indexId = deItem.sequenceNbr;    // 将定额的id赋值给indexId
                    params.libraryCode = deItem.libraryCode;    // 将base定额的libraryCode赋值给libraryCode
                    params.unit = deItem.unit;    // 将base定额的unit赋值给unit
                    params.kind = blankLine.data.kind;
                    let res = await this._stepItemCostService.fillDataFromIndexPageFullEdition(params);
                    await this.updateChildrenDe(res,constructId, singleId, unitId,deItem,2,deCoefficient);
                    result.push(res);
                }
            }
            return result;
        }

    }


    /**
     * 修改子定额数据
     * @param res
     * @param constructId
     * @param singleId
     * @param unitId
     */
    updateChildrenDe(res,constructId, singleId, unitId,glDe,type,deCoefficient){

        let queryVariableMap1 = this.queryVariableMap(deCoefficient);
        let valueMap = queryVariableMap1.valueMap;
        let varr = queryVariableMap1.varr;
        //只保留公式中的GCL
        for (let varrElement of varr) {
            //处理  GCL*S1   GCL*S  由于规则中的S 导致的报错
            const parts = glDe.quantityExpression.split(/[+\-*\/]/);
            if (parts.includes(varrElement) && varrElement!="GCL"){
                if(varrElement=="S"){
                    this.replaceStrS(glDe,valueMap.get(varrElement));
                }else {
                    glDe.quantityExpression = glDe.quantityExpression.replaceAll(varrElement, ObjectUtils.isEmpty(valueMap.get(varrElement)) ? 0 : valueMap.get(varrElement));
                }
            }
        }

        let allData;
        if(type==1){
            allData= PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        }else {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }
        res.data=allData.getAllNodes().find(de=>de.sequenceNbr==res.data.sequenceNbr);
        this.service.yuSuanProject.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(constructId, singleId, unitId, glDe);
        res.data.quantityExpression=glDe.quantityExpression;
        res.data.quantity=glDe.quantity;
        res.data.parentDeId=glDe.parentDeId;
        //重新计算定额单价构成
        // 4.处理单价构成
        this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, res.data.sequenceNbr, true, allData);
    }



}

BaseDeRelationService.toString = () => '[class BaseDeRelationService]';
module.exports = BaseDeRelationService;
