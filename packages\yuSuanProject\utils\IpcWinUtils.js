const {BrowserWindow} = require("electron");
const {NumberUtil} = require("./NumberUtil");

/**
 * IPC请求
 */
class IpcWinUtils{


    /**
     * 子窗口发送统计消息
     * @param channel 管道
     * @param success 成功
     * @param total   总数
     */
    async childWinSendCount(channel,success,total){
        if (success > total){
            return;
        }

        let data ={
            succeed:success,
            notSuccess:total-success,
            total:total,
            percent:Math.floor(total==0?100:NumberUtil.divide(success,total)*100) //如果total为0 进度条认为已到 100%
        }
        let childWin = BrowserWindow.fromId(global.windowMap.get(channel));
        childWin.webContents.send(channel, data);
    }

     childWinSend(channel,id,data){
        let childWin = BrowserWindow.fromId(global.windowMap.get(id));
        childWin.webContents.send(channel, data);
    }


}

module.exports = {
    IpcWinUtils: new IpcWinUtils()
};

