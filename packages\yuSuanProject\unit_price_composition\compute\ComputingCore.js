const {AnalyzeCore} = require("../core/UPCTemplateVO");
const _ = require("lodash");
const {NumberUtil} = require("../../utils/NumberUtil");

class CupmuteContext{
    constructor() {
        //规则的定义
        this.definitionMap = {};
        //规则的实例
        this.instanceMap= {};
    }

    cupmutefilter() {
        return true;
    }
    convertValue(value) {
        return NumberUtil.numberScale(value, 2);
    }
    //解析基础参数
    analyzeBaseFn(fns){
        for (let key in fns){
            let fvalue =fns[key];
            let value =null;
            if(typeof fvalue == "function"){
                value = this.getValue(fvalue())
            }else {
                value = fvalue;
            }
            this.instanceMap[key] = value;
            this.definitionMap[key] =fvalue;
        }
    }
    analyzeCoreRules(rules){
        for (const rulesKey in rules) {
            this.definitionMap[rulesKey]=rules[rulesKey].mathFormula;
        }
    }
    //解析模板

    //解析参数值
    parseParams(param){
        if(param in this.instanceMap){
            return this.instanceMap[param];
        }
        let caculate = this.definitionMap[param];
        let arr = AnalyzeCore.renderParams(caculate);
        if(arr.length>0){
            //递归解析参数
            let paramsObj ={};
            arr.forEach(item => {
                paramsObj[item] = this.parseParams(item);
            });

            let fn = AnalyzeCore.renderFunction(arr,caculate);
            this.instanceMap[param] = this.cupmutefilter(param) ? this.callFn(fn, paramsObj, arr, param) : 0;
        }else {
            this.instanceMap[param] = Number(caculate);
        }
        return this.instanceMap[param];
    }
    //计算函数
    callFn(fn,paramsObj,arr,param){
        let length =0;
        //判断参数中是否有数组有数组的情况需要循环计算累计
        for (const argumentsKey in paramsObj) {
            if(_.isArray(paramsObj[argumentsKey])){
                let length1 = paramsObj[argumentsKey].length;
                if (length1 > length) length = length1;
            }else {
                let v = paramsObj[argumentsKey];
                if(typeof v =="object"){
                    paramsObj[argumentsKey] = this.runtimeGetvalue(v,param);
                }
            }
        }
        let value =0;
        if(length){
            for(let i=0;i<length;i++){
                let params ={};
                arr.forEach(item=>{
                    params[item] =  _.isArray(paramsObj[item])?paramsObj[item][i]:paramsObj[item];
                });
                value+= fn(params);
            }
        }else {
            value = fn(paramsObj);
        }
        if (typeof value == "number") {
            value = this.convertValue(value);
        }
        return   value;
    }
    //运行时获取值 子类实现
    runtimeGetvalue({type,kind,cloumn},param){
        throw Error("runtimeGetvalue需要子类实现，不可直接调用");
    }
    //规则转换 页面展示规则和 实际计算规则不一样

    //用于基础数据的获取
    getValue({type,kind,cloumn}){
        //获取方式留给子类实现
        throw Error("getValue需要子类实现，不可直接调用");
    }
    //获取实例


}
module.exports =  {
    CupmuteContext
}
