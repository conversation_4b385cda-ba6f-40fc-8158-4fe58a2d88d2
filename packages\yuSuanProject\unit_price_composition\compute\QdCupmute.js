const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const _ = require('lodash');
const {CupmuteContext} = require("./ComputingCore");
const {qdFillBaseRule, qdFillRules, qdUPCBaseRule, qdUPCRules, qdUPCRules2012} = require("./rules/qdRule");
const {qdUPCTemplate2022, qdUPCTemplate2012, otherList} = require("./template/qdUpcTemplate");
const {NumberUtil} = require("../../utils/NumberUtil");
const {LifeFactory, Organ} = require("@valuation/rules-engine");
const {baseFn, ccodes} = require("./rules/chargecode");


class UPCCupmuteQd  extends LifeFactory{
    constructor(qdId, constructId, singleId, unitId, allData) {
        super();
        this.qdId = qdId;
        this.qd = {};
        this.des = [];
        // 安文费费用定额
        this.awfDes = [];
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        this.is2022 = PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);
        this.allData = allData;
        this.upcTemplateList = [];
        this.upcTemplateGroupList = [];
        this.attr = new Map();
    }

    static getInstance({constructId, singleId, unitId, allData}, pointLine) {
        return new UPCCupmuteQd(pointLine.sequenceNbr, constructId, singleId, unitId, allData);
    }

    prepare() {
        this.getBaseData();
        this.getUpcTemplateList();
        // 构建规则
        let rules={};
        //this.analyzeBaseFn(qdFillBaseRule);//清单行基础
        for (let baseIten in qdFillBaseRule){
            let itemFn = qdFillBaseRule[baseIten];
            rules[baseIten]=()=>{
                let result = itemFn();
                let cell =   { name: baseIten, column: result.cloumn, from: result.type=="item"?"runtime":result.type };
                if(result.kind){
                    cell.kind = result.kind;
                }
                return cell;
            };
        }

        //this.analyzeCoreRules(qdFillRules);//清单行规则
        for (let busItenkey in qdFillRules){
            let busItem = qdFillRules[busItenkey];
            let rule = Organ.create({name:busItenkey,description:busItem.name,gene:busItem.mathFormula});
            rules[busItenkey]=rule;
        }

        //this.analyzeBaseFn(qdUPCBaseRule);//单价构成基础
        for (let baseIten in qdUPCBaseRule){
            let itemFn = qdUPCBaseRule[baseIten];
            rules[baseIten]=()=>{
                let result = itemFn();
                let cell =   { name: baseIten, column: result.cloumn, from: result.type=="item"?"runtime":result.type };
                if(result.kind){
                    cell.kind = result.kind;
                }
                return cell;
            };
        }
        if (this.upcTemplateList.length > 0) {
            this.upcTemplateList.forEach(item => {
                //合价计算公式
                let rule = Organ.create({name:item.typeCode + "_allPrice",description:item.typeCode+"合价",gene:this.convertRuleStrHj(item)});
                rules[item.typeCode + "_allPrice"]=rule;
                //单价逻辑
                let rule1 = Organ.create({name:item.typeCode,description:item.typeCode+"单价",gene:this.convertRuleStrDJ(item)});
                rules[item.typeCode]=rule1;
            });
        }
        for (const otherKey in otherList) {
            let item = otherList[otherKey];
            //合价计算公式
            let rule = Organ.create({name:item.typeCode + "_allPrice",description:item.typeCode+"合价",gene:this.convertRuleStrHj(item)});
            rules[item.typeCode + "_allPrice"]=rule;
            //单价逻辑
            let rule1 = Organ.create({name:item.typeCode,description:item.typeCode+"单价",gene:this.convertRuleStrDJ(item)});
            rules[item.typeCode]=rule1;
        }
        this.addRulesAndInitialize(rules);
    }



    //单价计算公式
    convertRuleStrDJ({typeCode}) {
        return "quantity>0?(" + typeCode + "_allPrice/quantity):0";
    }

    //合价计算公式
    convertRuleStrHj(item) {
        return this.is2022 ? qdUPCRules[item.typeCode + "_allPrice"].mathFormula : qdUPCRules2012[item.typeCode + "_allPrice"].mathFormula
    }
    getBaseData() {
        this.qd = this.allData.getNodeById(this.qdId);
        this.qd.children.forEach(item => {
            this.des.push(item);
        });
        this.upcTemplateList = this.is2022 ? _.cloneDeep(qdUPCTemplate2022) : _.cloneDeep(qdUPCTemplate2012);
    }

    getUpcTemplateList() {
        //合并单价构成数据
        let arr = [];
        this.des.forEach(item => {
            let list = this.unit.feeBuild[item.sequenceNbr];
            if (list && list.length > 0) {
                list = list.filter(item => !_.isUndefined(item));
                arr = arr.concat(list)
            }
        });
        //按类型分组
        this.upcTemplateGroupList = _.groupBy(arr, item => item.typeCode);
    }

    cupmute() {
        this.realCupmute();
        this.afterExecute();
        this.clean();
    }

    clean() {
        this.attr.clear();
    }

    getData({kind, cloumn}) {
        let value = [];
        let list = this.upcTemplateGroupList[kind];
        if (list && list.length > 0) {
            list.forEach(item => {
                if (typeof cloumn == "function") {
                    value.push(item);
                } else {
                    value.push(item[cloumn] || 0);
                }
            });
        }
        return value.length > 0 ? value : 0;
    }
    /**
     * 获取定额数据
     * @param cloumn
     * @returns {[]}
     */
    getDeData({cloumn}) {
        let value = [];
        if (this.des.length > 0) {
            this.des.forEach(item => {
                if (typeof cloumn == "function") {
                    value.push(item);
                } else {
                    value.push(item[cloumn] || 0);
                }
            });
        }
        return value.length > 0 ? value : 0;
    }
    transform(field, value) {
        return NumberUtil.numberScale(value, 2);
    }

    getCellValue(cell) {
        let {from:type, kind, column:cloumn}=cell;
        let value = 0;
        switch (type) {
            case "DE": {
                value = this.getDeData({type, kind, cloumn});
                break;
            }
            case "UPC": {
                value = this.getData({type, kind, cloumn});
                break;
            }
            case "qd": {
                if (typeof cloumn == "function") {
                    value = cloumn({qd: this.qd});
                } else {
                    value = this.qd[cloumn] || 0;
                }
                break;
            }
            case "runtime": {
                value = {type, kind, cloumn};
                break;
            }
            default: {
                value = cloumn(this);
            }
        }
        return value;
    }

    realCupmute() {
        //计算合价
        this.upcTemplateList.forEach(item => {
            this.item = item;
            try {
                item.allPrice = this.create(item.typeCode + "_allPrice");
                item.displayAllPrice = item.allPrice;
            } catch (e) {
                console.log(item.typeCode);
            }
        });
        //计算单价
        this.upcTemplateList.forEach(item => {
            this.item = item;
            item.unitPrice = this.create(item.typeCode);
            item.displayUnitPrice = item.unitPrice;

        });
        //默认隐藏的行
        for (const otherKey in otherList) {
            let item = otherList[otherKey];
            if (this.upcTemplateGroupList[otherKey]) {
                item = _.cloneDeep(item);
                item.allPrice = this.create(item.typeCode + "_allPrice");
                item.displayAllPrice = item.allPrice;
                item.unitPrice = this.create(item.typeCode);
                item.displayUnitPrice = item.unitPrice;
                this.upcTemplateList.push(item);
            }
        }

    }


    afterExecute() {
        if (!this.unit.feeBuild) {
            this.unit.feeBuild = {};
        }
        //填充单价构成
        this.unit.feeBuild[this.qd.sequenceNbr] = this.upcTemplateList;
        this.backFillData();
    }

    /**
     * 回填清单数据
     */
    backFillData() {

        for (const key in qdFillRules) {
            //如果计算清单的综合单价
            if(key=="price" && this.qd.lockPriceFlag && !this.qd.tempDeleteFlag){
                this.qd[key] =this.qd.lockPriceBack;
            }else if(key=="total" && this.qd.lockPriceFlag && !this.qd.tempDeleteFlag){
                this.qd[key]=NumberUtil.numberScale(NumberUtil.multiply(this.qd.quantity,this.qd.lockPriceBack), 2)
            } else {
                this.qd[key] = NumberUtil.numberScale(this.create(key), 2);
            }
        }
    }

}

module.exports = UPCCupmuteQd
