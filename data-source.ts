import "reflect-metadata"
import {DataSource} from "typeorm"


export async function getDataSource(path: String) {
	let dataSource = new DataSource({
		type: "better-sqlite3",
		database: "" + path,
		synchronize: false,
		logging: false,
		entities: [__dirname + "/electron/model/*.js",__dirname + "/packages/PreliminaryEstimate/models/*.js",__dirname + "/packages/yuSuanProject/model/*.js"],
		migrations: [],
		subscribers: [],
	})
	await dataSource.initialize()

	return dataSource
}


/**
 * 初始化packages 下项目的默认数据库
 * @param app
 * @param worker
 * @param path
 */
export async function initDataSource(app:any,worker:string[],path:string) {
	app.db={};
	for (let i = 0; i < worker.length; i++) {






		
		const w = worker[i]
		app.db[w]=await getDataSourceByDatabaseAndEntity(path + "/" + w + ".db", "/packages/" + w + "/models/*.js");
	}
}

export async function getDataSourceByDatabaseAndEntity(path: string,entity:string) {
	let dataSource = new DataSource({
		type: "better-sqlite3",
		database: path,
		synchronize: false,
		logging: false,
		entities: [__dirname + entity],
		migrations: [],
		subscribers: [],
	})
	await dataSource.initialize()
	return dataSource
}
