const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const {UPCCupmuteDe, UPCCupmuteQd, UPCCupmuteOther} = require("./UPCCupmute");
const EE = require('../../../../core/ee');
const {ObjectUtils} = require("../../utils/ObjectUtils");
class CalculationTool {
    constructor(ctx) {
        this.ctx = ctx;
        this.allData = ctx.allData;
    }
    /**
     * 获取计算器
     * @param pointLine
     * @returns {UPCCupmuteQd|UPCCupmuteOther|UPCCupmuteDe}
     */
    getCalculator(pointLine) {
        let calculator = null;

        switch (pointLine.kind) {
            case BranchProjectLevelConstant.de: {
                calculator = UPCCupmuteDe.getInstance(this.ctx, pointLine);
                break;
            }
            case BranchProjectLevelConstant.qd: {
                calculator = UPCCupmuteQd.getInstance(this.ctx, pointLine);
                break;
            }

            default: {
                calculator = UPCCupmuteOther.getInstance(this.ctx, pointLine);
            }

        }
        return calculator;
    }

    /**
     * 执行计算单条数据
     * @param pointLine
     */
    calculationChian(pointLine) {
        pointLine = this.allData.getNodeById(pointLine.sequenceNbr);

        while (true) {
            let calculator = this.getCalculator(pointLine);
            if (calculator) {
                calculator.prepare();
                calculator.cupmute();
            }
            //计算
            if (!pointLine.parent) {
                break;
            }
            pointLine = pointLine.parent;
        }
    }

    /**
     * 根据ids批量计算定额
     * @param pointLineIds
     */
    calculationDes(pointLineIds) {
        if (ObjectUtils.isEmpty(pointLineIds)) return;
        let arry = pointLineIds || [];
        while (true) {
            let set = this.calculationBatchForIDs(arry);
            if (set.size == 0) {
                break;
            }
            arry = [...set];
        }
    }

    /**
     * 根据id进行计算
     * @param pointLines
     * @returns {Set<any>}
     */
    calculationBatchForIDs(ids) {
        let set = new Set();
        for (let i = 0; i < ids.length; i++) {
            let id = ids[i];
            let pointLine = this.allData.getNodeById(id);
            if (!pointLine) continue;
            let calculator = this.getCalculator(pointLine);
            if (calculator) {
                calculator.prepare();
                calculator.cupmute();
            }
            if (pointLine.parent) {
                set.add(pointLine.parentId)
            }
        }
        return set;
    }

}
module.exports =CalculationTool;
