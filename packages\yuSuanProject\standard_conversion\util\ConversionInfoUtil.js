const { Snowflake } = require('../../utils/Snowflake');
class ConversionInfoUtil {

    constructor() {
        this.STARDARD_CONVERSION_SOURCE = "标准换算";
        this.DIRECT_INPUT = "直接输入";
        this.UNITE_CONVERSION_SOURCE = "统一换算";
        this.RCJ_DETAIL_SOURCE = "人材机明细";
        this.RCJ_COUNT_SOURCE = "人材机汇总";
        this.TYPE1 = "add";
        this.TYPE2 = "del";
        this.TYPE3 = "update";
        this.TYPE4 = "updateQty";
        this.TYPE5 = "replace";
        this.RCJ_CONVERSION_INFO_ADD_TYPE = this.TYPE1;
        this.RCJ_CONVERSION_INFO_DEL_TYPE = this.TYPE2;
        this.RCJ_CONVERSION_INFO_UPDATE_TYPE = this.TYPE3;
        this.RCJ_CONVERSION_INFO_UPDATEQTY_TYPE = this.TYPE4;
        this.RCJ_CONVERSION_INFO_REPLACE_TYPE = this.TYPE5;

    }

    initConversionInfo(rule,source,conversionString,sortNo = null){
        return {
            sequenceNbr: Snowflake.nextId(),
            ...rule,
            ruleId: rule.sequenceNbr,
            source,
            conversionExplain: null,
            conversionString,
            kind: rule.kind,
            type: rule.type,
            sortNo,
            children: []
        };
    }

}

module.exports = {
    ConversionInfoUtil: new ConversionInfoUtil()
}