const {UPCCupmuteDe} = require("../compute/UPCCupmute");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const {checkAndSaveFeeFile} = require("../../main_editor/rules/updateRules");
const {UPCContext} = require("../../../../electron/unit_price_composition/core/UPCContext");

/*
*  把单价构成模板 应用到 其他定额
* */
class ApplyDependenceDe {
    constructor({constructId, singleId, unitId, source, updateInfo, upcTemplateList}) {
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.unit = PricingFileFindUtils.getUnit(this.constructId, this.singleId, this.unitId);
        this.source = source;
        this.updateInfo = updateInfo;
        this.upcTemplateList = upcTemplateList;
    }

    async update(option) {
        let {costFileCode, costMajorName, qfName, qfCode} = this.updateInfo;
        //更新目标 定额
        let de = this.unit.itemBillProjects.getNodeById(this.source.sequenceNbr)
        if (!de) {
            de = this.unit.measureProjectTables.getNodeById(this.source.sequenceNbr);
        }
        //如果是系统模板 需要更新取费文件
        if (qfCode.indexOf("_") < 0) {
            de.costFileCode = costFileCode;
            de.costMajorName = costMajorName;
            await checkAndSaveFeeFile({
                qfCode: costFileCode,
                constructId: this.constructId,
                singleId: this.singleId,
                unitId: this.unitId,
                isMain: false
            });
        } else {
            //编辑区单价构成下拉选择
            let feeFileMap = UPCContext.getfeeFilebyPath(this.constructId + "," + this.unitId);
            feeFileMap.set(qfCode, option);
            UPCContext.qfCodeMap.set(this.constructId + "," + this.unitId + qfCode, costFileCode);
        }
        de.qfName = qfName;
        de.qfCode = qfCode;
        //单价构成增量模板处理
        let incrementTemplateListMap = UPCContext.getTemplateListbyPath(this.constructId, this.unitId);
        incrementTemplateListMap.set(qfCode, this.upcTemplateList);
        this.source = de;
    }

    //计算单价构成
    async cupmute() {
        let upCCupmuteDe = new UPCCupmuteDe(this.source, this.constructId, this.singleId, this.unitId);
        upCCupmuteDe.prepare();
        upCCupmuteDe.cupmute();
    }
}

module.exports = ApplyDependenceDe
