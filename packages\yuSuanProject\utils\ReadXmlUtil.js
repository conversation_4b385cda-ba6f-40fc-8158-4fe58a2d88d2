const fs = require("fs");
const xml2js = require("xml2js");

class ReadXmlUtil {


    async readXmlFactory(node) {
        if (node.children.length == 0){
            return
        }

        //2024.11.13改动: 把招标通改为河北省公共资源交易平台, 删掉原招标通
        /*if (node.name === '工程量清单数据文件') {
            console.log('招标通');
            return '招标通';
        }*/

        if (node.name === 'JingJiBiao' && (node.attr.Version === '1.3' || node.attr.Version === '1.2')) {
            console.log('E招冀成');
            return 'E招冀成';
        }

        if (node.name === 'GCZJWJ') {
            console.log('云采供');
            return '云采供';
        }

        if (node.name === '工程造价数据文件') {
            if (node.attr['预留数据字段1'] === '优招标') {
                console.log('优招标');
                return '优招标';
            } else {
                console.log('冀招标');
                return '冀招标';
            }
        }

        if (node.name === '工程造价文件') {
            console.log('惠招标');
            return '惠招标';
        }

        if (node.name === '建设项目') {
            console.log('招采进宝');
            return '招采进宝';
        }

        if (node.name === 'JingJiBiao' && node.attr.Version === '3.0') {
            console.log('招标通');
            return '招标通';
        }

        node.eachChild( (child)=> {

            if (child.children.length > 0) {
                this.readXmlFactory(child)
            }

        })
    }

    readXmlFactoryName(importUrl){
        const _this = this;
        return new Promise((resolve,reject)=>{
            fs.readFile(importUrl, async function (err, xmlData) {//读取文件
                if (err) {
                    console.log(err)
                    return null
                }
                let xmldoc = require('xmldoc');//使用xmldoc库
                let document = new xmldoc.XmlDocument(xmlData.toString());
                let name = await _this.readXmlFactory(document);//以递归的方式解析xml文件
                resolve(name);
            })
        })
    }

    async getFile(importUrl) {



        // 读取文件内容
        const xmlString = fs.readFileSync(importUrl);
        return new Promise((resolve, reject) => {
            xml2js.parseString(xmlString, (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    // result 为解析后的 JSON 数据
                    resolve(result);
                }
            });
        });
    }

    async readXmlMainInfo(importUrl){
        let xmlData = await this.getFile(importUrl);
        let factoryName =await this.readXmlFactoryName(importUrl)
        return  await this.readXmlMainInfoDetail(xmlData,factoryName);
    }

    async readXmlMainInfoDetail(document,name){
        let info = {};
        if (name === 'E招冀成') {
            info.projectName = document.JingJiBiao.$.Xmmc;
            info.projectCode = document.JingJiBiao.$.Xmbh;
        }else if(name === '云采供'){
            info.projectName = document.GCZJWJ.GCSJ[0].GCXM[0].$.XMMC
            info.projectCode = document.GCZJWJ.GCSJ[0].GCXM[0].$.XMBM;
            info.projectSt = document.GCZJWJ.GCSJ[0].GCXM[0].$.DQBZ;
        }else if(name === '优招标' || name === '冀招标'){
            info.projectName = document.工程造价数据文件.工程数据[0].招投标信息[0].$.招投标项目名称;
            info.projectCode = document.工程造价数据文件.工程数据[0].招投标信息[0].$.招投标项目编码;
            info.projectSt = document.工程造价数据文件.工程数据[0].总工程[0].$.地区标准;
        }else if(name === '惠招标'){
            info.projectName = document.工程造价文件.工程数据[0].工程项目[0].$.项目名称;
            info.projectCode = document.工程造价文件.工程数据[0].工程项目[0].$.项目编码;
            info.projectSt = document.工程造价文件.工程数据[0].工程项目[0].$.地区标准;
        }else if(name === '招采进宝'){
            info.projectName = document.建设项目.$.项目名称;
            info.projectCode = document.建设项目.$.项目编号;
            info.projectSt = document.建设项目.$.标准名称;
        }else if(name === '招标通'){
            info.projectName = document.JingJiBiao.$.Xmmc;
            info.projectCode = document.JingJiBiao.$.Xmbh;
        }
        return info;
    }

}

module.exports = {
    ReadXmlUtil: new ReadXmlUtil()
};