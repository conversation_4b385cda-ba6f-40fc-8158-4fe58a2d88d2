'use strict';

const {Service, Log} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const QdDeStandardEnum = require("../enum/QdDeStandardEnum");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");

/**
 * 国标定额 process
 */
class BaseDeProcess extends Service {

    constructor(ctx) {
        super(ctx);
        this._baseDeService = this.service.yuSuanProject.baseDeService;   // 国标定额 12 service
        this._baseDe2022Service = this.service.yuSuanProject.baseDe2022Service;   // 国标定额 22 service
        this.baseDeRelation2022Service = this.service.yuSuanProject.BaseDeRelation2022Service;   // 国标定额 22 子父级定额关联表
        this.baseDeRelationVariableCoefficient2022Service = this.service.yuSuanProject.BaseDeRelationVariableCoefficient2022Service;   //子父级定额计算公式
        this.baseDeRelationVariableDescribe2022Service = this.service.yuSuanProject.BaseDeRelationVariableDescribe2022Service;   // 父级定额规则组表
    }

    /**
     * 获取定额分类目录树
     * @param libraryCode 清单册code
     * @returns {tree|Error}
     */
    async listTreeByLibraryCode(args) {
        if (ObjectUtils.isEmpty(args)) {
            throw new Error("定额册code为空");
        }

        let {code: libraryCode, deStandardReleaseYear} = args;

        // 通过libraryCode判断是12定额标准还是22定额标准
        // 是22定额标准执行22定额标准1逻辑
        if (deStandardReleaseYear == QdDeStandardEnum.HB_22_DE_STANDARD.releaseYear) {
            return await this._baseDe2022Service.listTreeByLibraryCode(libraryCode);
        }

        // 原12定额标准逻辑
        return await this._baseDeService.listTreeByLibraryCode(libraryCode);
    }

    /**
     * 模糊查定额
     * @param qdDeParam
     * @see QdDeParam
     * @returns {{"total":length,"data":data}|Error}
     */
    async queryDeByBdCodeAndName(qdDeParam) {
        if (ObjectUtils.isEmpty(qdDeParam) || ObjectUtils.isEmpty(qdDeParam.libraryCode)) {
            throw new Error("定额册code为空");
        }

        // 通过libraryCode判断是12定额标准还是22定额标准
        // 是22定额标准执行22定额标准1逻辑
        let is22de=PricingFileFindUtils.is22UnitById(qdDeParam.constructId,qdDeParam.spId,qdDeParam.upId);
        if (is22de) {
            return await this._baseDe2022Service.queryDeByBdCodeAndName(qdDeParam);
        }

        // 原12定额标准逻辑
        return await this._baseDeService.queryDeByBdCodeAndName(qdDeParam);
    }


}

BaseDeProcess.toString = () => '[class BaseDeProcess]';
module.exports = BaseDeProcess;
