'use strict';

const { Service, Log } = require('../../../core');
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const { BaseList } = require('../model/BaseList');
const ConstructionMeasureTypeConstant = require('../enum/ConstructionMeasureTypeConstant');
const { ObjectUtils } = require('../utils/ObjectUtils');
const BranchProjectLevelConstant = require('../enum/BranchProjectLevelConstant');
const DePropertyTypeConstant = require('../enum/DePropertyTypeConstant');
const { BaseDe, BaseDe2022 } = require('../model/BaseDe');
const StepItemCostLevelConstant = require('../enum/StepItemCostLevelConstant');
const { ConvertUtil } = require('../utils/ConvertUtils');
const { NumberUtil } = require('../utils/NumberUtil');
const { ArrayUtil } = require('../utils/ArrayUtil');
const {
  BaseAnZhuangRate, BaseAnZhuangRate2022
} = require('../model/BaseAnZhuangRate');
const RcjMathEnum = require('../enum/RcjMathEnum');
const ConstantUtil = require('../enum/ConstantUtil');
const QuotaStandard = require('../enum/QuotaStandard');
const InsertStrategy = require('../main_editor/insert/insertStrategy');
const Helper = require('../../../core/utils/helper');

/**
 * 安装费用记取
 * @class
 */
class AzCostMathService extends Service {
  constructor(ctx) {
    super(ctx);
    this._anZjNumMap = new Map();
    this._anZjNumMap.set('1', ['第一册', '第一章', '一、', '1.', '第一节', '(1)', '①', '第一部分']);
    this._anZjNumMap.set('2', ['第二册', '第二章', '二、', '2.', '第二节', '(2)', '②', '第二部分']);
    this._anZjNumMap.set('3', ['第三册', '第三章', '三、', '3.', '第三节', '(3)', '③', '第三部分']);
    this._anZjNumMap.set('4', ['第四册', '第四章', '四、', '4.', '第四节', '(4)', '④', '第四部分']);
    this._anZjNumMap.set('5', ['第五册', '第五章', '五、', '5.', '第五节', '(5)', '⑤', '第五部分']);
    this._anZjNumMap.set('6', ['第六册', '第六章', '六、', '6.', '第六节', '(6)', '⑥', '第六部分']);
    this._anZjNumMap.set('7', ['第七册', '第七章', '七、', '7.', '第七节', '(7)', '⑦', '第七部分']);
    this._anZjNumMap.set('8', ['第八册', '第八章', '八、', '8.', '第八节', '(8)', '⑧', '第八部分']);
    this._anZjNumMap.set('9', ['第九册', '第九章', '九、', '9.', '第九节', '(9)', '⑨', '第九部分']);
    this._anZjNumMap.set('10', ['第十册', '第十章', '十、', '10.', '第十节', '(10)', '⑩', '第十部分']);
    this._anZjNumMap.set('11', ['第十一册', '第十一章', '十一、', '11.', '第十一节', '(11)', '⑪', '第十一部分']);
    this._anZjNumMap.set('12', ['第十二册', '第十二章', '十二、', '12.', '第十二节', '(12)', '⑫', '第十二部分']);
    this._anZjNumMap.set('13', ['第十三册', '第十三章', '十三、', '13.', '第十三节', '(13)', '⑬', '第十三部分']);
    this._anZjNumMap.set('14', ['第十四册', '第十四章', '十四、', '14.', '第十四节', '(14)', '⑭', '第十四部分']);
    this._anZjNumMap.set('15', ['第十五册', '第十五章', '十五、', '15.', '第十五节', '(15)', '⑮', '第十五部分']);
    this._anZjNumMap.set('16', ['第十六册', '第十六章', '十六、', '16.', '第十六节', '(16)', '⑯', '第十六部分']);
    this._anZjNumMap.set('17', ['第十七册', '第十七章', '十七、', '17.', '第十七节', '(17)', '⑰', '第十七部分']);
    this._anZjNumMap.set('18', ['第十八册', '第十八章', '十八、', '18.', '第十八节', '(18)', '⑱', '第十八部分']);
    this._anZjNumMap.set('19', ['第十九册', '第十九章', '十九、', '19.', '第十九节', '(19)', '⑲', '第十九部分']);
    this._anZjNumMap.set('20', ['第二十册', '第二十章', '二十、', '20.', '第二十节', '(20)', '⑳', '第二十部分']);
    this._anZjNumMap.set('21', ['第二十一册', '第二十一章', '二十一、', '21.', '第二十一节', '(21)', '㉑', '第二十一部分']);
    this._anZjNumMap.set('22', ['第二十二册', '第二十二章', '二十二、', '22.', '第二十二节', '(22)', '㉒', '第二十二部分']);
    this._anZjNumMap.set('23', ['第二十三册', '第二十三章', '二十三、', '23.', '第二十三节', '(23)', '㉓', '第二十三部分']);
    this._anZjNumMap.set('24', ['第二十四册', '第二十四章', '二十四、', '24.', '第二十四节', '(24)', '㉔', '第二十四部分']);
    this._anZjNumMap.set('25', ['第二十五册', '第二十五章', '二十五、', '25.', '第二十五节', '(25)', '㉕', '第二十五部分']);
    this._anZjNumMap.set('26', ['第二十六册', '第二十六章', '二十六、', '26.', '第二十六节', '(26)', '㉖', '第二十六部分']);
    this._anZjNumMap.set('27', ['第二十七册', '第二十七章', '二十七、', '27.', '第二十七节', '(27)', '㉗', '第二十七部分']);
    this._anZjNumMap.set('28', ['第二十八册', '第二十八章', '二十八、', '28.', '第二十八节', '(28)', '㉘', '第二十八部分']);
    this._anZjNumMap.set('29', ['第二十九册', '第二十九章', '二十九、', '29.', '第二十九节', '(29)', '㉙', '第二十九部分']);
    this._anZjNumMap.set('30', ['第三十册', '第三十章', '三十、', '30.', '第三十节', '(30)', '㉚', '第三十部分']);
    this._anZjNumMap.set('31', ['第三十一册', '第三十一章', '三十一、', '31.', '第三十一节', '(31)', '㉛', '第三十一部分']);
    this._anZjNumMap.set('32', ['第三十二册', '第三十二章', '三十二、', '32.', '第三十二节', '(32)', '㉜', '第一部分']);
    this._anZjNumMap.set('33', ['第三十三册', '第三十三章', '三十三、', '33.', '第三十三节', '(33)', '㉝', '第三十二部分']);
    this._anZjNumMap.set('34', ['第三十四册', '第三十四章', '三十四、', '34.', '第三十四节', '(34)', '㉞', '第三十四部分']);
    this._anZjNumMap.set('35', ['第三十五册', '第三十五章', '三十五、', '35.', '第三十五节', '(35)', '㉟', '第三十五部分']);
    this._anZjNumMap.set('36', ['第三十六册', '第三十六章', '三十六、', '36.', '第三十六节', '(36)', '㊱', '第三十六部分']);
    this._anZjNumMap.set('37', ['第三十七册', '第三十七章', '三十七、', '37.', '第三十七节', '(37)', '㊲', '第三十七部分']);
    this._anZjNumMap.set('38', ['第三十八册', '第三十八章', '三十八、', '38.', '第三十八节', '(38)', '㊳', '第三十八部分']);
    this._anZjNumMap.set('39', ['第三十九册', '第三十九章', '三十九、', '39.', '第三十九节', '(39)', '㊴', '第三十九部分']);
    this._anZjNumMap.set('40', ['第四十册', '第四十章', '四十、', '40.', '第四十节', '(40)', '㊵', '第四十部分']);
  }

  /**
   * 安装费用----查询清单列表对应的默认清单的值
   */
  async getDefaultQdValue(arg) {
    let { constructId, singleId, unitId, data, type, feeCode } = arg;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let deList = [];
    //对应分部分项
    if (type === ConstructionMeasureTypeConstant.FBFX) {
      deList.push(...PricingFileFindUtils.getDeByfbfx(constructId, singleId, unitId));
    }
    //对应总价措施
    if (type === ConstructionMeasureTypeConstant.ZJCS) {
      deList.push(...PricingFileFindUtils.getDeByZjcs(constructId, singleId, unitId));
    }
    //根据Type获取清单列表数据
    //获取到清单列表
    let qdList = await this.qdList(arg);

    let array = [];
    //如果没有定额数据的话  那么全部就是默认数据
    // if (ObjectUtils.isEmpty(deList)){
    //     await this.defaultValueHandler(array,feeCode,data);
    //     return  array;
    // }

    const is22De = PricingFileFindUtils.is22Unit(unit);
    //循环前端传递的选择的费用定额数据
    for (const costDe of data) {
      let { libraryCode, classLevel1, deCode, relationListId } = costDe;
      //根据安装表定额去基础定额表里面查询数据
      let baseCostDe = await this.app.appDataSource
        .getRepository(is22De ? BaseDe2022 : BaseDe)
        .findOne({
          where: {
            libraryCode: libraryCode, classifyLevel1: classLevel1, deCode: deCode, value: feeCode
          }
        });
      //查询单位下存在的费用定额数据
      let de = deList.find((k) => k.standardId === baseCostDe.sequenceNbr && k.isCostDe === DePropertyTypeConstant.AZ_DE);
      if (!ObjectUtils.isEmpty(de)) {
        let parentId = de.parentId;
        let qd = qdList.find((k) => k.sequenceNbr === parentId);
        if (!ObjectUtils.isEmpty(qd)) {
          array.push({
            sequenceNbr: costDe.sequenceNbr, relationList: ObjectUtils.isEmpty(qd.fxCode) ? qd.bdCode : qd.fxCode
              .concat(' ')
              .concat(ObjectUtils.isEmpty(qd.name) ? qd.bdName : qd.name), relationListId: qd.sequenceNbr
          });
        }
        continue;
      }
      //单位里是否有默认清单的数据
      let qd = qdList.find((k) => k.standardId === relationListId);
      if (!ObjectUtils.isEmpty(qd)) {
        array.push({
          sequenceNbr: costDe.sequenceNbr, relationList: ObjectUtils.isEmpty(qd.fxCode) ? qd.bdCode : qd.fxCode
            .concat(' ')
            .concat(ObjectUtils.isEmpty(qd.name) ? qd.bdName : qd.name), relationListId: qd.sequenceNbr
        });
        continue;
      }
      //默认清单处理
      await this.defaultValueHandler(array, feeCode, [costDe]);
    }
    return array;
  }

  /**
   * 费用定额默认清单ID处理
   */
  async defaultValueHandler(array, feeCode, data) {
    let costDeList = await this.getBaseAnZhuangByFeeCode(feeCode);
    costDeList.forEach((k) => {
      if (!ObjectUtils.isEmpty(k.relationList)) {
        let str = k.relationList.split(' ');
        let strElement = str[0];
        strElement = strElement.slice(0, -3);
        k.relationList = strElement.concat(' ').concat(str[1]);
      }
    });
    let dataDeIdList = data.map((k) => k.sequenceNbr);
    costDeList = costDeList.filter((k) => dataDeIdList.includes(k.sequenceNbr));
    for (const item of costDeList) {
      array.push({
        sequenceNbr: item.sequenceNbr, relationList: item.relationList, relationListId: item.relationListId
      });
    }
  }

  /**
   * 安装记取缓存接口
   */
  azCostMathCache(arg) {
    let { unitId, singleId, constructId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    return unit.azCostMathCache;
  }

  /**
   * 安装费用记取接口
   */
  async azCostMath(arg) {
    // borrowRule： 使用借用的库的安装费用规则  true表示勾选  false表示不勾选
    // outputType： 安装费用输出方式   1：清单费用按每个分部分别计取  2：清单费用按整个工程统一计取
    let { constructId, singleId, unitId, data, borrowRule, outputType, unitIdList } = arg;
    let unitList = PricingFileFindUtils.getUnitList(constructId);
    const targetUnit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    // 把当前单位排到最前面
    const index = unitIdList.indexOf(unitId);
    if (index !== -1) {
      const [removedElement] = unitIdList.splice(index, 1);
      unitIdList.unshift(removedElement);
    }
    for (const currentUnitId of unitIdList) {
      let unit = unitList.find(k => k.sequenceNbr === currentUnitId);
      let is22De = PricingFileFindUtils.is22Unit(unit);
      //删除安装费用定额
      let changeDeIds = new Set();
      await this.service.yuSuanProject.constructCostMathService.clearCostDe(unit, [DePropertyTypeConstant.AZ_DE], changeDeIds);
      if (ObjectUtils.isEmpty(borrowRule)) {
        // 默认是勾选 【使用借用的库的安装费用规则】 的   理论上前端肯定会传这个参数   这里是个兜底
        borrowRule = true;
        arg.borrowRule = true;
      }
      if (ObjectUtils.isEmpty(outputType)) {
        // 默认是清单费用按整个工程统一计取
        outputType = 2;
        arg.outputType = 2;
      }
      //记取安装参数缓存
      if (unitId == unit.sequenceNbr) {
        // 当前单位的正常设置缓存
        unit.azCostMathCache = arg;
      } else {
        // 其他单位的需要去掉基数定额的选中状态
        const cacheObj = ObjectUtils.cloneDeep(arg);
        if (ObjectUtils.isNotEmpty(cacheObj.data)) {
          for (let key of Object.keys(cacheObj.data)) {
            // 区分【安装工程】【房屋修缮】
            for (const cacheData of cacheObj.data[key]) {
              delete cacheData.baseDeList;
              delete cacheData.notSelectDeList;
              // 默认置空，原缓存中有的话就取原缓存中的值
              cacheData.createFeeQdId = null;
              if (ObjectUtils.isNotEmpty(unit.azCostMathCache)) {
                const findObj = unit.azCostMathCache.data[key].find(item => item.feeCode == cacheData.feeCode);
                if (ObjectUtils.isNotEmpty(findObj)) {
                  // 保留原缓存中的费用项已创建的清单id   这个字段仅在房修中有效   目的是防止房修中创建多个相同的费用项清单
                  cacheData.createFeeQdId = findObj.createFeeQdId;
                }
              }
              if (ObjectUtils.isNotEmpty(cacheData.classLevelList)) {
                cacheData.classLevelList.map(item => {
                  // 这里需要保留之前单位中已选择的清单数据
                  // 所以需要查询之前单位中的选中清单数据对缓存进行修正  cacheObj中的选择清单相关数据不能应用于当前单位
                  const cacheSelectQdDefaultRow = this.findUnitAzCacheSelectQdDefaultRow(unit, item.isDefaultRow, key);
                  if (ObjectUtils.isNotEmpty(cacheSelectQdDefaultRow) && ObjectUtils.isNotEmpty(cacheSelectQdDefaultRow.selectQdId)) {
                    item.isDefaultRow.selectQdId = cacheSelectQdDefaultRow.selectQdId;
                    item.isDefaultRow.selectQdName = cacheSelectQdDefaultRow.selectQdName;
                  } else {
                    //  如果之前的没有已使用的清单   那么需要把参数中的非当前单位的清空
                    item.isDefaultRow.selectQdId = null;
                    item.isDefaultRow.selectQdName = null;
                  }
                });
              }
            }
          }

        }
        unit.azCostMathCache = cacheObj;
      }
      if (!borrowRule && unit.constructMajorType != ConstantUtil.STR_ANZHUANG_PROJECT) {
        // 如果没勾选使用借用的库的安装费用规则  就表示非安装工程专业的单位工程  其下的所有定额都不作为基数定额  所以就不计取了
        await this.service.yuSuanProject.autoCostMathService.autoCostMath({
          constructId: constructId,
          singleId: unit.spId,
          unitId: currentUnitId,
          countCostCodeFlag: true,
          changeDeIdArr: changeDeIds
        });
        return;
      }
      for (let key of Object.keys(data)) {
        // data中包含【安装工程】和【房屋修缮】两类数据
        let costData = data[key].filter(k => k.isCheck);
        if (ObjectUtils.isEmpty(costData)) {
          continue;
        }
        for (const feeItem of costData) {
          // type 选择的记取方式
          // baseDeList表示用户打开了基数定额弹窗并点击确认按钮之后，确认选择的基数定额id
          // notSelectDeList表示用户打开了基数定额弹窗并点击确认按钮之后，确认取消勾选的基数定额id
          // createFeeQdId仅在房修安装生效，表示在房修安装中当前这个费用在记取时是否有创建过的【03B的补充费用清单】的id，用于处理重复创建清单的问题
          let { type, classLevelList, feeCode, baseDeList, notSelectDeList, createFeeQdId } = feeItem;
          //处理前端如果没有点开详情按钮的话，则就是默认选择了所有的定额
          if (unitId == unit.sequenceNbr) {
            // 如果是本次打开安装记取弹窗的单位工程  那么没有值的时候就默认选择了所有的基数定额
            if (ObjectUtils.isEmpty(baseDeList) && ObjectUtils.isEmpty(notSelectDeList)) {
              let deList = await this.baseDeList({
                feeCode: feeCode,
                unitId: unitId,
                singleId: singleId,
                constructId: constructId,
                classLevelList: classLevelList,
                azClassLevelType: key
              });
              baseDeList = deList.filter(k => k.kind === BranchProjectLevelConstant.de).map(i => i.sequenceNbr);
            }
          } else {
            // 如果不是本次打开安装记取弹窗的单位   那么默认选择所有的安装基数定额
            let deList = await this.baseDeList({
              feeCode: feeCode,
              unitId: currentUnitId,
              singleId: unit.spId,
              constructId: constructId,
              classLevelList: classLevelList,
              azClassLevelType: key
            });
            baseDeList = deList.filter(k => k.kind === BranchProjectLevelConstant.de).map(i => i.sequenceNbr);
          }

          //根据feeCode获取到所有的基数表中的费用定额
          let costDeList = await this.getCostDeByFeeCode(feeCode, is22De);
          //根据feeCode获取到所有的费用定额
          let anZhuangRateList = await this.getBaseAnZhuangByFeeCode(feeCode, is22De);

          let deDataList = [];
          deDataList = deDataList.concat(PricingFileFindUtils.getDeByfbfx(constructId, unit.spId, currentUnitId));
          deDataList = deDataList.concat(PricingFileFindUtils.getDeByDjcs(constructId, unit.spId, currentUnitId));
          deDataList = deDataList.concat(PricingFileFindUtils.getDeByZjcs(constructId, unit.spId, currentUnitId));

          //获取单位下所有人材机数据
          let rcjList = PricingFileFindUtils.getRcjList(constructId, unit.spId, currentUnitId);

          //循环分册集合
          for (const fcItem of classLevelList) {
            //获取到选择的定额
            let selectDe = fcItem.isDefaultRow;
            // 过滤出用户在页面上选择的基数定额
            let selectBaseDeList = [];
            if (ObjectUtils.isNotEmpty(baseDeList)) {
              selectBaseDeList = unit.itemBillProjects.filter((item) => baseDeList.includes(item.sequenceNbr));
            }
            // 根据前端选择的费用定额，在分部分项里面查询对应的符合条件基数定额有哪些
            // 这一步主要是确定出比如：在【超高费】下的【机械设备安装】、【电气设备超高费】等分册对应的基数定额
            let baseDeArray = this.getCostDeByBaseDe(selectBaseDeList, selectDe, is22De, null);
            //如果所对应的费用定额为空的话，就不行记取定额了
            if (ObjectUtils.isEmpty(baseDeArray)) {
              continue;
            }
            //确定清单，不存在则要新建清单
            let {
              tempType,
              qdArray,
              qdGroup
            } = await this.confirmQd(selectDe, type, unit, baseDeList, anZhuangRateList, baseDeArray, outputType, is22De, targetUnit, key, createFeeQdId);
            //确定费用定额并且封装费用定额数据
            let costDe = this.confirmCostDe(selectDe, costDeList, baseDeList, baseDeArray, tempType, is22De);

            if (!ObjectUtils.isEmpty(qdArray) && !ObjectUtils.isEmpty(costDe)) {
              for (const qd of qdArray) {
                //确定计算费用定额时，所依据的基数定额有哪些
                let deIdList = this.getBaseDeByCostDe(tempType, qd, qdGroup, baseDeArray, outputType);
                //获取到基数定额详情数据集合
                let deList = deDataList.filter((k) => deIdList.includes(k.sequenceNbr));
                //筛选基数定额的人材机数据
                let baseRcjs = rcjList.filter((k) => deIdList.includes(k.deId) && this.calculateBaseHandler(selectDe.calculateBase, selectDe.allocationMethod).includes(k.kind));
                //根据分摊选择算 费率
                let mathRate = this.allocationMethodCostRate(selectDe, this.calculateBaseHandler(selectDe.calculateBase, selectDe.allocationMethod));
                //分别算人材机基数
                let { rBase, cBase, jBase } = this.rcjBaseCost(selectDe, baseRcjs, deList, mathRate, unit, costDe);
                //赋值计算基数
                costDe.caculatePrice = 1;
                costDe.baseNum = {
                  1: rBase, 2: cBase, 3: jBase
                };
                let param = ConvertUtil.deepCopy(costDe);
                //给确定的清单下挂定额以及人材机数据
                let { de, titleData } = await this.confirmQdAddCostDe(unit, qd, param, type);
                // 重新计算人材机数据以及单价构成
                await this.updateTotalNumber(unit, de, titleData, rcjList, mathRate);
              }
            }
          }
        }
      }

      await this.service.yuSuanProject.autoCostMathService.autoCostMath({
        constructId: constructId,
        singleId: unit.spId,
        unitId: currentUnitId,
        countCostCodeFlag: true,
        changeDeIdArr: changeDeIds
      });
    }
  }

  /**
   * 根据费用确定基数定额
   */
  getBaseDeByCostDe(type, qd, qdGroup, baseDeArray, outputType) {
    //指定分部分项 || 指定措施清单
    if (type == ConstructionMeasureTypeConstant.FBFX || type == ConstructionMeasureTypeConstant.ZJCS) {
      if (type == ConstructionMeasureTypeConstant.FBFX && outputType == 1) {
        // 如果是清单费用按每个分部分别计取   那么基数定额就需要在所属的分部下面去查找
        const deListIds = [];
        for (const baseDe of baseDeArray) {
          if (baseDe.parent.parentId == qd.parentId) {
            deListIds.push(baseDe.sequenceNbr);
          }
        }
        return deListIds;
      } else {
        return baseDeArray.map((k) => k.sequenceNbr);
      }
    }
    if (type === ConstructionMeasureTypeConstant.DYFBFX) {
      //根据清单ID获取到基数定额
      let deList = qdGroup.get(qd.sequenceNbr);
      return deList.map((k) => k.sequenceNbr);
    }
  }

  /**
   * 清单下挂定额,下挂人材机数据
   */
  async confirmQdAddCostDe(unit, qd, costDe, type) {
    let { constructId, spId, sequenceNbr } = unit;
    //插入的定额数据
    let de = null;
    //单位标题数据
    let titleData = null;
    //指定分部分项清单 || 对应分部分项
    if (type === ConstructionMeasureTypeConstant.FBFX || type === ConstructionMeasureTypeConstant.DYFBFX) {
      // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
      this.service.yuSuanProject.itemBillProjectOptionService.cleanQdDelTempStatus({
        constructId: constructId,
        singleId: spId,
        unitId: sequenceNbr,
        id: qd.sequenceNbr,
        modelType: 1,
        tempDeleteFlag: false
      });
      //给清单下新增定额数据
      let insertStrategy = new InsertStrategy({
        constructId, singleId: spId, unitId: sequenceNbr, pageType: 'fbfx'
      });
      de = await insertStrategy.execute({
        pointLine: qd,
        newLine: costDe,
        indexId: costDe.standardId,
        libraryCode: costDe.libraryCode,
        option: 'insert',
        skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
        overwriteColumn: false
      });
      titleData = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).itemBillProjects;
    }
    //指定措施清单
    if (type === ConstructionMeasureTypeConstant.ZJCS) {
      // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
      this.service.yuSuanProject.itemBillProjectOptionService.cleanQdDelTempStatus({
        constructId: constructId,
        singleId: spId,
        unitId: sequenceNbr,
        id: qd.sequenceNbr,
        modelType: 2,
        tempDeleteFlag: false
      });
      let insertStrategy = new InsertStrategy({
        constructId, singleId: spId, unitId: sequenceNbr, pageType: 'csxm'
      });
      de = await insertStrategy.execute({
        pointLine: qd,
        newLine: costDe,
        indexId: costDe.standardId,
        libraryCode: costDe.libraryCode,
        option: 'insert',
        skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
        overwriteColumn: false
      });
      titleData = PricingFileFindUtils.getUnit(constructId, spId, sequenceNbr).measureProjectTables;
    }
    return {
      de: de, titleData: titleData
    };
  }

  /**
   * 根据feeCode获取到所有安装表中的的费用定额
   * @return {Promise<void>}
   */
  async getBaseAnZhuangByFeeCode(feeCode, is22De) {
    let anZhuangRateList = await this.app.appDataSource
      .getRepository(is22De ? BaseAnZhuangRate2022 : BaseAnZhuangRate)
      .find({
        where: { feeCode: feeCode }
      });
    return anZhuangRateList;
  }

  /**
   * 根据feeCode获取到所有的费用定额
   * @return {Promise<void>}
   */
  async getCostDeByFeeCode(feeCode, is22De) {
    let costDeList = await this.app.appDataSource
      .getRepository(is22De ? BaseDe2022 : BaseDe)
      .find({
        where: {
          value: feeCode
        }
      });
    return costDeList;
  }

  /**
   * 确定费用定额并且封装费用定额数据
   */
  confirmCostDe(selectDe, costDeList, baseDeIdList, baseDeArray, tempType, is22De) {
    let { libraryCode, classLevel1, deCode, deName } = selectDe;

    //根据前端选择的费用定额，在分部分项里面查询对应的基数定额
    baseDeArray = baseDeArray.filter((k) => baseDeIdList.includes(k.sequenceNbr));
    if (ObjectUtils.isEmpty(baseDeArray)) {
      return null;
    }
    // 获取到费用定额
    // selectDe数据来源是base_anzhuang_rate   costDeList是base_de的数据
    // 这两个数据确认唯一一条，需要使用libraryCode、deCode、deName三个字段进行匹配
    let costDe = costDeList.find((k) => k.libraryCode === libraryCode && k.deCode === deCode && k.deName == deName);
    if (libraryCode == QuotaStandard.DEK_ANZHUANG_2022_FWXS_TJ) {
      // 【房屋修缮建筑工程】需要特殊处理，使用【房屋修缮安装工程】的费用定额数据
      // 原因是【房屋修缮建筑工程】在base_anzhuang_rate_2022中有【G009】【G010】这两条定额   但是在base_de_2022中没有【G009】【G010】这两条定额
      // 所以这里特殊处理为使用base_de_2022中【房屋修缮安装工程】的【G009】【G010】数据作为费用定额基础数据
      costDe = costDeList.find((k) => k.libraryCode === QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ && k.deCode === deCode && k.deName == deName);
    }
    //封装费用定额其他数据
    let deData = {};
    deData.kind = StepItemCostLevelConstant.de;
    deData.fxCode = costDe.deCode;
    deData.name = costDe.deName;
    deData.bdCode = costDe.deCode;
    deData.bdName = costDe.deName;
    deData.quantity = 1;
    deData.unit = costDe.unit;
    if (is22De && deData.unit == '%') {
      deData.unit = '元';
    }
    deData.standardId = costDe.sequenceNbr;
    deData.libraryCode = costDe.libraryCode;
    deData.isAutoCost = true; //手动记取
    deData.isCostDe = DePropertyTypeConstant.AZ_DE; //添加定额标识 安装费用定额
    deData.isStandard = DePropertyTypeConstant.STANDARD; //定额数据位标准定额数据
    deData.quantityExpression = '1'; //工程量表达式展示用
    deData.quantityExpressionNbr = 1; //工程量表达式计算用
    deData.costDeMatchType = tempType; // 记取方式
    return deData;
  }

  /**
   * 确定项目里面的清单
   */
  async confirmQd(selectDe, type, unit, baseDeList, anZhuangRateList, array, outputType, is22De, targetUnit, key) {
    //根据type获取到对应的清单  循环这里是为了第一次不存在的清单，插进去后，第二次就需要能查询出来了
    let qdList = this.getQdByType(unit.constructId, unit.spId, unit.sequenceNbr, type);
    // relationListId表示：表示默认的【base_anzhuang_rate】中的默认数据 如果为空则表示没有选择的清单，没有选择的清单就需要新建清单
    // relationList 表示：【base_anzhuang_rate】中默认的清单编码名称组合
    // selectQdId：用户在页面选择的具体清单的id，这个肯定是一个项目中已存在的清单
    // selectQdName：用户在页面选择的具体清单的编码名称组合，这个肯定是一个项目中已存在的清单
    let { feeName, relationListId, relationList, selectQdId, sequenceNbr } = selectDe;
    // 这个defaultQdData是定额分册对应下拉列表选择的BaseAnZhuangRate
    let defaultQdData = anZhuangRateList.find((k) => k.sequenceNbr === sequenceNbr);
    let baseQd;
    if (ObjectUtils.isNotEmpty(defaultQdData.relationListId)) {
      baseQd = await this.app.appDataSource
        .getRepository(BaseList)
        .findOne({
          where: { sequenceNbr: defaultQdData.relationListId }
        });
    }
    let result = {};
    result.tempType = type;
    // 指定措施项目  或者  指定分部分项
    if (type === ConstructionMeasureTypeConstant.FBFX || type === ConstructionMeasureTypeConstant.ZJCS) {
      // outputType： 安装费用输出方式   1：清单费用按每个分部分别计取  2：清单费用按整个工程统一计取
      if (type === ConstructionMeasureTypeConstant.FBFX && outputType == 1) {
        const fbFx = PricingFileFindUtils.getFbFx(unit.constructId, unit.spId, unit.sequenceNbr);
        // 指定分部分项时  需要判断安装费用输出方式
        // 如果是:清单费用按每个分部分别计取  需要根据分部下是否有基数定额来确定
        // array里面是所有的基数定额  根据基数定额筛选出哪些清单下有基数定额
        const baseDeParentIds = array.map((k) => k.parentId);
        const baseQdList = qdList.filter((k) => baseDeParentIds.includes(k.sequenceNbr));
        // 得到所有有基数定额的清单后，就可以获取到他们的父级，也就是分部   这里是去重之后的分部ids
        const fbIds = new Set(baseQdList.map((item) => item.parentId));
        for (const fbId of fbIds) {
          // 根据id获取到分部
          const fbNode = fbFx.getNodeById(fbId);
          if (ObjectUtils.isEmpty(fbNode) || ObjectUtils.isEmpty(fbNode.children)) {
            // 如果没有这个分部  或者分部的子级为空  就不处理了
            continue;
          }
          // 查看是不是有已存在的可用清单
          let findExistingQd;
          if (ObjectUtils.isNotEmpty(defaultQdData.relationListId)) {
            findExistingQd = fbNode.children.find((qd) => qd.standardId == defaultQdData.relationListId);
          }
          if (ObjectUtils.isEmpty(findExistingQd)) {
            // 如果没有可用的清单  需要新建一个清单   如果defaultQdData.relationListId是空  说明是特殊的【03B】
            if (ObjectUtils.isEmpty(defaultQdData.relationListId)) {
              let qd03B = await this.createAzQd(unit, relationListId, relationList, null, qdList, defaultQdData, null, type, key, feeName);
              this.resetUnitCacheSelectQd(unit, selectDe, qd03B, key);
              result.qdArray = [qd03B];
            } else {
              let newQdData = await this.createAzQd(unit, relationListId, relationList, baseQd, qdList, defaultQdData, fbNode, type, key, feeName);
              if (ObjectUtils.isEmpty(result.qdArray)) {
                result.qdArray = [newQdData];
              } else {
                result.qdArray.push(newQdData);
              }
            }
          } else {
            // 如果有默认清单  直接用这个
            if (ObjectUtils.isEmpty(result.qdArray)) {
              result.qdArray = [findExistingQd];
            } else {
              result.qdArray.push(findExistingQd);
            }
          }
        }
        return result;
      } else {
        if (unit.sequenceNbr == targetUnit.sequenceNbr) {
          // 目标单位就是当前单位
          if (ObjectUtils.isNotEmpty(selectQdId)) {
            // 用户选择了项目中存在的清单
            let qd = qdList.find((k) => k.sequenceNbr === selectQdId);
            if (!ObjectUtils.isEmpty(qd)) {
              result.qdArray = [qd];
              return result;
            }
          } else {
            // 用户没有选择清单  查询是否有匹配的标准清单
            let qd = qdList.find((k) => k.standardId === relationListId);
            if (!ObjectUtils.isEmpty(qd)) {
              result.qdArray = [qd];
              this.resetUnitCacheSelectQd(unit, selectDe, qd, key);
              return result;
            }
          }
          // 如果用户既没有选择项目中已有的清单，并且项目中也没有匹配的标准清单  那么就需要根据defaultQdData创建对应清单
          if (ObjectUtils.isNotEmpty(defaultQdData) && ObjectUtils.isNotEmpty(defaultQdData.relationListId)) {
            // 根据defaultQdData.relationListId创建对应的标准清单
            let qd3 = await this.createAzQd(unit, relationListId, relationList, baseQd, qdList, defaultQdData, null, type, key, feeName);
            this.resetUnitCacheSelectQd(unit, selectDe, qd3, key);
            result.qdArray = [qd3];
            return result;
          } else {
            // 【base_anzhuang_rate】默认清单为空  用户也没有选择清单，那么就需要创建【03B】清单
            if (key == 'fwxs') {
              // 查询房屋修缮的当前费用项是否已经创建过清单  如果创建过这个费用清单  那么直接使用这个
              const createdQd = this.findFwxsCreatedQd(unit, selectDe, qdList, key);
              if (ObjectUtils.isNotEmpty(createdQd)) {
                this.resetUnitCacheSelectQd(unit, selectDe, createdQd, key);
                result.qdArray = [createdQd];
                return result;
              }
            }
            let qd03B = await this.createAzQd(unit, relationListId, relationList, null, qdList, defaultQdData, null, type, key, feeName);
            this.resetUnitCacheSelectQd(unit, selectDe, qd03B, key);
            // 如果创建了清单  需要在创建之后记录下当前费用已创建了清单，后续就不需要创建了
            if (key == 'fwxs') {
              const findObj = unit.azCostMathCache.data[key].find((item) => item.feeCode == selectDe.feeCode);
              if (ObjectUtils.isNotEmpty(findObj)) {
                findObj.createFeeQdId = qd03B.sequenceNbr;
              }
            }
            result.qdArray = [qd03B];
            return result;
          }
        } else {
          // 目标单位不是当前单位
          if (ObjectUtils.isNotEmpty(selectQdId)) {
            // 如果在目标单位中选择了项目中存在的清单
            let targetQdNode;
            if (type == ConstructionMeasureTypeConstant.FBFX) {
              targetQdNode = targetUnit.itemBillProjects.getNodeById(selectQdId);
            } else {
              targetQdNode = targetUnit.measureProjectTables.getNodeById(selectQdId);
            }
            if (targetQdNode.isSupplement == 0) {
              // 用户在目标单位选择的是标准清单  那么就找当前单位有没有匹配的清单  有就直接使用  没有就创建一条标准清单
              const standardQd = qdList.find((k) => k.standardId === targetQdNode.standardId);
              if (ObjectUtils.isNotEmpty(standardQd)) {
                result.qdArray = [standardQd];
                this.resetUnitCacheSelectQd(unit, selectDe, standardQd, key);
                return result;
              } else {
                baseQd = await this.app.appDataSource
                  .getRepository(BaseList)
                  .findOne({
                    where: {
                      sequenceNbr: targetQdNode.standardId
                    }
                  });
                let qd3 = await this.createAzQd(unit, targetQdNode.standardId, relationList, baseQd, qdList, defaultQdData, null, type, key, feeName);
                this.resetUnitCacheSelectQd(unit, selectDe, qd3, key);
                result.qdArray = [qd3];
                return result;
              }
            } else {
              // 补充清单
              if (ObjectUtils.isNotEmpty(defaultQdData.relationListId)) {
                // 用户选择的补充清单(非停车场那一条，也就是选择了一条非【03B】的补充清单)
                // defaultQdData.relationListId有值，那么先查询当前有没有匹配的标准清单，有就直接使用，没有就根据defaultQdData.relationListId创建一条标准清单
                const standardQd = qdList.find((k) => k.standardId === defaultQdData.relationListId);
                if (ObjectUtils.isNotEmpty(standardQd)) {
                  result.qdArray = [standardQd];
                  this.resetUnitCacheSelectQd(unit, selectDe, standardQd, key);
                  return result;
                } else {
                  baseQd = await this.app.appDataSource
                    .getRepository(BaseList)
                    .findOne({
                      where: {
                        sequenceNbr: defaultQdData.relationListId
                      }
                    });
                  // 直接根据base_anzhuang_rate的默认数据创建一条标准清单
                  let qd3 = await this.createAzQd(unit, relationListId, relationList, baseQd, qdList, defaultQdData, null, type, key, feeName);
                  this.resetUnitCacheSelectQd(unit, selectDe, qd3, key);
                  result.qdArray = [qd3];
                  return result;
                }
              } else {
                // 先判断缓存有没有已使用的清单，有已经使用过的清单就直接使用，没有才创建【03B】
                const cacheSelectQdDefaultRow = this.findUnitAzCacheSelectQdDefaultRow(unit, selectDe, key);
                if (ObjectUtils.isNotEmpty(cacheSelectQdDefaultRow) && ObjectUtils.isNotEmpty(cacheSelectQdDefaultRow.selectQdId)) {
                  // 从当前单位的清单中查找
                  const unitCacheQdNode = qdList.find((k) => k.sequenceNbr === cacheSelectQdDefaultRow.selectQdId);
                  if (ObjectUtils.isNotEmpty(unitCacheQdNode)) {
                    result.qdArray = [unitCacheQdNode];
                    return result;
                  }
                }
                if (key == 'fwxs') {
                  // 查询房屋修缮的当前费用项是否已经创建过清单  如果创建过这个费用清单  那么直接使用这个
                  const createdQd = this.findFwxsCreatedQd(unit, selectDe, qdList, key);
                  if (ObjectUtils.isNotEmpty(createdQd)) {
                    this.resetUnitCacheSelectQd(unit, selectDe, createdQd, key);
                    result.qdArray = [createdQd];
                    return result;
                  }
                }
                let qd03B = await this.createAzQd(unit, null, null, null, qdList, defaultQdData, null, type, key, feeName);
                this.resetUnitCacheSelectQd(unit, selectDe, qd03B, key);
                // 如果创建了清单  需要在创建之后记录下当前费用已创建了清单，后续就不需要创建了
                if (key == 'fwxs') {
                  const findObj = unit.azCostMathCache.data[key].find((item) => item.feeCode == selectDe.feeCode);
                  if (ObjectUtils.isNotEmpty(findObj)) {
                    findObj.createFeeQdId = qd03B.sequenceNbr;
                  }
                }
                result.qdArray = [qd03B];
                return result;
              }
            }
          } else {
            // 说明用户没有手动选择目标单位的已存在清单，使用的是默认的base_anzhuang_rate的清单relationListId
            // 那么找单位中有没有匹配的标准清单，如果有标准清单，那么直接使用匹配到的第一条  如果没有标准清单就根据这个baseQd创建一条标准清单
            if (ObjectUtils.isNotEmpty(defaultQdData.relationListId)) {
              // 从当前单位的清单中查找匹配的标准清单
              const standardQd = qdList.find((k) => k.standardId === defaultQdData.relationListId);
              if (ObjectUtils.isNotEmpty(standardQd)) {
                result.qdArray = [standardQd];
                this.resetUnitCacheSelectQd(unit, selectDe, standardQd, key);
                return result;
              } else {
                let qd3 = await this.createAzQd(unit, relationListId, relationList, baseQd, qdList, defaultQdData, null, type, key, feeName);
                this.resetUnitCacheSelectQd(unit, selectDe, qd3, key);
                result.qdArray = [qd3];
                return result;
              }
            } else {
              // 说明用户在目标单位中对没有默认清单的这条数据也没有选择已存在的清单，在目标单位中走的逻辑是创建【03B】
              // 先判断缓存有没有已使用的清单，有已经使用过的清单就直接使用，没有才创建【03B】
              const cacheSelectQdDefaultRow = this.findUnitAzCacheSelectQdDefaultRow(unit, selectDe, key);
              if (ObjectUtils.isNotEmpty(cacheSelectQdDefaultRow) && ObjectUtils.isNotEmpty(cacheSelectQdDefaultRow.selectQdId)) {
                // 从当前单位的清单中查找
                const unitCacheQdNode = qdList.find((k) => k.sequenceNbr === cacheSelectQdDefaultRow.selectQdId);
                if (ObjectUtils.isNotEmpty(unitCacheQdNode)) {
                  // 有就直接继续使用
                  result.qdArray = [unitCacheQdNode];
                  return result;
                }
              }
              if (key == 'fwxs') {
                // 查询房屋修缮的当前费用项是否已经创建过清单  如果创建过这个费用清单  那么直接使用这个
                const createdQd = this.findFwxsCreatedQd(unit, selectDe, qdList, key);
                if (ObjectUtils.isNotEmpty(createdQd)) {
                  this.resetUnitCacheSelectQd(unit, selectDe, createdQd, key);
                  result.qdArray = [createdQd];
                  return result;
                }
              }
              let qd03B = await this.createAzQd(unit, null, null, null, qdList, defaultQdData, null, type, key, feeName);
              this.resetUnitCacheSelectQd(unit, selectDe, qd03B, key);
              // 如果创建了清单  需要在创建之后记录下当前费用已创建了清单，后续就不需要创建了
              if (key == 'fwxs') {
                const findObj = unit.azCostMathCache.data[key].find((item) => item.feeCode == selectDe.feeCode);
                if (ObjectUtils.isNotEmpty(findObj)) {
                  findObj.createFeeQdId = qd03B.sequenceNbr;
                }
              }
              result.qdArray = [qd03B];
              return result;
            }
          }
        }
      }
    } else if (type === ConstructionMeasureTypeConstant.DYFBFX) {
      //这里是 对应分部分项 了
      if (ObjectUtils.isEmpty(baseDeList)) {
        return [];
      }
      //分部分项下所有的数据
      let { itemBillProjects } = unit;
      //根据前端选择的费用定额，在分部分项里面查询对应的基数定额
      if (ObjectUtils.isEmpty(array)) {
        return (result.qdArray = []);
      }
      //将选中的定额 根据清单ID分组
      let qdGroup = ArrayUtil.group(array, 'parentId');
      //获取到符合条件定额的所有的父级ID
      let deParentId = array.map((k) => k.parentId);
      const filter = itemBillProjects.filter((k) => deParentId.includes(k.sequenceNbr) && k.kind === StepItemCostLevelConstant.qd);
      if (Array.isArray(filter)) {
        result.qdArray = filter;
      } else {
        result.qdArray = [filter];
      }
      result.qdGroup = qdGroup;
      return result;
    }
  }

  /**
   * 查询房屋修缮的当前费用项是否已经创建过清单
   */
  findFwxsCreatedQd(unit, selectDe, qdList, key) {
    // 如果是房屋修缮，并且当前费用下已经有创建过【03B的费用清单】，那么就直接使用这个
    const findObj = unit.azCostMathCache.data[key].find((item) => item.feeCode == selectDe.feeCode);
    if (ObjectUtils.isNotEmpty(findObj) && ObjectUtils.isNotEmpty(findObj.createFeeQdId)) {
      return qdList.find((item) => item.sequenceNbr == findObj.createFeeQdId);
    }
    return null;
  }

  /**
   * 这个方法仅用于多单位记取时，页面选择的具体清单sequenceNbr在当前单位中正确，但是其他单位中清单的sequenceNbr不存在的问题
   * 处理方式是在新建清单之后，把新清单sequenceNbr重新给当前单位的缓存数据中的selectQdId和selectQdName
   */
  resetUnitCacheSelectQd(unit, selectDe, qd, key) {
    const findObj = unit.azCostMathCache.data[key].find((item) => item.feeCode == selectDe.feeCode);
    if (ObjectUtils.isNotEmpty(findObj)) {
      const cacheCostDe = findObj.classLevelList.find((item) => item.isDefault == selectDe.sequenceNbr);
      if (ObjectUtils.isNotEmpty(cacheCostDe)) {
        cacheCostDe.isDefaultRow.selectQdId = qd.sequenceNbr;
        cacheCostDe.isDefaultRow.selectQdName = qd.bdCode + ' ' + qd.name;
      }
    }
  }

  /**
   * 查找对应单位安装费用项选择的清单
   */
  findUnitAzCacheSelectQdDefaultRow(unit, selectDe, key) {
    if (ObjectUtils.isEmpty(unit.azCostMathCache)) {
      return null;
    }
    const findObj = unit.azCostMathCache.data[key].find((item) => item.feeCode == selectDe.feeCode);
    if (ObjectUtils.isNotEmpty(findObj)) {
      const cacheCostDe = findObj.classLevelList.find((item) => item.isDefault == selectDe.sequenceNbr);
      if (ObjectUtils.isNotEmpty(cacheCostDe)) {
        return cacheCostDe.isDefaultRow;
      }
    }
    return null;
  }

  /**
   * 创建安装费用定额所属的清单
   * @param unit
   * @param relationListId      用户选择的清单id
   * @param relationList        需要使用的清单的编码和名称组合
   * @param baseQd              创建普通清单的清单册清单
   * @param qdList              当前单位的所有清单
   * @param defaultQdData       页面选择的定额分册对应下拉列表选择的BaseAnZhuangRate
   * @param parentNode          创建的清单的父级
   * @param type                表示要创建的清单是分部分项还是措施项目
   * @param key                 安装工程 或者 房屋修缮
   */
  async createAzQd(unit, relationListId, relationList, baseQd, qdList, defaultQdData, parentNode, type, key, feeName) {
    if (ObjectUtils.isEmpty(parentNode)) {
      // 如果没有父级清单  那么就要选择默认的
      if (type === ConstructionMeasureTypeConstant.DJCS) {
        // 单价措施
        let measure = unit.measureProjectTables.find((k) => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === type);
        if (ObjectUtils.isEmpty(measure)) {
          let defaultLine = this.service.yuSuanProject.stepItemCostService.Default_Frist_Line();
          const titleData = await this.service.yuSuanProject.stepItemCostService.save(unit.constructId, unit.spId, unit.sequenceNbr, unit.measureProjectTables[0], defaultLine);
          parentNode = titleData.data;
        } else {
          parentNode = measure;
        }
      } else if (type === ConstructionMeasureTypeConstant.ZJCS) {
        // 总价措施
        let measure = unit.measureProjectTables.find((k) => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === type);
        if (ObjectUtils.isEmpty(measure)) {
          let defaultLine = this.service.yuSuanProject.stepItemCostService.Default_THR_Line();
          const titleData = await this.service.yuSuanProject.stepItemCostService.save(unit.constructId, unit.spId, unit.sequenceNbr, unit.measureProjectTables[0], defaultLine);
          parentNode = titleData.data;
        } else {
          parentNode = measure;
        }
      } else {
        // 分部分项
        let allData = unit.itemBillProjects.getAllNodes();
        parentNode = allData[0];
      }
    }
    // 查看前端传来的relationListId是不是项目中已有的清单 如果不是就说明需要创建补充清单
    let standardQd = qdList.find((k) => k.standardId === relationListId);
    // 同时如果relationList是空的   也说明是需要创建补充清单的
    // 如果relationList是03B开头的   也要创建补充清单
    if (ObjectUtils.isEmpty(standardQd) && ((ObjectUtils.isNotEmpty(relationList) && relationList.startsWith('03B')) || (ObjectUtils.isEmpty(relationListId) && ObjectUtils.isEmpty(relationList)))) {
      // 需要创建补充清单
      // 补充清单的清单编码以【03B】开头后续递增【03B001】【03B002】【03B003】
      // 清单名称使用defaultQdData的deName
      return await this.qdDataAssembleAndCreateFor03B(unit, type, parentNode, defaultQdData, qdList, key, feeName);
    } else {
      // 根据清单册中的清单创建普通清单
      // 普通的清单册清单使用baseQd作为基础创建
      //封装清单数据
      let newQd = {
        name: baseQd.bdNameLevel04,
        bdName: baseQd.bdNameLevel04,
        kind: StepItemCostLevelConstant.qd,
        standardId: baseQd.sequenceNbr,
        unit: ObjectUtils.isEmpty(baseQd.unit) ? '项' : baseQd.unit,
        isSupplement: baseQd.isSupplement
      };
      return await this.createQd(unit, type, newQd, parentNode, qdList);
    }
  }

  /**
   * 判断是不是房屋修缮
   */
  isFwxs(libraryCode) {
    return [QuotaStandard.DEK_ANZHUANG_2012_FWXS, QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ, QuotaStandard.DEK_ANZHUANG_2022_FWXS_TJ].includes(libraryCode);
  }

  /**
   * 根据费用定额查询当前项目下符合条件的基数定额有哪些
   * @param itemBillProjects
   * @param selectDe
   * @param is22De
   * @param costDe  自动计取时的费用定额  主要是看费用定额是不是对应分部分项方式记取的
   * @return {*[]}
   */
  getCostDeByBaseDe(itemBillProjects, selectDe, is22De, costDe) {
    let { classLevel1, classLevel2, libraryCode } = selectDe;
    //分部分项定额,只需要安装工程下的基数定额
    let array = itemBillProjects.filter((k) => k.kind === StepItemCostLevelConstant.de && k.libraryCode == libraryCode && k.classifyLevel1 === classLevel1);
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }

    if (ObjectUtils.isNotEmpty(costDe) && costDe.costDeMatchType === ConstructionMeasureTypeConstant.DYFBFX) {
      // 如果是对应分部分项  那么基数定额只能从对应清单下获取   也就是同一个清单下的
      array = array.filter((item) => item.parentId === costDe.parentId);
    }
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }

    if (is22De) {
      // 先根据 , 分割
      let zjList = [];
      const split = classLevel2.split(',');
      for (const item of split) {
        if (item.includes('~')) {
          zjList = zjList.concat(this.zjUtils(item));
        } else {
          zjList.push(item);
        }
      }
      array = this.zjSelect(zjList, array, this.isFwxs(libraryCode), [libraryCode]);
    } else {
      //判断有哪些基数定额符合当前费用定额锁规定的基数定额章节内数据
      //章节集合
      let zjList = NumberUtil.numToChByBusiness([classLevel2]);
      //章节名称集合
      const zjKeys = zjList.map((obj) => Object.keys(obj)[0]);

      //循环定额，筛选章节
      for (let i = array.length - 1; i >= 0; i--) {
        //获取到定额
        let item = array[i];
        let { classifyLevel2, classifyLevel3 } = item;
        //切割基数定额classifyLevel2字符串
        let tempClassifyLevel2 = this.substringByClassifyLevel2(classifyLevel2, '章');
        let key = zjKeys.find((k) => k === tempClassifyLevel2);
        if (ObjectUtils.isEmpty(key)) {
          array.splice(i, 1);
          continue;
        }
        //查找是否有第三节
        const valueArray = zjList.find((obj) => obj.hasOwnProperty(key))[key];
        if (ObjectUtils.isEmpty(valueArray)) {
          continue;
        }
        //存在第三节
        let tempClassifyLevel3 = this.substringByClassifyLevel2(classifyLevel3, '、');
        if (!valueArray.includes(tempClassifyLevel3)) {
          array.splice(i, 1);
        }
      }
    }

    return array;
  }

  zjSelect(zjList, array, isFwxsFlag = false, libraryCodeArr) {
    array = array.filter((item) => libraryCodeArr.includes(item.libraryCode));
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }
    let array1 = [];
    for (const zj of zjList) {
      // 把2.11.11.6 这种数据拆分  split1有几个 就说么有几层  split1有一个就说明只筛选classify_level2 split1有两个就说明筛选classify_level2和classify_level3 以此类推到classify_level7
      const split1 = zj.split('.');
      if (split1.length === 1) {
        // 筛选classify_level2
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classifyLevel2) && item.classifyLevel2.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 2) {
        // 筛选classify_level2 classify_level3
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classifyLevel2) && item.classifyLevel2.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel3) && item.classifyLevel3.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 3) {
        // 筛选classify_level2 classify_level3 classify_level4
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classifyLevel2) && item.classifyLevel2.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel3) && item.classifyLevel3.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel4) && item.classifyLevel4.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 4) {
        // 筛选classify_level2 classify_level3 classify_level4 classify_level5
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classifyLevel2) && item.classifyLevel2.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel3) && item.classifyLevel3.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel4) && item.classifyLevel4.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel5) && item.classifyLevel5.startsWith(this.getClassify_level5Str(split1[3], isFwxsFlag, item.libraryCode))));
      } else if (split1.length === 5) {
        // 筛选classify_level2 classify_level3 classify_level4 classify_level5 classify_level6
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classifyLevel2) && item.classifyLevel2.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel3) && item.classifyLevel3.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel4) && item.classifyLevel4.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel5) && item.classifyLevel5.startsWith(this.getClassify_level5Str(split1[3], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel6) && item.classifyLevel6.startsWith(this.getClassify_level6Str(split1[4]))));
      } else if (split1.length === 6) {
        // 筛选classify_level2 classify_level3 classify_level4 classify_level5 classify_level6 classify_level7
        array1 = array1.concat(array.filter((item) => ObjectUtils.isNotEmpty(item.classifyLevel2) && item.classifyLevel2.startsWith(this.getClassify_level2Str(split1[0], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel3) && item.classifyLevel3.startsWith(this.getClassify_level3Str(split1[1], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel4) && item.classifyLevel4.startsWith(this.getClassify_level4Str(split1[2], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel5) && item.classifyLevel5.startsWith(this.getClassify_level5Str(split1[3], isFwxsFlag, item.libraryCode)) && ObjectUtils.isNotEmpty(item.classifyLevel6) && item.classifyLevel6.startsWith(this.getClassify_level6Str(split1[4])) && ObjectUtils.isNotEmpty(item.classifyLevel7) && item.classifyLevel7.startsWith(this.getClassify_level7Str(split1[5]))));
      }
    }
    return array1;
  }

  getClassify_level2Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return '第' + this.numberToChinese(str) + '部分';
      } else {
        // 房屋修缮建筑工程
        return '第' + this.numberToChinese(str) + '章';
      }
    } else {
      return '第' + this.numberToChinese(str) + '章';
    }
  }

  getClassify_level3Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return '第' + this.numberToChinese(str) + '章';
      } else {
        // 房屋修缮建筑工程
        return this.numberToChinese(str) + '、';
      }
    } else {
      return '第' + this.numberToChinese(str) + '节';
    }
  }

  getClassify_level4Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return this.numberToChinese(str) + '、';
      } else {
        // 房屋修缮建筑工程
        return str + '.';
      }
    } else {
      return this.numberToChinese(str) + '、';
    }
  }

  getClassify_level5Str(str, isFwxsFlag = false, libraryCode) {
    if (isFwxsFlag) {
      if (libraryCode == QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ) {
        // 房屋修缮安装工程
        return str + '.';
      } else {
        // 房屋修缮建筑工程
        return '(' + str + ')';
      }
    } else {
      return str + '.';
    }
  }

  getClassify_level6Str(str) {
    return '(' + str + ')';
  }

  getClassify_level7Str(str) {
    switch (str + '') {
      case '1':
        return '①';
      case '2':
        return '②';
      case '3':
        return '③';
      case '4':
        return '④';
      case '5':
        return '⑤';
      case '6':
        return '⑥';
      case '7':
        return '⑦';
      case '8':
        return '⑧';
      case '9':
        return '⑨';
      default: {
        return '';
      }
    }
  }

  numberToChinese(num) {
    const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const units = ['', '十', '百', '千', '万', '十', '百', '千', '亿'];

    if (num === 0) return digits[0];

    let result = '';
    let digitArray = num.toString().split('').reverse();

    for (let i = 0; i < digitArray.length; i++) {
      let digit = parseInt(digitArray[i]);
      let unit = units[i];
      if (digit === 0) {
        if (result.startsWith(digits[0])) continue; // 避免重复的零
        if (i % 4 === 0 && result) unit = units[i]; // 在每个新的四位数字组的开始加上万或亿单位
      }
      result = digits[digit] + unit + result;
    }

    // 处理“十”这个特殊情况
    if (result.startsWith('一十')) {
      result = result.substring(1);
    }

    // 移除多余的零
    result = result.replace(/零+/g, '零').replace(/零+$/, '');

    return result;
  }

  zjUtils(zjStr) {
    // 假设每个部分的最大值
    let maxParts = [Infinity, Infinity, Infinity, Infinity, Infinity, Infinity]; // 对于任意长度的版本号，假设没有明确最大值
    function incrementVersion(version, maxParts) {
      let parts = version.split('.').map(Number);
      for (let i = parts.length - 1; i >= 0; i--) {
        parts[i]++;
        if (i < maxParts.length && parts[i] > maxParts[i]) {
          parts[i] = 0;
        } else {
          break;
        }
      }
      return parts.join('.');
    }

    function isVersionGreaterOrEqual(v1, v2) {
      let parts1 = v1.split('.').map(Number);
      let parts2 = v2.split('.').map(Number);
      let len = Math.max(parts1.length, parts2.length);

      for (let i = 0; i < len; i++) {
        let part1 = parts1[i] || 0;
        let part2 = parts2[i] || 0;
        if (part1 > part2) {
          return true;
        } else if (part1 < part2) {
          return false;
        }
      }
      return true; // 当两个版本号相等时返回 true
    }

    function expandRange(rangeStr, maxParts) {
      let [start, end] = rangeStr.split('~');
      let result = [];
      let current = start;
      while (!isVersionGreaterOrEqual(current, end)) {
        result.push(current);
        current = incrementVersion(current, maxParts);
      }
      result.push(end); // 添加结束版本号
      return result;
    }

    return expandRange(zjStr, maxParts);
  }

  async qdDataAssembleAndCreateFor03B(unit, type, parentNode, defaultQdData, qdList, key, feeName) {
    let { constructId, spId, sequenceNbr } = unit;
    // 这个03B的清单编码需要特殊处理 例如【03B001】【03B002】【03B003】【03B005】
    const fbFxAllData = PricingFileFindUtils.getFbFx(constructId, spId, sequenceNbr);
    const csxmAllData = PricingFileFindUtils.getCSXM(constructId, spId, sequenceNbr);
    let code = '03B001';
    let allQdData = [];
    allQdData = allQdData.concat(fbFxAllData.filter((item) => item.kind == BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(item.bdCode) && item.bdCode.startsWith('03B') && item.bdCode.length == 6));
    allQdData = allQdData.concat(csxmAllData.filter((item) => item.kind == BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(item.bdCode) && item.bdCode.startsWith('03B') && item.bdCode.length == 6));
    if (ObjectUtils.isNotEmpty(allQdData)) {
      let codeArray = [];
      allQdData.forEach((item) => {
        codeArray.push(item.fxCode);
      });
      const max03BCode = codeArray.reduce((max, current) => {
        // 提取后三位的数字部分并转成整数进行比较
        let maxNum = parseInt(max.substring(3), 10);
        let currentNum = parseInt(current.substring(3), 10);
        // 返回较大的那个字符串
        return currentNum > maxNum ? current : max;
      });
      code = this.getNext03BCode(max03BCode);
    }
    //封装清单数据
    let newQd = {
      name: key == 'fwxs' ? feeName : defaultQdData.deName,
      bdName: key == 'fwxs' ? feeName : defaultQdData.deName,
      kind: StepItemCostLevelConstant.qd,
      fxCode: code,
      bdCode: code,
      unit: '项',
      isSupplement: 1,
      quantityExpressionNbr: 1,
      quantityExpression: '1',
      quantity: 1
    };
    return await this.createQd(unit, type, newQd, parentNode, qdList);
  }

  getNext03BCode(current) {
    // 提取前三位的字符部分和后三位的数字部分
    let prefix = current.substring(0, 3); // 取 "03B"
    let numberPart = current.substring(3); // 取 "001"
    // 将后三位的数字部分转换为整数
    let number = parseInt(numberPart, 10);
    // 判断是否已经到达最大值 999
    if (number >= 999) {
      throw new Error('已经达到最大值 03B999，无法生成下一个字符串');
    }
    // 数字部分加 1
    number += 1;
    // 将数字部分转换为三位数字，保留前导 0
    let nextNumberPart = number.toString().padStart(3, '0');
    // 拼接前缀和新的数字部分
    return prefix + nextNumberPart;
  }

  /**
   * 根据type确定清单数据
   * @param unit
   * @param type
   */
  getQdByType(constructId, singleId, unitId, type) {
    let qdList = [];
    //获取分部分项清单 || 对应分部分项
    if (type === ConstructionMeasureTypeConstant.FBFX || type === ConstructionMeasureTypeConstant.DYFBFX) {
      qdList = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
    }
    //获取其他总价措施清单
    if (type === ConstructionMeasureTypeConstant.ZJCS) {
      qdList = PricingFileFindUtils.getQdByZjcs(constructId, singleId, unitId);
    }
    return qdList;
  }

  /**
   * 安装列表展示数据查询
   * @return {Promise<*>}
   */
  async azCostMathList(arg) {
    //查询所有的安装费率数据
    let { constructId, singleId, unitId } = arg;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let is22De = PricingFileFindUtils.is22Unit(unit);
    let anZhuangRateListAllData = await this.app.appDataSource
      .getRepository(is22De ? BaseAnZhuangRate2022 : BaseAnZhuangRate)
      .find();
    anZhuangRateListAllData.forEach((k) => {
      if (!ObjectUtils.isEmpty(k.relationList)) {
        let str = k.relationList.split(' ');
        let strElement = str[0];
        strElement = strElement.slice(0, -3);
        k.rate = k.rate.toFixed(2);
        k.rRate = k.rRate.toFixed(2);
        k.cRate = k.cRate.toFixed(2);
        k.jRate = k.jRate.toFixed(2);
        k.relationList = strElement.concat(' ').concat(str[1]);
      }
    });
    // 过滤出安装工程的数据  主要是去除掉房屋修缮相关的数据  后续处理房屋修缮时再取出来
    const libraryCode = is22De ? QuotaStandard.DEK_ANZHUANG_2022 : QuotaStandard.DEK_ANZHUANG_2012;
    let anZhuangRateList = anZhuangRateListAllData.filter((item) => item.libraryCode == libraryCode);
    const id = 'az';
    let array = await this.getAnZhuangFeeData(anZhuangRateList, arg, is22De, id);
    array.sort((a, b) => a.feeCode - b.feeCode);
    let resTree = [{
      id: id,
      feeName: is22De ? '河北省建设工程消耗量标准（2022）-安装工程' : '河北省安装工程消耗量定额（2012）',
      children: array
    }];
    // 再查询是否需要展示【房屋修缮】的安装数据
    const deArr = unit.itemBillProjects.getNodesArrayByKind(BranchProjectLevelConstant.de);
    let libraryCodes = [QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ, QuotaStandard.DEK_ANZHUANG_2022_FWXS_TJ];
    if (!is22De) {
      libraryCodes = [QuotaStandard.DEK_ANZHUANG_2012_FWXS];
    }
    const fwxsBaseDe = deArr.find((item) => libraryCodes.includes(item.libraryCode));
    if (ObjectUtils.isNotEmpty(fwxsBaseDe)) {
      const fwxsBaseAnZhuangRateArray = anZhuangRateListAllData.filter((item) => libraryCodes.includes(item.libraryCode));
      const fwxsId = 'fwxs';
      const fwxsArr = await this.getAnZhuangFeeData(fwxsBaseAnZhuangRateArray, arg, is22De, fwxsId);
      fwxsArr.sort((a, b) => a.feeCode - b.feeCode);
      resTree.push({
        id: fwxsId,
        feeName: is22De ? '河北省建设工程消耗量标准(2023)-房屋修缮工程' : '河北省房屋修缮工程消耗量定额（2013）-安装',
        children: fwxsArr
      });
    }
    return resTree;
  }

  async getAnZhuangFeeData(anZhuangRateList, arg, is22De, parentId) {
    const map = anZhuangRateList.reduce((result, item) => {
      (result[item.feeName] = result[item.feeName] || []).push(item);
      return result;
    }, {});
    let keys = Object.keys(map);
    let array = [];
    for (let i = 0; i < keys.length; i++) {
      let key = keys[i];
      let anZhuangRate = map[key][0];
      let type = null;
      if ('超高费' === key) {
        type = ConstructionMeasureTypeConstant.ZJCS;
      }
      if ('系统调试费' === key) {
        type = ConstructionMeasureTypeConstant.FBFX;
      }
      if ('垂直运输费' === key) {
        type = ConstructionMeasureTypeConstant.ZJCS;
      }
      if (key.includes('脚手架搭拆')) {
        // 12定额中，安装工程的叫【脚手架搭拆费】  房屋修缮的叫【脚手架搭拆】  所以这里用includes
        type = ConstructionMeasureTypeConstant.ZJCS;
      }
      if ('操作高度增加费' === key) {
        type = ConstructionMeasureTypeConstant.DYFBFX;
      }
      let item = {
        sortNo: NumberUtil.add(i, 1),
        feeName: key,
        feeCode: anZhuangRate.feeCode,
        classLevelList: [],
        type: type,
        baseDeScope: '分部分项',
        isCheck: false,
        parentId: parentId
      };
      await this.deBookListHandler(item, key, map, arg, is22De);
      array.push(item);
    }
    return array;
  }

  /**
   * 安装费用----定额分册列表查询
   * @return {Promise<*>}
   */
  async deBookListHandler(item, key, map, arg, is22De) {
    const splitStr = '+++++';
    let anZhuangRateList = map[key];
    //组装分册集合数据
    const classLevel1Map = anZhuangRateList.reduce((result, item) => {
      let itemKey = item.classLevel1 + splitStr + item.classLevel2;
      if (is22De) {
        itemKey = item.classLevel2;
      }
      (result[itemKey] = result[itemKey] || []).push(item);
      return result;
    }, {});
    let classLevel1Key = Object.keys(classLevel1Map);
    for (const name of classLevel1Key) {
      let data = {};
      //定额分册名称
      data.classLevel1Name = classLevel1Map[name][0].classLevel1;
      data.classLevel2Name = classLevel1Map[name][0].classLevel2;
      //定额下拉集合 --默认清单处理
      //let deList = this.defaultQdHandler(type, classLevel1Map[name], arg);
      let deList = classLevel1Map[name];
      //排序
      deList.sort((a, b) => a.sortNumber - b.sortNumber);
      data.deList = this.cgCostHadler(key, deList, arg);
      item.classLevelList.push(data);
    }
  }

  /**
   * 超高条件查询处理
   */
  cgCostHadler(key, deList, arg) {
    let { layerInterval, heightRange } = arg;
    if (key !== '超高费') {
      return deList;
    }
    let array = [];
    if (heightRange == 0 && layerInterval == 0) {
      // 如果是默认的  直接返回默认的
      for (let i = 0; i < deList.length; i++) {
        array.push(deList[i]);
      }
      return array;
    }
    //条件查询
    for (let i = 0; i < deList.length; i++) {
      let de = deList[i];
      let deHeightRange = de.heightRange;
      let deLayerInterval = de.layerInterval;

      if (heightRange == 0 && layerInterval == 0) {
        array.push(de);
      } else {
        //计算层高
        if (!ObjectUtils.isEmpty(deHeightRange) && (heightRange !== 0 || ObjectUtils.isEmpty(heightRange))) {
          let item = deHeightRange.split('~');
          if (NumberUtil.isBetween(heightRange, parseInt(item[0]), parseInt(item[1]))) {
            array.push(de);
          }
        }
        //计算米
        if (!ObjectUtils.isEmpty(deLayerInterval) && (layerInterval !== 0 || ObjectUtils.isEmpty(layerInterval))) {
          let item1 = deLayerInterval.split('~');
          if (NumberUtil.isBetween(layerInterval, parseInt(item1[0]), parseInt(item1[1]))) {
            array.push(de);
          }
        }
      }
    }
    if (!ObjectUtils.isEmpty(array)) {
      deList.forEach((k) => {
        k.isDefault = 0;
      });
      const maxObject = array.reduce((max, obj) => {
        return obj.sortNumber > max.sortNumber ? obj : max;
      });
      maxObject.isDefault = 1;
    }
    return deList;
  }

  substringByClassifyLevel2(str, substring) {
    const index = str.indexOf(substring);
    if (index !== -1) {
      return str.substring(0, index + 1);
    }
  }

  /**
   * 安装费用----基数定额列表查询
   */
  async baseDeList(arg) {
    let {
      feeCode, unitId, singleId, constructId, azType, zjType, classLevelList, azClassLevelType
    } = arg;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let is22De = PricingFileFindUtils.is22Unit(unit);
    //分部分项下所有的数据
    let { itemBillProjects } = unit;

    //分部分项下所有的数据
    let deByfbfxList = itemBillProjects.flattenTree(itemBillProjects.root);
    if (ObjectUtils.isEmpty(deByfbfxList)) {
      return [];
    }
    let libraryCodes = [is22De ? QuotaStandard.DEK_ANZHUANG_2022 : QuotaStandard.DEK_ANZHUANG_2012];
    if (azClassLevelType == 'fwxs') {
      libraryCodes = is22De ? [QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ, QuotaStandard.DEK_ANZHUANG_2022_FWXS_TJ] : [QuotaStandard.DEK_ANZHUANG_2012_FWXS];
    }
    //分部分项定额
    let array = deByfbfxList.filter((k) => k.kind === '04' && libraryCodes.includes(k.libraryCode));
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }
    //筛选专业
    if (!ObjectUtils.isEmpty(azType)) {
      array = array.filter((k) => k.classifyLevel1 === azType);
    }
    //筛选章节
    if (!ObjectUtils.isEmpty(zjType)) {
      array = array.filter((k) => k.classifyLevel2 === zjType);
    }
    if (ObjectUtils.isEmpty(array)) {
      return [];
    }

    let result = []; //最终返回结果容器
    if (is22De) {
      let zjSet = new Set();
      let libraryCodeSet = new Set();
      for (const classLevel of classLevelList) {
        libraryCodeSet.add(classLevel.isDefaultRow.libraryCode);
        const split = classLevel.isDefaultRow.classLevel2.split(',');
        for (const item of split) {
          if (item.includes('~')) {
            const zj1 = this.zjUtils(item);
            if (ObjectUtils.isNotEmpty(zj1)) {
              for (const zj of zj1) {
                zjSet.add(zj);
              }
            }
          } else {
            zjSet.add(item);
          }
        }
      }
      let zjSelect1 = this.zjSelect([...zjSet], array, azClassLevelType == 'fwxs', Array.from(libraryCodeSet));
      if (ObjectUtils.isNotEmpty(zjSelect1)) {
        zjSelect1 = ArrayUtil.distinctList(zjSelect1, 'sequenceNbr');
      }
      this.findDataByParentId(result, deByfbfxList, zjSelect1.map((a) => a.parentId));
      result.push(...zjSelect1);
    } else {
      let classLevelMap = new Map();
      for (const item of classLevelList) {
        // 检查 Map 中是否存在对应的键
        if (!classLevelMap.has(item.isDefaultRow.classLevel1)) {
          // 如果不存在，则设置对应键的值为一个空数组
          classLevelMap.set(item.isDefaultRow.classLevel1, []);
        }
        classLevelMap
          .get(item.isDefaultRow.classLevel1)
          .push(item.isDefaultRow.classLevel2);
      }
      let keys = [...classLevelMap.keys()];
      //根据所有对应的所有分册筛选
      // array = array.filter(k => keys.includes(k.classifyLevel1));
      array = array.filter((k) => keys.includes(k.classifyLevel1));
      if (ObjectUtils.isEmpty(array)) {
        return [];
      }
      for (const zjKey of keys) {
        const itemArr = classLevelMap.get(zjKey);
        //章节集合
        let zjList = NumberUtil.numToChByBusiness(itemArr);
        //章节名称集合
        const keys = zjList.map((obj) => Object.keys(obj)[0]);
        //筛选章节
        for (let i = array.length - 1; i >= 0; i--) {
          let item = array[i];
          let { classifyLevel1, classifyLevel2, classifyLevel3 } = item;
          if (classifyLevel1 !== zjKey) {
            continue;
          }
          //切割字符串
          let tempClassifyLevel2 = this.substringByClassifyLevel2(classifyLevel2, '章');
          let key = keys.find((k) => k === tempClassifyLevel2);
          if (ObjectUtils.isEmpty(key)) {
            array.splice(i, 1);
            continue;
          }
          const valueArray = zjList.find((obj) => obj.hasOwnProperty(key))[key];
          if (!ObjectUtils.isEmpty(valueArray)) {
            let tempClassifyLevel3 = this.substringByClassifyLevel2(classifyLevel3, '、');
            if (!valueArray.includes(tempClassifyLevel3)) {
              array.splice(i, 1);
            }
          }
        }
      }
      if (ObjectUtils.isNotEmpty(array)) {
        array = ArrayUtil.distinctList(array, 'sequenceNbr');
      }
      this.findDataByParentId(result, deByfbfxList, array.map((a) => a.parentId));
      result.push(...array);
    }
    //处理值包含定额行以及父级的所有数据
    return result;
  }

  /**
   * 安装费用----清单列表查询
   */
  async qdList(arg) {
    let {
      feeCode, unitId, singleId, constructId, relationListId, selectQdId, type
    } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

    let array = [];
    //处理分部分项
    if (ConstructionMeasureTypeConstant.FBFX === type) {
      let { itemBillProjects } = unit;
      array = array.concat(itemBillProjects.getAllNodes());
    }
    if (ConstructionMeasureTypeConstant.ZJCS === type) {
      let { measureProjectTables } = unit;
      array.push(measureProjectTables.root);
      //获取到所有的单价措施标题下数据
      let measure = measureProjectTables.filter((k) => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === type);
      for (const item of measure) {
        // let { datas } = this.service.yuSuanProject.baseBranchProjectOptionService.getDJCSConditionList(measureProjectTables, item);
        let datas = item.children;
        if (!ObjectUtils.isEmpty(datas)) {
          array.push(...datas);
        }
      }
    }

    let deepCopy = ConvertUtil.deepCopy(array);

    let checkFlag = true;
    for (let i = deepCopy.length - 1; i >= 0; i--) {
      let item = deepCopy[i];
      let { kind, sequenceNbr } = item;
      if (BranchProjectLevelConstant.de === kind) {
        deepCopy.splice(i, 1);
      }
      let qdNode = null;
      if (ConstructionMeasureTypeConstant.FBFX === type) {
        qdNode = unit.itemBillProjects.getNodeById(selectQdId);
      }
      if (ConstructionMeasureTypeConstant.ZJCS === type) {
        qdNode = unit.measureProjectTables.getNodeById(selectQdId);
      }
      if (BranchProjectLevelConstant.qd === kind && checkFlag) {
        // 如果是一个已存在的清单  那么用sequenceNbr和qdId作比较查看是否勾选
        if (ObjectUtils.isNotEmpty(qdNode)) {
          if (sequenceNbr === selectQdId) {
            item.isCheck = 1;
            checkFlag = false;
          }
        } else {
          // 如果不是一个已存在的清单  那么就是一个清单册的
          if (item.standardId === relationListId) {
            item.isCheck = 1;
            checkFlag = false;
          }
        }
      }
    }
    return deepCopy;
  }

  findDataByParentId(result, arr, parentIds) {
    for (const parentId of parentIds) {
      const foundItems = arr.find((item) => item.sequenceNbr === parentId);
      if (!result
        .map((i) => i.sequenceNbr)
        .includes(foundItems.sequenceNbr)) {
        result.push(foundItems);
      }
      if (ObjectUtils.isEmpty(foundItems.parentId) || foundItems.parentId === '0') {
        return;
      } else {
        this.findDataByParentId(result, arr, [foundItems.parentId]);
      }
    }
    return result;
  }

  /**
   * 新建清单  如果有空清单直接使用空清单
   */
  async createQd(unit, constructionMeasureType, qdData, parentNode, qdList) {
    //返回的清单数据
    let qDresult = {};
    let { constructId, spId, sequenceNbr } = unit;
    if (constructionMeasureType === ConstructionMeasureTypeConstant.DJCS || constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS) {
      //获取到空数据行的清单
      let emptQd = qdList.find((k) => k.kind === BranchProjectLevelConstant.qd && ObjectUtils.isEmpty(k.fxCode) && ObjectUtils.isEmpty(k.bdCode));
      let insertStrategy = new InsertStrategy({
        constructId, singleId: spId, unitId: sequenceNbr, pageType: 'csxm'
      });
      if (ObjectUtils.isEmpty(emptQd)) {
        if (qdData.isSupplement == 1) {
          qDresult = await insertStrategy.supplement({
            pointLine: parentNode, newLine: qdData, pageInfo: qdData
          });
        } else {
          qDresult = await insertStrategy.execute({
            pointLine: parentNode, newLine: qdData, indexId: qdData.standardId, option: 'insert'
          });
        }
      } else {
        emptQd.name = qdData.name;
        emptQd.kind = StepItemCostLevelConstant.qd;
        emptQd.fxCode = qdData.fxCode;
        emptQd.bdCode = qdData.fxCode;
        emptQd.standardId = qdData.standardId;
        emptQd.unit = qdData.unit;
        qDresult = emptQd;
      }
    }
    //分部分项
    if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
      let emptyQd = qdList.find((k) => k.kind === BranchProjectLevelConstant.qd && ObjectUtils.isEmpty(k.fxCode) && ObjectUtils.isEmpty(k.bdCode));
      if (ObjectUtils.isEmpty(emptyQd)) {
        let insertStrategy = new InsertStrategy({
          constructId, singleId: spId, unitId: sequenceNbr, pageType: 'fbfx'
        });
        if (qdData.isSupplement == 1) {
          qDresult = await insertStrategy.supplement({
            pointLine: parentNode, newLine: qdData, pageInfo: qdData
          });
        } else {
          qDresult = await insertStrategy.execute({
            pointLine: parentNode, newLine: qdData, indexId: qdData.standardId, option: 'insert'
          });
        }
      } else {
        emptyQd.name = qdData.name;
        emptyQd.kind = StepItemCostLevelConstant.qd;
        emptyQd.fxCode = qdData.fxCode;
        emptyQd.bdCode = qdData.fxCode;
        emptyQd.standardId = qdData.standardId;
        emptyQd.unit = qdData.unit;
        qDresult = emptyQd;
      }
    }
    return qDresult;
  }

  /**
   *
   * @param unit 单位
   * @param costDe 费用定额
   * @param titleData 标题数据
   */
  async updateTotalNumber(unit, costDe, titleData, rcjList, mathRate) {
    let { sequenceNbr, spId, constructId } = unit;
    const rBase = costDe.baseNum[1];
    const cBase = costDe.baseNum[2];
    const jBase = costDe.baseNum[3];
    let deMathBase = null;
    let { rRate, cRate, jRate } = mathRate;
    //如果材料的费率为0，则删除该条材料
    for (let i = rcjList.length - 1; i >= 0; i--) {
      let item = rcjList[i];
      let { kind, sequenceNbr, deId } = item;
      //找到费用定额人材机 // && this.calculateBaseHandler(calculateBase,selectDe.allocationMethod).includes(kind)
      if (deId === costDe.sequenceNbr) {
        //人
        if (kind === 1) {
          if (ObjectUtils.isEmpty(rRate) || rRate === 0 || rRate === '0.00') {
            rcjList.splice(i, 1);
            continue;
          } else {
            deMathBase = rBase;
          }
        }
        //机
        if (kind === 3) {
          if (ObjectUtils.isEmpty(jRate) || jRate === 0 || jRate === '0.00') {
            rcjList.splice(i, 1);
            continue;
          } else {
            deMathBase = jBase;
          }
        }
        //材料
        if (![1, 3, 4].includes(kind)) {
          if (ObjectUtils.isEmpty(cRate) || cRate === 0 || cRate === '0.00') {
            rcjList.splice(i, 1);
            continue;
          } else {
            deMathBase = cBase;
          }
        }
        if (item.materialCode !== ConstantUtil.CODE_RGF_ADJUST) {
          // 措施中人工费调整不进行修改
          item.marketPrice = 1;
          item.dePrice = 1;
        }
        //合计数量
        item.totalNumber = NumberUtil.numberScale(NumberUtil.multiplyParams(item.resQty, costDe.quantity, deMathBase), 4);
        //合价
        item.total = NumberUtil.numberScale(NumberUtil.multiply(item.totalNumber, item.marketPrice));
      }
    }

    // 检测是否需要措施中人工费调整
    await this.service.yuSuanProject.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(constructId, spId, sequenceNbr, costDe);

    //计算单价构成
    this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(constructId, spId, sequenceNbr, costDe.sequenceNbr, true, titleData, false);
  }

  /**
   * 根据分摊方式算费率
   */
  allocationMethodCostRate(selectDe, array) {
    let { allocationMethod, rate, rRate, cRate, jRate } = selectDe;

    let mathRate = {};
    if (!ObjectUtils.isEmpty(allocationMethod)) {
      //分摊方式（0非分摊，1分摊）
      if (parseInt(allocationMethod) === 0) {
        /**
         * 【非分摊记取】：即安装费用定额的人材机费率分别记取，其人材机【计算基数】为基数定额的人工费/材料费/机械费；举例如下：
         * Eg2:   安装费用定额BM1采取费分摊方式记取，其中人工费费率25%，机械费25%；
         * 则安装费用定额人工费=基数定额人工费*25%；安装费用定额机械费=基数定额机械费*25%
         * 说明：安装费用定额的人工/材料/机械费率即对应【定额明细-人材机明细-消耗量】
         */
        //人
        if (array.includes(1)) {
          mathRate.rRate = rRate;
        }
        //机
        if (array.includes(3)) {
          //机械费费率
          mathRate.jRate = jRate;
        }
        //材料
        if (!array.includes([1, 3, 4])) {
          //材料费费率
          mathRate.cRate = cRate;
        }
        return mathRate;
      }
      if (parseInt(allocationMethod) === 1) {
        //【分摊记取】：即安装费用定额的人材机分摊总体费率，其人材机【计算基数】相同。需保证：人工分摊费率+材料分摊费率+机械分摊费率=100%;举例如下：
        // Eg1:   安装费用定额2-1968总体费率为7.56%，采取分摊计费法，其中人工占比11.11%，机械88.89%（11.11+88.89=100），
        // 则
        // 人工费费率=7.56%*11.11%=0.839916%；
        // 机械费费率=7.56%*88.89%=6.720084%
        // 安装费用人工费=0.839916%*（基数定额人工费+机械费）；安装费用机械费=6.720084%*（基数定额人工费+机械费）
        if (array.includes(1)) {
          //人工费费率
          const mathRRate = NumberUtil.multiply(NumberUtil.divide100(rate), NumberUtil.divide100(rRate));
          mathRate.rRate = mathRRate;
        }
        //机
        if (array.includes(3)) {
          //机械费费率
          const mathJRate = NumberUtil.multiply(NumberUtil.divide100(rate), NumberUtil.divide100(jRate));
          mathRate.jRate = mathJRate;
        }
        //材料
        if (!array.includes([1, 3, 4])) {
          //材料费费率
          const mathCRate = NumberUtil.multiply(NumberUtil.divide100(rate), NumberUtil.divide100(cRate));
          mathRate.cRate = mathCRate;
        }
        return mathRate;
      }
    }
  }

  /**
   * 分别人材机的计算基数
   */
  rcjBaseCost(selectDe, baseRcjs, deList, mathRate, unit, costDe) {
    let { allocationMethod } = selectDe;
    let { rRate, cRate, jRate } = mathRate;
    let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
    //循环基数定额

    //人，材，机 数据合计
    let rSum = 0;
    let jSum = 0;
    let cSum = 0;
    for (const de of deList) {
      let baseRList = baseRcjs.filter((k) => k.kind === 1 && k.deId === de.sequenceNbr);
      if (!ObjectUtils.isEmpty(baseRList)) {
        const RSum = baseRList.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, de.quantity), 0);
        rSum = NumberUtil.add(RSum, rSum);
      }
      //筛选机明细
      let baseJList = baseRcjs.filter((k) => k.kind === 3 && k.deId === de.sequenceNbr);
      if (!ObjectUtils.isEmpty(baseJList)) {
        baseJList.forEach((k) => {
          let { rDetail, cDetail, jDetail } = PricingFileFindUtils.getRcjDetailGroup(unit, k);
          //人
          if (!ObjectUtils.isEmpty(rDetail)) {
            const RSum = rDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
            rSum = NumberUtil.add(NumberUtil.numberScale(RSum, 2), rSum);
          }
          //材
          if (!ObjectUtils.isEmpty(cDetail)) {
            const CSum = cDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
            cSum = NumberUtil.add(NumberUtil.numberScale(CSum, 2), cSum);
          }
          //机
          if (!ObjectUtils.isEmpty(jDetail)) {
            const JSum = jDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
            jSum = NumberUtil.add(NumberUtil.numberScale(JSum, 2), jSum);
          } else {
            const JSum = NumberUtil.multiplyParams(k.dePrice, k.resQty, de.quantity);
            jSum = NumberUtil.add(JSum, jSum);
          }
        });
      }
      // 筛选材料明细
      let baseCList = baseRcjs.filter((k) => ![1, 3, 4].includes(k.kind) && k.deId === de.sequenceNbr);
      if (!ObjectUtils.isEmpty(baseCList)) {
        baseCList.forEach((k) => {
          let { rDetail, cDetail, jDetail } = PricingFileFindUtils.getRcjDetailGroup(unit, k);
          //人
          if (!ObjectUtils.isEmpty(rDetail)) {
            const RSum = rDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
            rSum = NumberUtil.add(NumberUtil.numberScale(RSum, 2), rSum);
          }
          //材
          if (!ObjectUtils.isEmpty(cDetail)) {
            const CSum = cDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
            cSum = NumberUtil.add(NumberUtil.numberScale(CSum, 2), cSum);
          } else {
            const CSum = NumberUtil.multiplyParams(k.dePrice, k.resQty, de.quantity);
            cSum = NumberUtil.add(CSum, cSum);
          }
          //机
          if (!ObjectUtils.isEmpty(jDetail)) {
            const JSum = jDetail.reduce((total, item) => total + NumberUtil.multiplyParams(item.dePrice, item.resQty, k.resQty, de.quantity), 0);
            jSum = NumberUtil.add(NumberUtil.numberScale(JSum, 2), jSum);
          }
        });
      }
    }

    /**
     * 人工费  RGF
     * 机械费 JXF
     * 材料费 CLF
     * 人工费+机械费 RGF+JXF
     * 人工费+材料费  RGF+CLF
     * 机械费+材料费 JXF+CLF
     * 人工费+机械费+材料费  RGF+JXF+CLF
     */
    let sum = 0;
    if (allocationMethod == 1) {
      let temp = {};
      temp.rSum = rSum;
      temp.jSum = jSum;
      temp.cSum = cSum;
      //分摊
      let baseMathArray = RcjMathEnum.baseMath[selectDe.calculateBase];
      for (const i of baseMathArray) {
        sum = NumberUtil.add(temp[i], sum);
      }
      //计算基数
      return {
        rBase: NumberUtil.multiply(rRate, sum),
        jBase: NumberUtil.multiply(jRate, sum),
        cBase: NumberUtil.multiply(cRate, sum)
      };
    }
    if (allocationMethod == 0) {
      //非分摊
      return {
        rBase: NumberUtil.multiply(NumberUtil.divide100(rRate), rSum),
        jBase: NumberUtil.multiply(NumberUtil.divide100(jRate), jSum),
        cBase: NumberUtil.multiply(NumberUtil.divide100(cRate), cSum)
      };
    }
  }

  /**
   * 计算基数处理
   */
  calculateBaseHandler(calculateBase, allocationMethod) {
    if (allocationMethod == 0) {
      return [1, 2, 3, 5, 6, 7, 8, 9, 10];
    }
    let array = [];
    if (calculateBase.includes('+')) {
      array = calculateBase.split('+');
    }
    if (calculateBase.includes('，')) {
      array = calculateBase.split('，');
    }
    let result = [];
    for (const iter of array) {
      //RGF，CLF，JXF
      if (iter === 'RGF') {
        result.push(1);
      }
      if (iter === 'JXF') {
        result.push(3);
      }
      if (iter === 'CLF') {
        result.push(...[2, 5, 6, 7, 8, 9, 10]);
      }
    }
    return result;
  }

  async queryBaseDeChapter(args) {
    // chapterStr表示当前选择了的章节字符串
    const {
      constructId, singleId, unitId, chapterStr, fascicleStr, azClassLevelType
    } = args;
    const is22De = PricingFileFindUtils.is22Unit(PricingFileFindUtils.getUnit(constructId, singleId, unitId));
    let libraryCode = is22De ? QuotaStandard.DEK_ANZHUANG_2022 : QuotaStandard.DEK_ANZHUANG_2012;
    if (azClassLevelType == 'fwxs') {
      if (is22De) {
        if (fascicleStr == '房屋修缮建筑工程') {
          libraryCode = QuotaStandard.DEK_ANZHUANG_2022_FWXS_TJ;
        } else {
          libraryCode = QuotaStandard.DEK_ANZHUANG_2022_FWXS_AZ;
        }
      } else {
        libraryCode = QuotaStandard.DEK_ANZHUANG_2012_FWXS;
      }
    }
    let result = null;
    if (is22De) {
      result = await this.service.yuSuanProject.baseDe2022Service.listTreeByLibraryCode(libraryCode);
      result = result.childrenList[0];
    } else {
      result = await this.service.yuSuanProject.baseDeService.listTreeByLibraryCode(libraryCode);
    }
    // 筛选分册数据
    result = this.filterFascicle(result, fascicleStr, is22De);
    // 完善章节数据
    this.fillZjData(result);
    if (ObjectUtils.isEmpty(chapterStr)) {
      return result;
    }
    // 先把章节字符串拆分并添加到checkArr里面
    let checkArr = [];
    const chapterStrSplit = chapterStr.split(',');
    for (const str of chapterStrSplit) {
      if (str.includes('~')) {
        checkArr = checkArr.concat(this.zjUtils(str));
      } else {
        checkArr.push(str);
      }
    }
    if (ObjectUtils.isNotEmpty(result)) {
      // 设置是否选中
      this.setCheckFlag(result, checkArr);
    }
    // 手动设置顶层的idx为0
    result.idx = '0';
    // 并给顶层的下级节点设置parentId
    if (ObjectUtils.isNotEmpty(result.childrenList)) {
      for (const item of result.childrenList) {
        item.parentId = result.idx;
      }
    }
    return result;
  }

  setCheckFlag(data, checkArr) {
    if (ObjectUtils.isEmpty(data.childrenList)) {
      return;
    }
    for (const item of data.childrenList) {
      if (checkArr.includes(item.idx)) {
        item.isCheck = 1;
      } else {
        item.isCheck = 0;
      }
      if (item.isCheck == 1) {
        // 如果当前的节点是选中的，那么他的所有子级也都选中
        if (ObjectUtils.isNotEmpty(data.childrenList)) {
          // 如果有子级  那么所有子级也选中
          this.setCheck(item);
        }
      } else {
        this.setCheckFlag(item, checkArr);
      }
    }
  }

  setCheck(data) {
    if (ObjectUtils.isEmpty(data.childrenList)) {
      return;
    }
    for (const item of data.childrenList) {
      item.isCheck = 1;
      this.setCheck(item);
    }
  }

  filterFascicle(data, fascicleStr, is22De) {
    let result = {};
    if (is22De) {
      // 22的过滤掉【classlevel02 = 安装工程其它措施项目】的
      result = data;
      result.childrenList = result.childrenList.filter((item) => item.classifyLevel2 != '安装工程其它措施项目');
      result.idx = null;
    } else {
      // 12定额先处理数据层级结构
      result = data.childrenList.filter((item) => item.name == fascicleStr);
      if (ObjectUtils.isNotEmpty(result)) {
        result = result[0];
      }
      result.idx = null;
      // 处理完之后过滤掉【classify_level1 = 安装工程-绿建补充】的
      // 但是由于12定额的层级结构第一层(classify_level1)就需要进行过滤，所以不会出现安装工程-绿建补充
    }
    return result;
  }

  /**
   * 填充章节数据的idx和parentId
   */
  fillZjData(data) {
    if (ObjectUtils.isEmpty(data.childrenList)) {
      return;
    }
    for (const item of data.childrenList) {
      // 如果是22定额  那么就有7级 依次为：【第一章】 -> 【第一节】 -> 【一、】 -> 【1.】 -> 【(1)】 -> 【①】
      // 如果是12定额  那么就只有4级 依次为：【第一册】 —> 【第一章】 —> 【一、】 —> 【1.】
      item.idx = this.getIdxStr(data, item);
      item.parentId = data.idx;
      this.fillZjData(item);
    }
  }

  getIdxStr(parent, item) {
    let idx = '';
    if (ObjectUtils.isEmpty(item.name)) {
      return idx;
    }
    if (ObjectUtils.isNotEmpty(parent.idx)) {
      idx = parent.idx;
    }
    for (const [key, value] of this._anZjNumMap) {
      for (const v of value) {
        if (item.name.startsWith(v)) {
          if (ObjectUtils.isEmpty(idx)) {
            return key;
          } else {
            return idx + '.' + key;
          }
        }
        // 除了this._anZjNumMap中的以外，还有一种不在map中的：【12定额的classify_level4中有[36.钢管(沟槽连接)]、[35.钢骨架复合塑料管(电熔连接)]这种的，这种映射太多了，单独判断这种】
        const number = this.extractLeadingNumber(item.name);
        if (ObjectUtils.isNotEmpty(number)) {
          if (ObjectUtils.isEmpty(idx)) {
            return number;
          } else {
            return idx + '.' + number;
          }
        }
      }
    }
    return idx;
  }

  extractLeadingNumber(str) {
    // ^ 表示匹配字符串的开头
    // (\d+) 表示匹配并捕获一个或多个数字
    // \. 表示匹配一个点
    const regex = /^(\d+)\./;
    const match = str.match(regex);

    if (match) {
      // match[1] 包含捕获到的第一个数字
      return parseInt(match[1], 10);
    } else {
      // 如果没有匹配到，返回 null
      return null;
    }
  }

  async clearAzCacheByFwxsVersion(constructObj) {
    // 处理安装工程【房修需求】历史版本兼容问题
    let azCount = Helper.compareVersion(constructObj.version, '1.0.20');
    if (azCount < 0) {
      let unitProjects = PricingFileFindUtils.getUnitListByConstructObj(constructObj);
      if (ObjectUtils.isNotEmpty(unitProjects)) {
        for (const unit of unitProjects) {
          delete unit.azCostMathCache;
        }
      }
    }
  }

  async clearAzUnitCacheByFwxsVersion(unit) {
    const projectObjById = PricingFileFindUtils.getProjectObjById(unit.constructId);
    if (ObjectUtils.isEmpty(projectObjById.version)) {
      delete unit.azCostMathCache;
      return;
    }
    let azCount = Helper.compareVersion(projectObjById.version, '1.0.20');
    if (azCount < 0) {
      delete unit.azCostMathCache;
    }
  }
}

AzCostMathService.toString = () => '[class AzCostMathService]';
module.exports = AzCostMathService;
