'use strict';

const {Service, Log} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const path = require('path');
const fs = require('fs')
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");

/**
 * 章节说明、计算规则、工作内容 service
 * @class
 */
class ChapterRelevantDataService extends Service {

    constructor(ctx) {
        super(ctx);
    }


    /**
     * 文件分隔符
     * @type {"\\" | "/" | module:path.path.sep}
     */
    separator = path.sep;

    /**
     * 项目根路径
     * TODO: note: 注意join方法的第二个参数是写死的../../。 移动js文件时一定要修改这里！！！
     * @type {*}
     */
    rootPath = path.join(__dirname, '../../');

    /**
     * 前端项目路径
     * ps: 目前前端项目访问的是local:8080，只能访问前端项目即frontendUi文件夹内的资源。因此将离线html放值前端项目下
     * TODO: note: 注意该前端项目路径是写死的，若前端项目名/路径修改时，一定要同步更改这里！！！
     * @type {string}
     */
    webPath = "frontendUi".concat(this.separator).concat("public").concat(this.separator);

    /**
     * 章节说明、计算规则、工作内容 文件的路径前缀
     * ./offLineData/de/
     * @type {string}
     */
    deChapterDataFilePathPrefix = this.rootPath.concat(this.webPath).concat("offLineData").concat(this.separator).concat("de").concat(this.separator);
    qdChapterDataFilePathPrefix = this.rootPath.concat(this.webPath).concat("offLineData").concat(this.separator).concat("qd").concat(this.separator);

    /**
     * 获取章节说明/计算规则/工作内容
     * @param kind 清单03 定额04
     * @param dataType  1章节说明 2计算规则 3工作内容
     * @param libraryCode
     * @param selectedName
     * @return {Error|string[]}
     */
    getChapterRelevantDataFilePath(kind, dataType, libraryCode, selectedName, classLevels = []) {

        // 判断是定额还是清单,取对应的文件路径前缀
        let filePathPrefix;
        if (kind === BranchProjectLevelConstant.qd) {
            filePathPrefix = this.qdChapterDataFilePathPrefix;
        } else if (BranchProjectLevelConstant.de) {
            filePathPrefix = this.deChapterDataFilePathPrefix;
        } else {
            throw new Error("kind 有误，请正确传递kind：清单03 定额04。");
        }

        // dataType 1章节说明 2计算规则 3工作内容
        let dataTypeValue;
        // 拼接路径
        switch (dataType) {
            case 1:
                dataTypeValue = "章节说明";
                break;
            case 2:
                dataTypeValue = "计算规则";
                break;
            case 3:
                dataTypeValue = "工作内容";
                break;
            default:
                break;
        }

        if (ObjectUtils.isEmpty(dataTypeValue)) {
            throw new Error("dataType 有误，请正确传递dataType：1章节说明 2计算规则 3工作内容。");
        }
        // 绝对路径路径。 例：章节说明/libraryCode/A.1 土、石方工程.html
        let absolutePath = null;
        let index = classLevels.findIndex(l => l == selectedName);
        if(index == -1){
            absolutePath = filePathPrefix.concat(dataTypeValue).concat(this.separator).concat(libraryCode).concat(this.separator).concat(selectedName).concat(".html");
        }else{
            for(let i = index; i >= 0; i--){
                absolutePath = filePathPrefix.concat(dataTypeValue).concat(this.separator).concat(libraryCode).concat(this.separator).concat(classLevels[i]).concat(".html");
                if(fs.existsSync(absolutePath)){
                    break;
                }
            }
        }

        /*// 转为绝对路径返回
        let absolutePath = path.resolve(relativePath);*/
        // 转为相对路径. note: 该相对路径是相当于当前文件的相对路径，前端拿到绝对路径后还需转换一下。
        let relativePath = path.relative(__filename, absolutePath);
        return {"relativePath": relativePath, "absolutePath": absolutePath};
    }

}

ChapterRelevantDataService.toString = () => '[class ChapterRelevantDataService]';
module.exports = ChapterRelevantDataService;
