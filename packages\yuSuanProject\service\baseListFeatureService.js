'use strict';

const { Service} = require('../../../core');
const {BaseListFeature} = require("../model/BaseListFeature");
const {ObjectUtils} = require("../utils/ObjectUtils");

/**
 * base清单特征 service
 * @class
 */
class BaseListFeatureService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseListFeatureDao = this.app.appDataSource.manager.getRepository(BaseListFeature);


    /**
     * 查清单项目特征
     * @param libraryCode 清单册编码
     * @param listCode 清单编码
     * @return {Promise<BaseListFeature[]|Error>}
     */
    async listByQdData(libraryCode, listCode) {
        if (ObjectUtils.isEmpty(libraryCode) || ObjectUtils.isEmpty(listCode)) {
            throw new Error("必传参数清单册编码或清单编码为空");
        }

        if (listCode.length > 9) {
            // 截取清单编码，清单项目特征中的listCode都是9位, 若大于9位截取前9位
            listCode = listCode.substring(0, 9);
        }
        // 根据清单册编码和清单编码查清单特征
        let res = await this.baseListFeatureDao.findBy({libraryCode: libraryCode, listCode: listCode});
        if (res && res.length > 0) {
            res = res.filter(f=>f.featureName && f.featureName !== "");
        }

        return res;
    }

}

BaseListFeatureService.toString = () => '[class BaseListFeatureService]';
module.exports = BaseListFeatureService;
