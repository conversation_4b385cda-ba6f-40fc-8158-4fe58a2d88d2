var {UPCContext} = require('../../../../electron/unit_price_composition/core/UPCContext');
var {UPCCupmuteDe, UPCCupmuteQd} = require('../compute/UPCCupmute');
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const {Snowflake} = require("../../utils/Snowflake");
const _ = require('lodash');
const ConstantUtil = require("../../enum/ConstantUtil");
const CalculationTool = require("../compute/CalculationTool");
const ApplyDependenceDe = require("./applyDependenceDe");
const {re} = require("mathjs");

class EditorApply {
    constructor({constructId, singleId, unitId, type, way, deId, editorplaygroud, pageType}) {
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);//当前单位项目
        this.type = type;//应用范围 定额 分部 单位 工程项目
        this.way = way;

        this.editorplaygroud = editorplaygroud;//单价构成编辑器实例
        this.upcTemplate = [];//单价构成模板数据
        this.allData = this.unit[pageType == "fbfx" ? "itemBillProjects" : "measureProjectTables"];//分部分项数据 或者 措施项目数据
        this.de = this.allData.getNodeById(deId);//应用的定额数据
        this.pageType = pageType;
        this.status = {};
        this.dependence = new Map();
        this.path = this.constructId + "," + this.unitId;
        this.addNew = false;
    }

    prepare() {
        //恢复系统费率
        this.upcTemplate = this.editorplaygroud.upcTemplateList.map(item => {
            // 创建一个新的对象，仅包含除了unitPrice之外的所有字段
            const {displayAllPrice, displayUnitPrice, allPrice, unitPrice, ...rest} = item;
            return rest;
        });

    }

    /**
     * 构建新的模板
     * @param feeFileMap
     * @returns {*}
     */
    buildNewTemplate(feeFileMap) {
        let qfCode = this.upcTemplate[0].qfCode;
        let newQfFile = _.cloneDeep(UPCContext.feeFileList.filter(item => item.qfCode == qfCode)[0]);
        let feeFileCode = "";
        //获取当前需要应用的模板  要么是原始模板 要么是增量模板
        if (feeFileMap.has(qfCode)) {
            //增量模板 获取对应的取费文件映射
            newQfFile = _.cloneDeep(feeFileMap.get(qfCode));
            feeFileCode = UPCContext.qfCodeMap.get(this.path + qfCode);
        } else {
            feeFileCode = newQfFile.qfCode;
        }
        let index = 1;
        //判断当前的映射是否存在 存在则重新生成
        while (UPCContext.qfCodeMap.has(this.path + newQfFile.qfCode + "_" + index)) {
            index += 1;
        }
        //初始化新的 模板文件属性
        let newKey = newQfFile.qfCode + "_" + index;
        UPCContext.qfCodeMap.set(this.path + newKey, feeFileCode);
        newQfFile.sequenceNbr = Snowflake.nextId();
        newQfFile.qfCode = newKey;
        newQfFile.qfName = newQfFile.qfName + "_" + index;
        return newQfFile;
    }

    saveNewTemplate() {
        let feeFileMap = UPCContext.getfeeFilebyPath(this.path);
        let incrementTemplateListMap = UPCContext.getTemplateListbyPath(this.constructId, this.unitId);
        let newQfFile = this.buildNewTemplate(feeFileMap);
        feeFileMap.set(newQfFile.qfCode, newQfFile);
        this.status.newQfFile = newQfFile;
        let newIncrementTemplateList = [];
        this.upcTemplate.forEach(item => {
            item.qfCode = newQfFile.qfCode;
            newIncrementTemplateList.push(item);
        });
        incrementTemplateListMap.set(newQfFile.qfCode, newIncrementTemplateList);
    }

    /**
     * 插入新的模板
     * 如果更新的是的 系统模板
     * 更新的不是系统模板
     * @param path
     */
    insertOrUpdate() {
        //UPCContext
        //单价构成下拉列表系统默认模板
        let incrementTemplateListMap = UPCContext.getTemplateListbyPath(this.constructId, this.unitId);
        let qfCode = this.upcTemplate[0].qfCode;

        if (this.addNew) {
            this.saveNewTemplate();
        } else {
            //更新逻辑
            if (!UPCContext.qfCodeMap.has(this.path + qfCode)) {
                UPCContext.qfCodeMap.set(this.path + qfCode, qfCode);
            }
            incrementTemplateListMap.set(qfCode, this.upcTemplate);
        }
    }


    async apply() {
        this.prepare();
        this[this.type]();
        this.insertOrUpdate();
        await this.afterApply();
    }

    async afterApply() {
        if (!this.unit.feeBuild) {
            this.unit.feeBuild = {};
        }
        this.unit.feeBuild[this.de.sequenceNbr] = this.editorplaygroud.upcTemplateList;
        this.fillData();
        //执行剩下的逻辑 有影响的 定额需要重新计算
        this.handleRateDependence();
        await this.handleDependence();

    }
    //处理费管理费 利润 应用到全局
    handleRateDependence() {
        //如果应用的是系统费率模板直接更新 系统费率
        if (this.de.qfCode.indexOf("_") > -1) return;
        //系统模板  并且多条的时候取 行号最小 且费率不为空 的那一个

        let groupUpc = _.groupBy(this.upcTemplate, "typeCode");
        let glf = groupUpc["UPC_GLF"];
        let lr = groupUpc["UPC_LR"];
        switch (this.type) {
            //应用的单位
            case "useUnit": {
                //如果costFileCode 是空 代表是随主工程
                let unitFeeFile = this.unit.feeFiles.filter(f => {
                    if (this.de.costFileCode) {
                        return f.feeFileCode == this.de.costFileCode;
                    }
                    return f.defaultFeeFlag && f.defaultFeeFlag === 1;
                })[0];
                if (glf && glf.length > 0) {
                    unitFeeFile.managementFee = glf[0].rate;
                }
                if (lr && lr.length > 0) {
                    unitFeeFile.profit = lr[0].rate;
                }
                break;
            }
            //应道到工程项目
            case "useProject": {
                break;
            }
        }
    }

    /**
     *
     * @param dependenceDe 依赖的定额  如果是应用到 当前分部 则是数组 如果是应用到 工程项目或者单位 则是整个单位工程项目
     * @param updateInfo 给目标项目更新的信息
     * @param filter  过滤条件  刷新名称一致的  刷新前缀一致的  所有项目都刷
     * @param constructId
     * @param singleId
     * @param unitId
     * @param allData
     * @returns {Promise<void>}
     */
    async realApplyDependence(dependenceDe, filter, constructId, singleId, unitId, allData) {
        let {costFileCode, costMajorName, qfName, qfCode} = this.de;
        let updateInfo = {
            costFileCode, costMajorName, qfName, qfCode
        }
        let qDids = new Set();
        let feeFileMap = UPCContext.getfeeFilebyPath(this.path);
        let option = feeFileMap.get(this.de.qfCode);
        let upcTemplateList = this.upcTemplate;
        for (let i = 0; i < dependenceDe.length; i++) {
            let item = dependenceDe[i];
            if (filter(item)) {
                let handler = new ApplyDependenceDe({
                    constructId,
                    singleId,
                    unitId,
                    source: item,
                    updateInfo,
                    upcTemplateList
                });
                await handler.update(option);
                await handler.cupmute()
                qDids.add(item.parentId);
            }
        }
        //计算完所有的定额 向上汇总
        let toos = new CalculationTool({constructId, singleId, unitId, allData});
        [...qDids].forEach(id => {
            toos.calculationChian({sequenceNbr: id});
        });
    }

    /**
     * 处理依赖的定额
     * @param key
     * @param dependenceDe
     * @param filter
     */
    async applyDependence(key, dependenceDe, filter) {
        if (_.isArray(dependenceDe)) {
            await this.realApplyDependence(dependenceDe, filter, this.constructId, this.singleId, this.unitId, this.allData);
        } else {
            let {itemBillProjects, measureProjectTables, constructId, spId, sequenceNbr} = dependenceDe;
            //处理分部分项
            await this.realApplyDependence(itemBillProjects.filterAllSubsets(itemBillProjects.root, item => item.kind == ConstantUtil.DE_KIND), filter, constructId, spId, sequenceNbr, itemBillProjects);
            //处理措施项目
            await this.realApplyDependence(measureProjectTables.filterAllSubsets(measureProjectTables.root, item => item.kind == ConstantUtil.DE_KIND), filter, constructId, spId, sequenceNbr, measureProjectTables);
        }
    }
    /**
     * 处理依单赖项  生成过滤条件   重新赋值并计算
     */
    async handleDependence() {
        //新增 costFileCode  costMajorName
        let qfCode = this.de.qfCode;
        let feeFileCode = UPCContext.qfCodeMap.get(this.path + qfCode);
        if (!feeFileCode) {
            feeFileCode = qfCode;
        }
        //处理刷新的条件
        let filter = () => false;
        switch (this.way) {
            //应用范围下名称一致的
            case "name": {
                filter = item => this.de.sequenceNbr != item.sequenceNbr && item.qfCode == qfCode;
                break;
            }
            case "prefix": {
                //如果是前缀一致
                filter = item => this.de.sequenceNbr != item.sequenceNbr && item.qfCode.startsWith(feeFileCode);
                break;
            }
            case "all": {
                filter = item => this.de.sequenceNbr != item.sequenceNbr;
                //刷新所有
                break;
            }
        }
        //处理依赖的定额
        if (this.dependence.size > 0) {
            for (let [key, value] of this.dependence) {
                await this.applyDependence(key, value, filter);
            }
        }

    }

    fillData() {
        let {newQfFile} = this.status;
        if (newQfFile) {
            this.de.qfName = newQfFile.qfName;
            this.de.qfCode = newQfFile.qfCode;
        }
        //重新填充数据 单价构成  定额数据
        if (this.editorplaygroud.isRefresh) {
            this.editorplaygroud.fillData();
            let toos = new CalculationTool({
                constructId: this.constructId,
                singleId: this.singleId,
                unitId: this.unitId,
                allData: this.allData
            });
            toos.calculationChian(this.de.parent)
        }
    }

    /**
     *应用范围应用到分部
     */
    usePart() {
        let filter = item => item.kind == ConstantUtil.DE_KIND && item.qfCode == this.de.qfCode && item.sequenceNbr != this.de.sequenceNbr;
        let fb = this.de.parent.parent;
        //找到所有的定额
        let dependenceDe = this.allData.filterAllSubsets(fb, item => item.kind == ConstantUtil.DE_KIND && item.sequenceNbr != this.de.sequenceNbr);

        //找到匹配的 所有 单价构成模板相同的定额
        let ids = dependenceDe.filter(item => item.qfCode == this.de.qfCode).map(item => item.sequenceNbr);


        let isaddNew = this.unit.itemBillProjects.filter(item => filter(item) && !ids.includes(item.sequenceNbr));
        //如果当期分部分项 或者措施项目没有匹配的定额 则去其他查找
        if (!isaddNew.length) {
            isaddNew = this.unit.measureProjectTables.filter(item => filter(item) && !ids.includes(item.sequenceNbr));
        }
        //判断是否需要 新增模板
        this.addNew = isaddNew.length > 0;
        //收集数据
        this.dependence.set(this.unitId, dependenceDe);

    }

    /**
     * 应用到定额
     */
    useDe() {
        let filter = item => item.kind == ConstantUtil.DE_KIND && item.qfCode == this.de.qfCode && item.sequenceNbr != this.de.sequenceNbr;
        let isaddNew = this.allData.filter(filter);
        if (!isaddNew.length) {
            let otherAllData = this.unit[this.pageType != "fbfx" ? "itemBillProjects" : "measureProjectTables"];
            isaddNew = otherAllData.filter(item => filter(item));
        }
        //判断是否需要 新增模板
        this.addNew = isaddNew.length > 0;
    }

    /**
     * 应用到单位
     */
    useUnit() {
        this.dependence.set(this.unitId, this.unit);
    }

    /**
     * 应用到工程项目
     */
    useProject() {
        let allUnit = PricingFileFindUtils.getUnitList(this.constructId);
        //let filter = item => item.kind == ConstantUtil.DE_KIND && item.sequenceNbr != this.de.sequenceNbr;
        for (let i = 0; i < allUnit.length; i++) {
            this.dependence.set(allUnit[i].sequenceNbr, allUnit[i]);
        }
    }


}

module.exports = {EditorApply}
