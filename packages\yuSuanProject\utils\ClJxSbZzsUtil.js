// exceljs 所需的 polyfills


// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("./ObjectUtils");
const {ExcelUtil} = require("./ExcelUtil");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../../core');
const {NumberUtil} = require("../../../common/NumberUtil");


class ClJxSbZzsUtil{


    constructor() {
    }


    //材料、机械、设备增值税计算表
    async writeDataToClJxSbZzsjsb(data, worksheet){
        function calculateXiaoJi(list) {
            //计算小计
            let xiaoJiCaiLiao = {};
            let chuShuiTotal = 0;
            let hanShuiTotal = 0;
            let jxTotal = 0;
            let xxTotal = 0;
            list.forEach(value => {
                chuShuiTotal +=value.csTotal;
                hanShuiTotal += value.total;
                jxTotal += value.jxTotal;
                xxTotal +=value.xxTotal;
            });
            xiaoJiCaiLiao['total'] = hanShuiTotal;
            xiaoJiCaiLiao['csTotal'] = chuShuiTotal;
            xiaoJiCaiLiao['jxTotal'] = jxTotal;
            xiaoJiCaiLiao['xxTotal'] = xxTotal;
            return xiaoJiCaiLiao;
        }

        //材料List
        let clList = data.clList;

        //机械list
        let jxList = data.jxList;

        //设备list
        let sbList = data.sbList;

        let dataList = new Array();
        let biaotihang1 = {};
        let biaotihang2 = {};
        let biaotihang3 = {};
        biaotihang1.materialName = "材料";
        biaotihang1.type = 1;

        biaotihang2.materialName = "机械";
        biaotihang2.type = 1;

        biaotihang3.materialName = "设备";
        biaotihang3.type = 1;
        if (clList.length > 0) {
            dataList.push(biaotihang1);
            dataList.push(...clList);
        }

        // let xiaoJi1 = calculateXiaoJi(clList);
        // dataList.push(xiaoJi1);
        if (jxList.length > 0) {
            dataList.push(biaotihang2);
            dataList.push(...jxList);
        }

        // let xiaoJi2 = calculateXiaoJi(jxList);
        // dataList.push(xiaoJi2);
        if (sbList.length > 0) {
            dataList.push(biaotihang3);
            dataList.push(...sbList);
        }
        // let xiaoJi3 = calculateXiaoJi(sbList);
        // dataList.push(xiaoJi3);

        let recordListHeadLineNum = [];

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataList.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {

                let cell = rowObject._cells[j];

                if (dataList[countRow].type!=1){
                    if (cell.col == 1) {
                        cell.value = dataList[countRow].materialCode;
                    }
                    if (cell.col == 2){
                        cell.value = dataList[countRow].materialName;
                    }
                    if (cell.col == 3) {
                        cell.value = dataList[countRow].unit;
                    }
                    if (cell.col == 4) {
                        cell.value = dataList[countRow].totalNumber;
                    }
                    if (cell.col == 5) {
                        cell.value = dataList[countRow].taxRemoval;
                    }
                    if (cell.col == 7) {
                        cell.value = dataList[countRow].marketPrice;
                    }
                    if (cell.col == 8) {
                        cell.value = dataList[countRow].total;
                    }
                    if (cell.col == 9) {
                        cell.value = dataList[countRow].csPrice;
                    }
                    if (cell.col == 11) {
                        cell.value = dataList[countRow].csTotal;
                    }
                    if (cell.col == 12) {
                        cell.value = dataList[countRow].jxTotal;
                    }
                    if (cell.col == 13) {
                        cell.value = dataList[countRow].xxTotal;
                    }

                }else {
                    if (cell.col == 2){
                        cell.value = dataList[countRow].materialName;
                        //对材料、设备或机械行  进行合并单元格操作
                        // if (cell.value == "材料" || cell.value == "设备" || cell.value == "机械") {
                        rowObject._cells.forEach(cellItem => {
                            cellItem.value =cell.value;
                        });
                        recordListHeadLineNum.push(rowObject.number);
                    }
                }
            }
        }
        //填充完数据对 应调整的格式进行调整
        recordListHeadLineNum.forEach(rowNum => {
            worksheet.unMergeCells([rowNum,1,rowNum,13]);
            worksheet.mergeCells([rowNum,1,rowNum,13]);
        });

        let heJiCell = ExcelUtil.findValueCell(worksheet,"合  计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[7].value = data.totalObject.total.toFixed(2);//含税价格合计
        row._cells[10].value = data.totalObject.csTotal.toFixed(2);//除税价格合计
        row._cells[11].value = data.totalObject.jxTotal.toFixed(2);//进项税额合计
        row._cells[12].value = data.totalObject.xxTotal.toFixed(2);//销项税额合计
    }

    //增值税进项税额计算汇总表
    async writeDataToZzsJxs(data, worksheet){

        let pageTotal = 0;
        //增值税 list
        let dataList = data.list;
        dataList.sort((a,b)=>a.sortNo - b.sortNo);

        let countRow = -1;
        for (let i = 0; i < worksheet._rows.length; i++) {
            let rowNum = worksheet._rows[i];
            if (rowNum.number >= 4) {  //从第四行开始填充
                countRow++;//从索引零开始 填充
                if (countRow>=dataList.length) break;
                for (let j = 0; j < rowNum._cells.length; j++) {

                    let cell = rowNum._cells[j];

                    if (cell.col == 1) {
                        cell.value = dataList[countRow].sortNo;
                    }
                    if (cell.col == 2){
                        cell.value = dataList[countRow].name;
                    }

                    if (cell.col == 4) {
                        cell.value = dataList[countRow].total;
                        pageTotal += Number.parseFloat(cell.value);
                    }
                }
            }
        }
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[3].value = pageTotal.toFixed(2);//合计

    }


    //规费明细表
    async writeDataToGfeeDetails(dataTotal, worksheet) {

        let orderNum = 0;

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataTotal.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    orderNum++;
                    cell.value = orderNum;
                }
                if (cell.col == 3|| cell.col == 2){
                    cell.value = dataTotal[countRow].costMajorName;
                }
                if (cell.col == 4) {
                    cell.value = dataTotal[countRow].qfjs;//取费基数 固定文字
                }
                if (cell.col == 5|| cell.col == 6) {
                    cell.value = dataTotal[countRow].costFeeBase;//取费金额
                }
                if (cell.col == 7) {
                    cell.value = dataTotal[countRow].gfeeRate;  //费率
                }
                if (cell.col == 8) {
                    cell.value = dataTotal[countRow].feeAmount;  //规费金额
                }
            }
        }
    }


    //安全文明施工费明细表
    async writeDataToAwenFeeDetails(dataTotal, worksheet) {

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataTotal.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataTotal[countRow].sort;
                }
                if (cell.col == 2){
                    cell.value = dataTotal[countRow].costMajorName;
                }
                if (cell.col == 4 || cell.col == 3) {
                    cell.value = dataTotal[countRow].qfjs;//取费基数 固定文字
                }
                if (cell.col == 5) {
                    cell.value = dataTotal[countRow].qfje;//取费金额
                }
                if (cell.col == 6) {
                    cell.value = dataTotal[countRow].basicRate;  //费率
                }
                if (cell.col == 8) {
                    cell.value = dataTotal[countRow].addRate;  //增加费率
                }
                if (cell.col == 9) {
                    cell.value = dataTotal[countRow].feeAmount;  //安文费金额
                }
            }
        }
    }


    //水电费明细表
    async writeDataToWaterDetails(dataTotalObj, worksheet) {
        let dataTotalData = dataTotalObj.waterElectricData.filter(o => ObjectUtils.isNotEmpty(o.dataFlag) && o.dataFlag == 1);
        let dataTotal = dataTotalData.filter(o => (ObjectUtils.isNotEmpty(o.waterCost) && +o.waterCost > 0)
            || (ObjectUtils.isNotEmpty(o.electricCost) && +o.electricCost > 0)
            || (ObjectUtils.isNotEmpty(o.totalCost) && +o.totalCost > 0));

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标

        let a  = 0;
        let b  = 0;
        let c  = 0;
        for (let i = 0; i < dataTotal.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
                        worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i+1;    //序号
                }
                if (cell.col == 2) {
                    cell.value = dataTotal[countRow].projectMajor + " " + dataTotal[countRow].rule;      //专业+规则
                }
                if (cell.col == 3) {
                    cell.value = dataTotal[countRow].calculateBase;
                }
                if (cell.col == 4) {
                    cell.value = dataTotal[countRow].waterRate;  //扣除系数-水费
                }
                if (cell.col == 5) {
                    cell.value = dataTotal[countRow].electricRate;  //扣除系数-电费
                }
                if (cell.col == 6) {
                    cell.value = dataTotal[countRow].totalRate;  //扣除系数-水电费
                }
                if (cell.col == 7) {

                    if (ObjectUtils.isNotEmpty(dataTotal[countRow].waterCost)) {
                        cell.value = dataTotal[countRow].waterCost;  //金额-水费
                        a = NumberUtil.add(a, +dataTotal[countRow].waterCost);
                    }
                }
                if (cell.col == 8) {
                    if (ObjectUtils.isNotEmpty(dataTotal[countRow].electricCost)) {
                        cell.value = dataTotal[countRow].electricCost;  //金额-电费
                        b = NumberUtil.add(b, +dataTotal[countRow].electricCost);
                    }
                }
                if (cell.col == 9) {
                    if (ObjectUtils.isNotEmpty(dataTotal[countRow].totalCost)) {
                        cell.value = dataTotal[countRow].totalCost;  //金额-水电费
                        c = NumberUtil.add(c, +dataTotal[countRow].totalCost);
                    }
                }
            }
        }

        let heJiCell = ExcelUtil.findValueCell(worksheet,"合  计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = a.toFixed(2);
        row._cells[7].value = b.toFixed(2);
        row._cells[8].value = c.toFixed(2);
    }


    //水电费明细独立设置
    async writeDataToWaterDuliDetails(dataTotalObj, worksheet) {
        let dataTotal = [];
        let data = {};
        data.id = 1;
        data.projectMajor = "独立设置";
        data.totalCost = dataTotalObj.customWaterElectric;
        dataTotal.push(data);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataTotal.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number, left, rowNext.number, right]);
                        worksheet.mergeCells([rowNext.number, left, rowNext.number, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataTotal[countRow].id;    //序号
                }
                if (cell.col == 2) {
                    cell.value = dataTotal[countRow].projectMajor;    //独立设置
                }

                if (cell.col == 4) {
                    cell.value = "————";
                }
                if (cell.col == 5) {
                    cell.value = "————";
                }
                if (cell.col == 6) {
                    cell.value = "————";
                }
                if (cell.col == 7) {
                    cell.value = "————";
                }
                if (cell.col == 8) {
                    cell.value = "————";
                }
                if (cell.col == 9) {
                    cell.value = dataTotal[countRow].totalCost;  //金额-水电费
                }
            }
        }

        let heJiCell = ExcelUtil.findValueCell(worksheet, "合  计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[8].value = dataTotalObj.customWaterElectric;
    }



}
module.exports = {
    ClJxSbZzsUtil: new ClJxSbZzsUtil()
};
