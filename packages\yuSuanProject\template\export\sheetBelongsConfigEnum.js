const SheetBelongsConfigEnum = Object.freeze(
        [
            {
                headLine:"封面2 招标控制价（标底）",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"封面2 招标控制价(不含造价咨询人)",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"扉页2 招标控制价（标底）",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"扉页2 招标控制价（不含甲供设备及其税金）",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"封面1 工程量清单",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"扉页1 工程量清单",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"封面3 投标总价",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["投标项目报表"]
            },
            {
                headLine:"扉页3 投标总价",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["投标项目报表"]
            },
            {
                headLine:"扉页3 投标总价(不含甲供设备及其税金)",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["投标项目报表"]
            },
            {
                headLine:"表1-1 工程量清单编制说明",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"表1-2 工程量清单报价说明",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"project",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"project",
                deType:[22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"project",
                deType:[12],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"project",
                deType:[22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-3 工程项目总价表（沧州）",
                projectLevel:"project",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表（沧州）",
                projectLevel:"project",
                deType:[22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表（沧州）",
                projectLevel:"project",
                deType:[12],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-3 工程项目总价表（沧州）",
                projectLevel:"project",
                deType:[22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-4 单项工程费汇总表",
                projectLevel:"project",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-4 单项工程费汇总表",
                projectLevel:"project",
                deType:[22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-4 单项工程费汇总表",
                projectLevel:"project",
                deType:[12],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-4 单项工程费汇总表",
                projectLevel:"project",
                deType:[22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"单项工程造价分析表",
                projectLevel:"project",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"单项工程造价分析表",
                projectLevel:"project",
                deType:[22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"安全生产、文明施工费汇总表",
                projectLevel:"project",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"表1-5 单项工程费汇总表",
                projectLevel:"single",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-5 单项工程费汇总表",
                projectLevel:"single",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"封面2 招标控制价（标底）",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"封面2 招标控制价(不含造价咨询人)",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"扉页2 招标控制价（标底）",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"扉页2 招标控制价（不含甲供设备及其税金）",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表"]
            },
            {
                headLine:"封面1 工程量清单",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"扉页1 工程量清单",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"封面3 投标总价",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["投标项目报表"]
            },
            {
                headLine:"扉页3 投标总价",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["投标项目报表"]
            },
            {
                headLine:"扉页3 投标总价(不含甲供设备及其税金)",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["投标项目报表"]
            },
            {
                headLine:"表1-1 工程量清单编制说明",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"表1-2 工程量清单报价说明",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },




            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"unit",
                deType:[22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-3 工程项目总价表",
                projectLevel:"unit",
                deType:[22],
                lanMuName:["工程量清单报表"],
            },
            {
                headLine:"表1-3 工程项目总价表(沧州)",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表(沧州)",
                projectLevel:"unit",
                deType:[22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-3 工程项目总价表(沧州)",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-3 工程项目总价表(沧州)",
                projectLevel:"unit",
                deType:[22],
                lanMuName:["工程量清单报表"]
            },




            {
                headLine:"表1-5 单位工程费汇总表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-5 单位工程费汇总表--(唐山地区)",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-5 单位工程费汇总表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-5 单位工程费汇总表--(唐山地区)",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-6 分部分项工程量清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-6 分部分项工程量清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-7 单价措施项目工程量清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-7 单价措施项目工程量清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-8 总价措施项目清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-8 总价措施项目清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-9 其他项目清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-9 其他项目清单与计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-10 暂列金额明细表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"表1-11 暂估价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"表1-12 总承包服务费计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-12 总承包服务费计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-13 计日工表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-13 计日工表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-14 招标人供应材料、设备明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-14 招标人供应材料、设备明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-14 招标人供应材料、设备明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"simple",
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-14 招标人供应材料、设备明细表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-15 主要材料、设备明细表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-15 主要材料、设备明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-15 主要材料、设备明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"simple",
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-15 主要材料、设备明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-15 主要材料明细表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-15 主要材料明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-15 主要材料明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"simple",
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-15 主要材料明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表"]
            },


            {
                headLine:"表1-15 设备明细表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"表1-15 设备明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-15 设备明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"simple",
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-15 设备明细表",
                projectLevel:"unit",
                deType:[22],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表"]
            },

            {
                headLine:"表1-16 分部分项工程量清单综合单价分析表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-17 单价措施项目工程量清单综合单价分析表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"表1-18 总价措施项目费分析表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"材料、机械、设备增值税计算表",
                projectLevel:"unit",
                deType:[12],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"材料、机械、设备增值税计算表（实体）",
                projectLevel:"unit",
                deType:[12],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"材料、机械、设备增值税计算表（措施）",
                projectLevel:"unit",
                deType:[12],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"增值税进项税额计算汇总表",
                projectLevel:"unit",
                deType:[12],
                taxCalculationMethod:"general",
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
            {
                headLine:"规费明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"规费明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"水电费明细表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"水电费明细表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"水电费明细表(独立设置)",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"水电费明细表(独立设置)",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"安全文明施工费明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"安全文明施工费明细表",
                projectLevel:"unit",
                deType:[12],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"安全文明施工费明细表",
                projectLevel:"unit",
                deType:[22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"安全文明施工费明细表",
                projectLevel:"unit",
                deType:[22],
                lanMuName:["工程量清单报表"]
            },
            {
                headLine:"签证与索赔计价表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表"]
            },
            {
                headLine:"承包人提供主要材料和工程设备一览表",
                projectLevel:"unit",
                deType:[12,22],
                lanMuName:["招标项目报表","投标项目报表","工程量清单报表"]
            },
        ],
);

module.exports = SheetBelongsConfigEnum;
