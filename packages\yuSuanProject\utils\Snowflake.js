class Snowflake {
    constructor(options = {}) {
        // 默认配置
        const DEFAULTS = {
            startTime: 0,
            dataCenterIdBits: 5,
            workerIdBits: 5,
            sequenceBits: 12,
            workerId: 0,
            dataCenterId: 0,
        };

        // 合并配置
        this.options = Object.assign({}, DEFAULTS, options);

        // 集群数据中心 ID 左移位数
        this.dataCenterIdShift = this.options.workerIdBits + this.options.sequenceBits;

        // 工作机器 ID 左移位数
        this.workerIdShift = this.options.sequenceBits;

        // 时间戳左移位数
        this.timestampLeftShift = this.options.workerIdBits + this.options.sequenceBits + this.options.dataCenterIdBits;

        // 最大的数据中心 ID 和工作机器 ID
        this.maxDataCenterId = ~(-1 << this.options.dataCenterIdBits);
        this.maxWorkerId = ~(-1 << this.options.workerIdBits);

        // 同一毫秒内生成的序列号
        this.sequence = 0;

        // 上次生成 ID 的时间戳
        this.lastTimestamp = -1;

        // 检查配置参数
        this.validateOptions();
    }

    // 生成下一个 ID
    nextId() {
        let timestamp = this.getTimestamp();

        // 时钟回拨
        if (timestamp < this.lastTimestamp) {
            // 抛出异常或等待时钟超前
            throw new Error('Clock moved backwards');
        }

        if (timestamp === this.lastTimestamp) {
            this.sequence = (this.sequence + 1) & this.maxSequence();
            // 每毫秒最多生成 this.maxSequence() 个序列号，如果达到最大值，等待下一毫秒
            if (this.sequence === 0) {
                timestamp = this.nextMillis(this.lastTimestamp);
            }
        } else {
            this.sequence = 0;
        }

        this.lastTimestamp = timestamp;

        return this.generateId(timestamp);
    }

    // 获取时间戳
    getTimestamp() {
        return Date.now() - this.options.startTime;
    }

    // 获取下一个毫秒
    nextMillis(lastTimestamp) {
        let timestamp = this.getTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = this.getTimestamp();
        }
        return timestamp;
    }

    // 生成 ID
    generateId(timestamp) {
        let id = BigInt(timestamp) << BigInt(this.timestampLeftShift);
        id |= BigInt(this.options.dataCenterId) << BigInt(this.dataCenterIdShift);
        id |= BigInt(this.options.workerId) << BigInt(this.workerIdShift);
        id |= BigInt(this.sequence);
        return id.toString();
    }

    // 最大的序列号，用于限制同一毫秒内生成的序列号
    maxSequence() {
        return ~(-1 << this.options.sequenceBits);
    }

    // 验证配置参数
    validateOptions() {
        if (this.options.workerId < 0 || this.options.workerId > this.maxWorkerId) {
            throw new Error(`Worker ID must be between 0 and ${this.maxWorkerId}`);
        }
        if (this.options.dataCenterId < 0 || this.options.dataCenterId > this.maxDataCenterId) {
            throw new Error(`Data center ID must be between 0 and ${this.maxDataCenterId}`);
        }
    }
}


module.exports = {
    Snowflake: new Snowflake()
};

