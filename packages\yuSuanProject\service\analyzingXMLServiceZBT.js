'use strict';



const {ConstructProject} = require("../model/ConstructProject");
const {Service} = require("../../../core");
const {Snowflake} = require("../utils/Snowflake");
const fs = require('fs')
const xml2js = require('xml2js');
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const PolicyDocumentTypeEnum = require("../enum/PolicyDocumentTypeEnum");
const {arrayToTree} = require("../main_editor/tree");
const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../utils/ResponseData");
const {MeasureProjectTable} = require("../model/MeasureProjectTable");
const {OtherProjectZygcZgj} = require("../model/OtherProjectZygcZgj");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {OtherProjectServiceCost} = require("../model/OtherProjectServiceCost");
const {OtherProjectZgj} = require("../model/OtherProjectZgj");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {OtherProject} = require("../model/OtherProject");
const {UnitProject} = require("../model/UnitProject");
const {SingleProject} = require("../model/SingleProject");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
//let abc = new AnalyzingXMLService("123");
class AnalyzingXMLService extends Service{
    constructor(ctx) {
        super(ctx);

        this.qbExtraTableArray = new Array();
        this.qdMap = new Map();
        this.dispNo = 1;

    }


    async  analysis(constructProject,data){
        let 工程量清单数据文件 = data.工程量清单数据文件;
        let 招标信息 = 工程量清单数据文件.招标信息[0].$;
        let 总工程 = 工程量清单数据文件.总工程[0];
        let 工程项目 = 总工程.$;
        let 单项工程 = 总工程.单项工程;
        //单项工程
        let singleProjects = new Array();


        if(ObjectUtils.isEmpty(constructProject.biddingType)){
            constructProject.biddingType =ConstructBiddingTypeConstant.zbProject
        }

        if(ObjectUtils.isEmpty(constructProject.sequenceNbr)){
            constructProject.sequenceNbr = Snowflake.nextId();
        }
        //初始化工程项目的计税方式数据
        await this.service.yuSuanProject.projectTaxCalculationService.initConstructTaxCalculationMethod(constructProject);
        // constructProject.constructName = 工程项目.项目名称;

        constructProject.total = 工程项目.总价;
        constructProject.gfee = 工程项目.规费;
        constructProject.safeFee = 工程项目.安全文明施工费;
        constructProject.sbfsj = 工程项目.设备费;
        constructProject.gfId = '14';
        constructProject.awfId = '44';

        //人工费id
        let rgfPolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
            where: {
                areaId:130100,
                fileType: PolicyDocumentTypeEnum.RGF.code,
            }
        });
        if (!ObjectUtils.isEmpty(rgfPolicyDocumentList)) {

            //时间倒叙排列
            rgfPolicyDocumentList.sort(function(a, b) {
                return b.fileDate.localeCompare(a.fileDate);
            });
            constructProject.rgfId=rgfPolicyDocumentList[0].sequenceNbr;
        }


        let map = this.convertConstructProjectJBXX(constructProject, 招标信息);
        // 编制说明 ---项目层级
        this.service.yuSuanProject.constructProjectService.initProjectOrUnitBZSM(1, constructProject);
        //解析单项工程
        await this.convertSingleProject(单项工程,constructProject);
        //放入内存
        PricingFileWriteUtils.writeToMemory(constructProject);
        return constructProject.sequenceNbr;
    }


    convertConstructProjectJBXX(constructProject, 招标信息) {
        //工程基本信息
        this.service.yuSuanProject.constructProjectService.initProjectOrUnitData(constructProject, 1);
        let constructProjectJBXX = constructProject.constructProjectJBXX;
        for (let i = 0; i < constructProjectJBXX.length; i++) {
            switch (constructProjectJBXX[i].name) {
                case '工程名称':
                    constructProjectJBXX[i].remark = constructProject.constructName;
                    break;
                case '招标人(发包人)':
                    constructProjectJBXX[i].remark = 招标信息.招标人名称;
                    break;
                case '招标人(发包人)法人或其授权人':
                    constructProjectJBXX[i].remark = 招标信息.法定代表人;
                    break;
                default:
                    break;
            }

        }
        constructProject.constructProjectJBXX = constructProjectJBXX;
    }

    /**
     * 解析单项工程
     * @param 单项工程
     * @param constructProject
     */
    async convertSingleProject(单项工程, constructProject) {
        if(!ObjectUtils.isObject(单项工程)){
            let singleProjects = new Array();
            for (let i = 0; i < 单项工程.length; i++) {
                let singleProject = new SingleProject();
                let model = 单项工程[i];
                let $ = model.$;
                singleProject.sequenceNbr = Snowflake.nextId();
                singleProject.constructId = constructProject.sequenceNbr;
                singleProject.projectCode = $.编码;
                singleProject.projectName = $.名称;
                singleProject.total = $.总价;
                singleProject.safeFee = $.安全文明施工费;
                singleProject.gfee = $.规费;
                singleProject.sbf = $.设备费;
                //判断单项下是否还有单项
                if(model.单位工程 === undefined){
                    //还有单项, 递归去解析
                    await this.recursionSingleProject(model.单项工程, singleProject, constructProject);
                }else{
                    //解析单位工程
                    await this.convertUnitProject(model.单位工程, singleProject, constructProject.rgfId);
                }
                singleProjects.push(singleProject);
            }
            constructProject.singleProjects = singleProjects
        }
    }

    /**
     * 递归处理子单项
     */
    async recursionSingleProject(xmlSingleProjects, oldSingleProjects, constructProject) {
        let newSingleProjects = new Array();
        for (let i = 0; i < xmlSingleProjects.length; i++) {
            let singleProject = new SingleProject();
            let model = xmlSingleProjects[i];
            let $ = model.$;
            singleProject.sequenceNbr = Snowflake.nextId();
            singleProject.constructId = constructProject.sequenceNbr;
            singleProject.projectCode = $.编码;
            singleProject.projectName = $.名称;
            singleProject.total = $.总价;
            singleProject.safeFee = $.安全文明施工费;
            singleProject.gfee = $.规费;
            singleProject.sbf = $.设备费;
            //判断单项下是否还有单项
            if(model.单位工程 === undefined){
                await this.recursionSingleProject(model.单项工程, singleProject, constructProject);
            }else{
                //解析单位工程
                await this.convertUnitProject(model.单位工程, singleProject, constructProject.rgfId);
            }
            newSingleProjects.push(singleProject);
        }
        oldSingleProjects.subSingleProjects = newSingleProjects;

    }

    /**
     * 解析单位工程
     * @param 单位工程
     * @param singleProject
     */
    async convertUnitProject(单位工程, singleProject,map,rgfId) {
        if(!ObjectUtils.isObject(单位工程)){

            let unitProjects = new Array();
            for (let i = 0; i < 单位工程.length; i++) {
                let model = 单位工程[i].$;
                let unitProject = new UnitProject();
                unitProject.sequenceNbr = Snowflake.nextId();
                unitProject.upCode = model.编码;
                unitProject.upName = model.名称;
                unitProject.uptotal = model.总价;
                unitProject.csxhj = model.措施项;
                unitProject.djcsxhj = model.单价措施项;
                unitProject.zjcsxhj = model.总价措施项;
                unitProject.safeFee = model.安全文明施工费;
                unitProject.gfee = model.规费;
                unitProject.sbf = model.设备费;
                unitProject.spId = singleProject.sequenceNbr;
                unitProject.constructId = singleProject.constructId;
                unitProject.rgfId = rgfId;
                // 添加工程基本信息 ---单位层级
                this.service.yuSuanProject.constructProjectService.initProjectOrUnitData(unitProject, 3);
                // 编制说明 ---单位层级
                this.service.yuSuanProject.constructProjectService.initProjectOrUnitBZSM(3, unitProject);
                //单位工程费用汇总（包含单位工程部分数据）
                await this.convertUnitProjectSummary(单位工程[i].单位工程费汇总,unitProject);
                //分部分项
                this.dispNo = 1;
                await this.convertItemBill(单位工程[i].分部分项,unitProject);
                this.dispNo = 1;
                //单价措施
                await this.convertMeasureTableDJ(单位工程[i].措施项目,unitProject);
                //总价措施
                await this.convertMeasureTableZJ(单位工程[i].措施项目,unitProject);

                this.qbExtraTableArray = new Array();
                //暂列金额
                await this.convertProvisional(单位工程[i].其它项目[0].暂列金额,unitProject);
                //暂估价
                await this.convertZgjSums(单位工程[i].其它项目[0].暂估价,unitProject);
                //总承包服务费
                await this.convertServiceCosts(单位工程[i].其它项目[0].总承包服务费,unitProject);
                //计日工
                await this.convertDayWorks(单位工程[i].其它项目[0].计日工, unitProject);
                //其他项目 签证与索赔计价表 初始化
                let otherProjectQzSpJjbList = await this.service.yuSuanProject.otherProjectService.getInitOtherProjectQzSpJjb(unitProject);
                unitProject.otherProjectQzAndSuoPeis = otherProjectQzSpJjbList;
                //保存其他项目
                unitProject.otherProjects= ObjectUtils.isNotEmpty(this.qbExtraTableArray)?this.qbExtraTableArray:unitProject.otherProjects;;
                unitProject.constructProjectRcjs = [];
                unitProject.rcjDetailList = [];
                unitProjects.push(unitProject);
            }
            singleProject.unitProjects = unitProjects;
        }

    }

    /**
     * 费用汇总
     * @param 单位工程费汇总表
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertUnitProjectSummary(单位工程费汇总表, unitProject) {
        if(!ObjectUtils.isEmpty(单位工程费汇总表)){
            let 子目 = 单位工程费汇总表[0].子目;

            for (let i = 0; i < 子目.length; i++) {
                let model = 子目[i].$;
                switch(model.序号){
                    //分部分项工程量清单计价合计
                    case "1": {
                        unitProject.fbfxhj  = model.金额;
                        unitProject.fbfxrgf = model.人工费;
                        unitProject.fbfxclf = model.材料费;
                        unitProject.fbfxjxf = model.机械费;
                        break;
                    }

                    //措施项目清单计价合计
                    case "2": {
                        unitProject.csxhj  = model.金额;
                        unitProject.csxrgf = model.人工费;
                        unitProject.csxclf = model.材料费;
                        unitProject.csxjxf = model.机械费;
                        break;
                    }

                    //单价措施项目工程量清单计价合计
                    case "2.1": {
                        unitProject.djcsxhj = model.金额;
                        unitProject.djcsxrgf= model.人工费;
                        unitProject.djcsxclf= model.材料费;
                        unitProject.djcsxjxf= model.机械费;
                        break;
                    }

                    //其他总价措施项目清单计价合计
                    case "2.2": {
                        unitProject.zjcsxhj = model.金额;
                        unitProject.zjcsxrgf= model.人工费;
                        unitProject.zjcsxclf= model.材料费;
                        unitProject.zjcsxjxf= model.机械费;
                        break;
                    }

                    //其他项目清单计价合计
                    case "3": {
                        unitProject.qtxmhj = model.金额;
                        unitProject.qtxmrgf= model.人工费;
                        unitProject.qtxmclf= model.材料费;
                        unitProject.qtxmjxf= model.机械费;
                        break;
                    }

                    //规费
                    case "4": {
                        unitProject.gfee= model.金额;
                        break;
                    }

                    //安全生产、文明施工费
                    case "5": {
                        unitProject.safeFee= model.金额;
                        break;
                    }

                    //税前工程造价
                    case "6": {
                        unitProject.sqgczj= model.金额;
                        break;
                    }

                    //进项税额
                    case "6.1": {
                        unitProject.jxse= model.金额;
                        break;
                    }

                    //销项税额
                    case "7": {
                        unitProject.xxse= model.金额;
                        break;
                    }

                    //增值税应纳税额
                    case "8": {
                        unitProject.zzsynse= model.金额;
                        break;
                    }

                    //附加税费
                    case "9": {
                        unitProject.fjse= model.金额;
                        break;
                    }

                    //税金
                    case "10": {
                        unitProject.sj= model.金额;
                        break;
                    }
                }
            }
        }
    }

    /**
     * 暂列金额
     * @param 暂列金额
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertProvisional(暂列金额, unitProject) {
        if(ObjectUtils.isEmpty(暂列金额)){
            //调用插入暂列金额默认值
            unitProject.otherProjectProvisionals = await this.service.yuSuanProject.otherProjectProvisionalService.importInitProjectProvisional();
            return
        }
        if(!ObjectUtils.isEmpty(暂列金额)){
            let model = 暂列金额[0].$;
            //其他项目赋值
            let otherProject = new OtherProject();
            otherProject.sequenceNbr = Snowflake.nextId();
            otherProject.constructId = unitProject.constructId;
            otherProject.spId = unitProject.spId;
            otherProject.unitId = unitProject.sequenceNbr;
            otherProject.sortNo = 1;
            otherProject.extraName = '暂列金额';
            otherProject.type = OtherProjectCalculationBaseConstant.zljr;
            otherProject.markSj = 1;
            otherProject.markSafa = 1;
            otherProject.amount = 1;
            let qtJxTotal = 0;
            let qtCsTotal = 0;
            if(!ObjectUtils.isEmpty(model)){
                otherProject.dispNo = model.序号;
                otherProject.total = model.金额;
                otherProject.unit = model.计量单位;
                otherProject.description = model.备注;
                // this.qbExtraTableArray.push(otherProject);
            }else {
                otherProject.dispNo = '1';
                // this.qbExtraTableArray.push(otherProject);
                // return;
            }

            if(!ObjectUtils.isEmpty(暂列金额[0].子目)){
                let 暂列金额子目 = 暂列金额[0].子目;
                let otherProjectProvisionalArray = new Array();
                for (let i = 0; i < 暂列金额子目.length; i++) {
                    let $ = 暂列金额子目[i].$;
                    let otherProjectProvisional = new OtherProjectProvisional();
                    otherProjectProvisional.sequenceNbr = Snowflake.nextId();
                    otherProjectProvisional.name = $.项目名称;
                    otherProjectProvisional.unit = $.计量单位;
                    otherProjectProvisional.provisionalSum =ObjectUtils.isEmpty($.金额)?0:Number($.金额) ;
                    otherProjectProvisional.sortNo = i+1;
                    otherProjectProvisional.dispNo = $.序号;
                    otherProjectProvisional.constructId = unitProject.constructId;
                    otherProjectProvisional.spId = unitProject.spId;
                    otherProjectProvisional.unitId = unitProject.sequenceNbr;

                    otherProjectProvisional.amount = 1 ;
                    otherProjectProvisional.price = otherProjectProvisional.provisionalSum;//单价 没有单价所以直接默认赋值暂定金额
                    otherProjectProvisional.taxRemoval = 3 ; //除税系数(%)
                    // 进项合计 暂定金额*除税系数
                    otherProjectProvisional.jxTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectProvisional.provisionalSum,otherProjectProvisional.taxRemoval/100)) ;
                    otherProjectProvisional.csPrice = NumberUtil.subtract(otherProjectProvisional.provisionalSum,otherProjectProvisional.jxTotal);
                    otherProjectProvisional.csTotal = otherProjectProvisional.csPrice;

                    qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectProvisional.jxTotal);
                    qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectProvisional.csTotal);
                    otherProjectProvisionalArray.push(otherProjectProvisional);
                }
                unitProject.otherProjectProvisionals = otherProjectProvisionalArray;
                
            }
            otherProject.jxTotal = qtJxTotal;
            otherProject.csTotal = qtCsTotal;
            this.qbExtraTableArray.push(otherProject);
        }



    }

    /**
     * 暂估价
     * @param 暂估价
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZgjSums(暂估价, unitProject) {

        if(!ObjectUtils.isEmpty(暂估价)){

            let model = 暂估价[0].$;
            //其他项目赋值
            let otherProject = new OtherProject();
            otherProject.sequenceNbr = Snowflake.nextId();
            otherProject.extraName = '暂估价';
            otherProject.sortNo = 2;
            otherProject.constructId = unitProject.constructId;
            otherProject.spId = unitProject.spId;
            otherProject.unitId = unitProject.sequenceNbr;
            otherProject.markSj = 1;
            otherProject.markSafa = 1;
            otherProject.amount = 1;
            if(!ObjectUtils.isEmpty(model)){
                otherProject.dispNo = model.序号;
                otherProject.total = model.金额;
                otherProject.description = model.备注;
            }else {
                otherProject.dispNo = '2';
            }
            this.qbExtraTableArray.push(otherProject);
            let qtJxTotal = 0;
            let qtCsTotal = 0;
            if(!ObjectUtils.isEmpty(暂估价[0].子目)){
                let 子目 = 暂估价[0].子目;
                let cl = 子目.filter(item => item.$.序号 === '1');
                let sb = 子目.filter(item => item.$.序号 === '2');
                let zygc = 子目.filter(item => item.$.序号 === '3');
                //暂时存放临时补充
                let zgjArray =new Array();

                if(ObjectUtils.isEmpty(cl)){
                    //认为材料暂估价没有数据
                    let zgj= new Object();
                    zgj.名称 = '材料暂估价';
                    zgj.序号 = '1'
                    zgjArray.push(zgj);
                    zgj= new Object();
                    zgj.名称 = '';
                    zgj.序号 = '1.1'
                    zgjArray.push(zgj);
                }
                if(ObjectUtils.isEmpty(sb)){
                    //认为设备暂估价没有数据
                    let zgj= new Object();
                    zgj.名称 = '设备暂估价';
                    zgj.序号 = '2'
                    zgjArray.push(zgj);
                    zgj= new Object();
                    zgj.名称 = '';
                    zgj.序号 = '2.1'
                    zgjArray.push(zgj);
                }
                if(ObjectUtils.isEmpty(zygc)){
                    //认为专业工程暂估价没有数据
                    let zgj= new Object();
                    zgj.名称 = '专业工程暂估价';
                    zgj.序号 = '3'
                    zgjArray.push(zgj);
                    zgj= new Object();
                    zgj.名称 = '';
                    zgj.序号 = '3.1'
                    zgjArray.push(zgj);
                }
                //临时存放
                let tmpArray = new Array();
                if(!ObjectUtils.isEmpty(子目)){
                    for (let i = 0; i < 子目.length; i++) {
                        tmpArray.push( 子目[i].$)
                    }
                }

                let allZgjArray = [...zgjArray,...tmpArray];
                let map = new Map();
                let otherProjectClZgjs = new Array();
                let otherProjectSbZgjs = new Array();
                let otherProjectZygcZgjs = new Array();
                let clDispNo= 1,clSortNo=1;
                let sbDispNo= 1,sbSortNo=1;
                let zygcDispNo= 1,zygcSortNo=1;
                for (let i = 0; i < allZgjArray.length; i++) {
                    let zgjElement = allZgjArray[i];
                    let otherProjectZgj = new OtherProjectZgj();

                    if('1'=== zgjElement.序号){
                        otherProject = new OtherProject();
                        let nextId = Snowflake.nextId();
                        otherProject.sequenceNbr = nextId;
                        otherProject.extraName = '材料暂估价';
                        otherProject.sortNo = 3;
                        otherProject.constructId = unitProject.constructId;
                        otherProject.spId = unitProject.spId;
                        otherProject.unitId = unitProject.sequenceNbr;
                        otherProject.dispNo = '2.1';
                        otherProject.total = zgjElement.金额 === ''?0:zgjElement.金额;;
                        otherProject.unit = zgjElement.计量单位;
                        otherProject.description = zgjElement.备注;
                        otherProject.markSj = 0;
                        otherProject.markSafa = 0;
                        otherProject.amount = 1;
                        this.qbExtraTableArray.push(otherProject);
                        map.set(zgjElement.序号,nextId);
                    }
                    if('2'=== zgjElement.序号){
                        otherProject = new OtherProject();
                        let nextId = Snowflake.nextId();
                        otherProject.sequenceNbr = nextId;
                        otherProject.extraName = '设备暂估价';
                        otherProject.sortNo = 4;
                        otherProject.constructId = unitProject.constructId;
                        otherProject.spId = unitProject.spId;
                        otherProject.unitId = unitProject.sequenceNbr;
                        otherProject.dispNo = '2.2';
                        otherProject.total = zgjElement.金额 === ''?0:zgjElement.金额;;
                        otherProject.unit = zgjElement.计量单位;
                        otherProject.description = zgjElement.备注;
                        otherProject.amount = 1;
                        otherProject.markSj = 0;
                        otherProject.markSafa = 0;
                        this.qbExtraTableArray.push(otherProject);
                        map.set(zgjElement.序号,nextId);
                    }
                    if('3'=== zgjElement.序号){
                        otherProject = new OtherProject();
                        let nextId = Snowflake.nextId();
                        otherProject.sequenceNbr = nextId;
                        otherProject.extraName = '专业工程暂估价';
                        otherProject.sortNo = 5;
                        otherProject.constructId = unitProject.constructId;
                        otherProject.spId = unitProject.spId;
                        otherProject.unitId = unitProject.sequenceNbr;
                        otherProject.dispNo = '2.3';
                        otherProject.total = zgjElement.金额 === ''?0:zgjElement.金额;
                        otherProject.unit = zgjElement.计量单位;
                        otherProject.description = zgjElement.备注;
                        otherProject.type = OtherProjectCalculationBaseConstant.zygczgj;
                        otherProject.markSj = 1;
                        otherProject.markSafa = 1;
                        otherProject.amount = 1;
                        this.qbExtraTableArray.push(otherProject);
                        map.set(zgjElement.序号,nextId);

                    }
                    if(!ObjectUtils.isEmpty(zgjElement.序号)){
                        if(zgjElement.序号.indexOf('1.') !== -1){
                            otherProjectZgj = new OtherProjectZgj();
                            otherProjectZgj.sequenceNbr = Snowflake.nextId();
                            otherProjectZgj.price   = zgjElement.市场价;
                            otherProjectZgj.name   = zgjElement.名称;
                            otherProjectZgj.attr  =zgjElement.规格型号;
                            otherProjectZgj.unit  =zgjElement.计量单位;
                            otherProjectZgj.taxRemoval  =zgjElement.除税系数;
                            otherProjectZgj.jxTotal  =zgjElement.进项税额合计;
                            otherProjectZgj.csPrice  =zgjElement.除税市场价;
                            otherProjectZgj.csTotal  =zgjElement.除税合价;
                            otherProjectZgj.parentId = map.get('1');
                            otherProjectZgj.dispNo   = ''+(clDispNo++);
                            otherProjectZgj.sortNo   = clSortNo++;
                            otherProjectZgj.description = zgjElement.备注;
                            otherProjectZgj.constructId = unitProject.constructId;
                            otherProjectZgj.spId = unitProject.spId;
                            otherProjectZgj.unitId = unitProject.sequenceNbr;
                            otherProjectClZgjs.push(otherProjectZgj);
                        }
                        if(zgjElement.序号.indexOf('2.')  !== -1){
                            otherProjectZgj = new OtherProjectZgj();
                            otherProjectZgj.sequenceNbr = Snowflake.nextId();
                            otherProjectZgj.price   = zgjElement.市场价;
                            otherProjectZgj.name   = zgjElement.名称;
                            otherProjectZgj.attr  =zgjElement.规格型号;
                            otherProjectZgj.unit  =zgjElement.计量单位;
                            otherProjectZgj.taxRemoval  =zgjElement.除税系数;
                            otherProjectZgj.jxTotal  =zgjElement.进项税额合计;
                            otherProjectZgj.csPrice  =zgjElement.除税市场价;
                            otherProjectZgj.csTotal  =zgjElement.除税合价;
                            otherProjectZgj.parentId = map.get('2');
                            otherProjectZgj.dispNo   = ''+(sbDispNo++);
                            otherProjectZgj.sortNo   = sbSortNo++;
                            otherProjectZgj.description = zgjElement.备注;
                            otherProjectZgj.constructId = unitProject.constructId;
                            otherProjectZgj.spId = unitProject.spId;
                            otherProjectZgj.unitId = unitProject.sequenceNbr;
                            otherProjectSbZgjs.push(otherProjectZgj);
                        }
                        if(zgjElement.序号.indexOf('3.') !== -1){
                            otherProjectZgj = new OtherProjectZgj();
                            otherProjectZgj.sequenceNbr = Snowflake.nextId();
                            otherProjectZgj.name   = zgjElement.名称;
                            otherProjectZgj.attr  =zgjElement.规格型号;
                            otherProjectZgj.unit  =zgjElement.计量单位;
                            otherProjectZgj.total = zgjElement.市场价;

                            otherProjectZgj.amount = 1 ;
                            otherProjectZgj.price = otherProjectZgj.total;//单价 没有单价所以直接默认赋值暂定金额
                            otherProjectZgj.taxRemoval = 3 ; //除税系数(%)
                            // 进项合计 暂定金额*除税系数
                            otherProjectZgj.jxTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectZgj.total,otherProjectZgj.taxRemoval/100)) ;
                            otherProjectZgj.csPrice = NumberUtil.subtract(otherProjectZgj.total,otherProjectZgj.jxTotal);
                            otherProjectZgj.csTotal = otherProjectZgj.csPrice;

                            otherProjectZgj.parentId = map.get('3');
                            otherProjectZgj.dispNo   = ''+(zygcDispNo++);
                            otherProjectZgj.sortNo   = zygcSortNo++;
                            otherProjectZgj.description = zgjElement.备注;
                            otherProjectZgj.constructId = unitProject.constructId;
                            otherProjectZgj.spId = unitProject.spId;
                            otherProjectZgj.unitId = unitProject.sequenceNbr;

                            qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectZgj.jxTotal);
                            qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectZgj.csTotal);
                            otherProjectZygcZgjs.push(otherProjectZgj);
                        }
                    }
                }
                unitProject.otherProjectClZgjs = otherProjectClZgjs;
                unitProject.otherProjectSbZgjs = otherProjectSbZgjs;
                unitProject.otherProjectZygcZgjs =otherProjectZygcZgjs;
                if(ObjectUtils.isEmpty(otherProjectZygcZgjs)){
                    //调用插入专业工程额默认值
                    unitProject.otherProjectZygcZgjs = await this.service.yuSuanProject.otherProjectZgjService.importInitOtherProjectZygcZgj()
                }

                this.qbExtraTableArray[1].jxTotal =qtJxTotal;
                this.qbExtraTableArray[1].csTotal =qtCsTotal;
                this.qbExtraTableArray[4].jxTotal =qtJxTotal;
                this.qbExtraTableArray[4].csTotal =qtCsTotal;
            }
        }
    }


    /**
     * 总承包服务费
     * @param 总承包服务费
     * @param unitProject
     * @returns {Promise<void>}
     */
    async  convertServiceCosts(总承包服务费, unitProject) {


        let model = 总承包服务费[0].$;

        let otherProject = new OtherProject();
        otherProject.sequenceNbr = Snowflake.nextId();
        otherProject.extraName = '总承包服务费';
        otherProject.sortNo = 6;

        otherProject.constructId = unitProject.constructId;
        otherProject.spId = unitProject.spId;
        otherProject.unitId = unitProject.sequenceNbr;
        otherProject.type = OtherProjectCalculationBaseConstant.zcbfwf;
        otherProject.markSj = 1;
        otherProject.markSafa = 1;
        otherProject.amount = 1;
        if(!ObjectUtils.isEmpty(model)){
            otherProject.description = model.备注;
            otherProject.unit = model.计量单位;
            otherProject.dispNo = model.序号;
            otherProject.total = model.金额;
        }else {
            otherProject.dispNo = '3';
        }


        // this.qbExtraTableArray.push(otherProject);

        let 总承包服务费子目 = 总承包服务费[0].总承包服务费子目;

        if(ObjectUtils.isEmpty(总承包服务费子目)){
            this.qbExtraTableArray.push(otherProject);
            unitProject.otherProjectServiceCosts = this.service.yuSuanProject.otherProjectService.getInitOtherProjectZcbfwfList();
            return;
        }
        // 根据类型分组

        let zcbfwf = new Array();
        for (let i = 0; i < 总承包服务费子目.length; i++) {
            zcbfwf.push(总承包服务费子目[i].$);
        }
        let map = ArrayUtil.group(zcbfwf,'类型');

        let zygcList = map.get('招标人另行发包专业工程');
        let clList = map.get('招标人供应材料');
        let sbList = map.get('招标人供应设备');
        unitProject.otherProjectServiceCosts = new Array();
        await this.setServiceCosts(unitProject, zygcList, '1', '招标人另行发包专业工程');
        await this.setServiceCosts(unitProject, clList, '2', '招标人供应材料');
        await this.setServiceCosts(unitProject, sbList, '3', '招标人供应设备');

        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        otherProject.total = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.fwje)  ;
        }, 0).toFixed(2))
        this.qbExtraTableArray.push(otherProject);
    }

    /**
     * 总承包服务费
     * @param unitProject
     * @param list
     * @param dispNo
     * @param name
     * @returns {Promise<void>}
     */
    async  setServiceCosts(unitProject, list, dispNo, name) {
        let serviceCostsArray = new Array();
        let parentsUuid = Snowflake.nextId();
        let serviceCostsParents = new OtherProjectServiceCost();
        serviceCostsParents.sequenceNbr = parentsUuid;
        serviceCostsParents.dispNo = dispNo;
        serviceCostsParents.fxName = name;
        serviceCostsParents.sortNo = 0;
        serviceCostsParents.constructId = unitProject.constructId;
        serviceCostsParents.spId = unitProject.spId;
        serviceCostsParents.unitId = unitProject.sequenceNbr;
        serviceCostsParents.dataType = 1;

        serviceCostsArray.push(serviceCostsParents);


        if (!ObjectUtils.isEmpty(list)) {
            for (let i = 0; i < list.length; i++) {
                let model = list[i];
                let uuid = Snowflake.nextId();
                let otherProjectServiceCost = new OtherProjectServiceCost();
                otherProjectServiceCost.sequenceNbr  = uuid;
                otherProjectServiceCost.xmje  = model.JSJC;
                otherProjectServiceCost.dispNo  = dispNo+'.'+(i+1);
                otherProjectServiceCost.fxName  = model.XMMC;
                otherProjectServiceCost.fwje  = model.JE;
                otherProjectServiceCost.rate  = model.FL;
                otherProjectServiceCost.parentId  = parentsUuid;
                otherProjectServiceCost.constructId = unitProject.constructId;
                otherProjectServiceCost.spId = unitProject.spId;
                otherProjectServiceCost.unitId = unitProject.sequenceNbr;
                otherProjectServiceCost.sortNo  = i + 1;
                otherProjectServiceCost.amount  = 1;
                otherProjectServiceCost.dataType = 2;
                serviceCostsArray.push(otherProjectServiceCost);
            }
        }else {
            let myNumber = 0;
            let formattedNumber = myNumber.toFixed(2);
            let otherProjectServiceCost = new OtherProjectServiceCost();
            otherProjectServiceCost.sequenceNbr = Snowflake.nextId();
            otherProjectServiceCost.dispNo=dispNo+'.1';
            otherProjectServiceCost.xmje = formattedNumber;
            otherProjectServiceCost.rate= formattedNumber;
            otherProjectServiceCost.fwje = formattedNumber;
            otherProjectServiceCost.dataType =2 ;
            otherProjectServiceCost.parentId = parentsUuid;
            serviceCostsArray.push(otherProjectServiceCost);
        }
        unitProject.otherProjectServiceCosts.push(...serviceCostsArray);

    }

    /**
     * 计日工
     * @param 计日工
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertDayWorks(计日工, unitProject) {
        if (ObjectUtils.isEmpty(计日工)) {
            return;
        }

        const model = 计日工[0].$;

        const otherProject = new OtherProject();
        otherProject.sequenceNbr = Snowflake.nextId();
        otherProject.extraName = '计日工';
        otherProject.sortNo = 7;
        otherProject.constructId = unitProject.constructId;
        otherProject.spId = unitProject.spId;
        otherProject.unitId = unitProject.sequenceNbr;
        otherProject.type = OtherProjectCalculationBaseConstant.jrg;
        otherProject.markSj = 1;
        otherProject.markSafa = 1;
        otherProject.amount = 1;
        if(!ObjectUtils.isEmpty(model)){
            otherProject.dispNo = model.序号;
            otherProject.total = model.金额;
            otherProject.description = model.备注;
        }else {
            otherProject.dispNo = '4';
        }
        // this.qbExtraTableArray.push(otherProject);

        let qtJxTotal = 0;
        let qtCsTotal = 0;

        let dayWorksArray = [];
        let sort = 0;

        const types = ['人工', '材料', '机械'];

        if(!ObjectUtils.isEmpty(计日工[0].子目)){

            let map = new Map();
            for (let i = 0; i < 计日工[0].子目.length; i++) {
                let $ = 计日工[0].子目[i].$;
                if(!ObjectUtils.isEmpty($.序号)){

                    let otherProjectDayWork = new OtherProjectDayWork();
                    let nextId = Snowflake.nextId();
                    if(types[0]===$.名称){
                        //人工
                        map.set(types[0],nextId);
                    }else if(types[1]===$.名称){
                        //材料
                        map.set(types[1],nextId);
                    }else if(types[2]===$.名称){
                        //机械
                        map.set(types[2],nextId);
                    }
                    otherProjectDayWork.sequenceNbr = nextId;
                    otherProjectDayWork.worksName = $.名称;
                    otherProjectDayWork.unit = $.计量单位;


                    otherProjectDayWork.tentativeQuantity = $.数量;
                    otherProjectDayWork.price = $.综合单价;




                    otherProjectDayWork.sortNo = sort++;
                    otherProjectDayWork.dispNo = $.序号;
                    if($.序号.indexOf('1.')!== -1){
                        //人
                        otherProjectDayWork.taxRemoval = 0;
                        otherProjectDayWork.parentId = map.get(types[0]);
                    }else if($.序号.indexOf('2.')!== -1){
                        //材
                        otherProjectDayWork.taxRemoval =  11.28;
                        otherProjectDayWork.parentId = map.get(types[1]);
                    }else if($.序号.indexOf('3.')!== -1){
                        //机械
                        otherProjectDayWork.taxRemoval =  8.66;
                        otherProjectDayWork.parentId = map.get(types[2]);
                    }
                    otherProjectDayWork.dataType = 1;
                    if($.序号.indexOf('.')!== -1){
                        otherProjectDayWork.total = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.tentativeQuantity, otherProjectDayWork.price));
                        otherProjectDayWork.jxTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.total,otherProjectDayWork.taxRemoval/100));
                        otherProjectDayWork.csPrice = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.price,(100-otherProjectDayWork.taxRemoval)/100));
                        otherProjectDayWork.csTotal = NumberUtil.numberScale2(NumberUtil.multiply(otherProjectDayWork.total,(100-otherProjectDayWork.taxRemoval)/100));
                        otherProjectDayWork.dataType = 2;
                    }


                    otherProjectDayWork.constructId = unitProject.constructId;
                    otherProjectDayWork.spId = unitProject.spId;
                    otherProjectDayWork.unitId = unitProject.sequenceNbr;


                    qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectDayWork.jxTotal);
                    qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectDayWork.csTotal);

                    dayWorksArray.push(otherProjectDayWork);
                }

            }
            
            
        }

        otherProject.jxTotal = qtJxTotal;
        otherProject.csTotal = qtCsTotal;
        this.qbExtraTableArray.push(otherProject);

        unitProject.otherProjectDayWorks = dayWorksArray;
    }







    /**
     * 分部分项
     * @param 分部分项工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertItemBill(分部分项, unitProject) {
        let itemBillProjectArray = [];
        let itemBillProject = new ItemBillProject();
        let topId = Snowflake.nextId();
        itemBillProject.sequenceNbr = topId;
        itemBillProject.name = '单位工程';
        itemBillProject.kind = BranchProjectLevelConstant.top;
        itemBillProject.constructId = unitProject.constructId;
        itemBillProject.spId = unitProject.spId;
        itemBillProject.unitId = unitProject.sequenceNbr;
        itemBillProject.displaySign = BranchProjectDisplayConstant.open;
        itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
        itemBillProjectArray.push(itemBillProject);

        let 清单项目 = 分部分项[0].清单项目;
        if (ObjectUtils.isEmpty(清单项目)) {
            unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
            return;
        }

        for (let t = 0; t < 清单项目.length; t++) {
            let $ = 清单项目[t].$;
            itemBillProject = new ItemBillProject();
            itemBillProject.sequenceNbr = Snowflake.nextId();

            itemBillProject.name = $.名称;
            itemBillProject.name = $.名称;
            itemBillProject.bdCode = $.编码;
            itemBillProject.fxCode = $.编码;

            itemBillProject.unit = $.计量单位;
            itemBillProject.quantity = $.工程量;
            //清单工程量=清单工程量表达式/单位符号前数值
            let 单位num = $.计量单位.replace(/[^0-9].*/ig,'')!==''?$.计量单位.replace(/[^0-9].*/ig,''): 1;
            itemBillProject.quantityExpression =NumberUtil.multiplyToString(单位num, $.工程量,3);
            itemBillProject.quantityExpressionNbr = NumberUtil.multiplyToString(单位num, $.工程量,3);
            // itemBillProject.rfee = $.人工费;
            // itemBillProject.cfee = $.材料费;
            // itemBillProject.jfee = $.机械费;
            // itemBillProject.managerFee = $.管理费;
            // itemBillProject.profitFee = $.利润;
            // itemBillProject.price = $.综合单价;
            // itemBillProject.total = $.综合合价;
            itemBillProject.projectAttr = $.项目特征;
            itemBillProject.kind = BranchProjectLevelConstant.qd;
            itemBillProject.dispNo = (this.dispNo++)+'';
            itemBillProject.parentId = topId;
            itemBillProject.constructId = unitProject.constructId;
            itemBillProject.spId = unitProject.spId;
            itemBillProject.unitId = unitProject.sequenceNbr;
            itemBillProject.displaySign = BranchProjectDisplayConstant.noSign;
            itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
            if(ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))){
                //如果map中没有去查数据库
                let res = await this.service.yuSuanProject.baseListService.queryQdByCode(itemBillProject.fxCode);
                if(!ObjectUtils.isEmpty(res)){
                    this.qdMap.set(itemBillProject.fxCode,res)
                }
            }
            itemBillProject.standardId = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))? this.qdMap.get(itemBillProject.fxCode).sequenceNbr:'';
            itemBillProject.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? 0 : 1;
            itemBillProjectArray.push(itemBillProject);
        }

        unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
    }


    /**
     * 单价措施项目（目前只处理单价措施清单）
     * @param 措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableDJ(措施项目, unitProject) {
        if(ObjectUtils.isEmpty(措施项目)){
            return ;
        }
        let 单价措施 = 措施项目[0].单价措施;
        let djMeasureProjectTableArray = new Array();

        let dispNo =1;
        if(!ObjectUtils.isEmpty(单价措施)){
            let 清单项目 = 单价措施[0].清单项目;
            if(!ObjectUtils.isEmpty(清单项目)){
                for (let k = 0; k < 清单项目.length; k++) {
                    let $ = 清单项目[k].$ ;
                    let measureProjectTable = new MeasureProjectTable();
                    measureProjectTable.sequenceNbr =Snowflake.nextId();
                    measureProjectTable.dispNo = (dispNo++)+'';

                    measureProjectTable.name = $.名称;
                    measureProjectTable.name = $.名称;
                    measureProjectTable.bdCode = $.编码;
                    measureProjectTable.fxCode = $.编码;

                    measureProjectTable.projectAttr = $.项目特征;
                    measureProjectTable.unit = $.计量单位;
                    measureProjectTable.quantity = $.工程量;
                    let 单位num = $.计量单位.replace(/[^0-9].*/ig,'')!==''?$.计量单位.replace(/[^0-9].*/ig,''): 1;
                    measureProjectTable.quantityExpression =NumberUtil.multiplyToString(单位num, $.工程量,3);
                    measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToString(单位num, $.工程量,3);
                    // measureProjectTable.rfee = $.人工费;
                    // measureProjectTable.cfee = $.材料费;
                    // measureProjectTable.jfee = $.机械费;
                    // measureProjectTable.managerFee = $.管理费;
                    // measureProjectTable.profitFee = $.利润;
                    // measureProjectTable.price = $.综合单价;
                    // measureProjectTable.total = $.综合合价;
                    measureProjectTable.kind = BranchProjectLevelConstant.qd;
                    //单价措施类型
                    measureProjectTable.constructId = unitProject.constructId;
                    measureProjectTable.spId = unitProject.spId;
                    measureProjectTable.unitId = unitProject.sequenceNbr;
                    measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                    measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                    measureProjectTable.adjustmentCoefficient = 1;
                    if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                        //如果map中没有去查数据库
                        let res =await this.service.yuSuanProject.baseListService.queryQdByCode(measureProjectTable.fxCode);
                        if(!ObjectUtils.isEmpty(res)){
                            this.qdMap.set(measureProjectTable.fxCode,res)
                        }
                    }
                    if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                        if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                            measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                        }
                    }
                    measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                    measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                    djMeasureProjectTableArray.push(measureProjectTable);
                }
            }


        }
        unitProject.djMeasureProjectTableArray = djMeasureProjectTableArray;

    }

    /**
     *
     * @param 总价措施措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableZJ(措施项目, unitProject) {
        if(ObjectUtils.isEmpty(措施项目)){
            return ;
        }
        let 总价措施 = 措施项目[0].总价措施;

        if(!ObjectUtils.isEmpty(总价措施[0].清单项目)){
            let 清单项目 = 总价措施[0].清单项目;
            let zjMeasureProjectTableArray = new Array();
            let awfMeasureProjectTableArray = new Array();
            for (let i = 0; i < 清单项目.length; i++) {
                let $ = 清单项目[i].$;
                let measureProjectTable = new MeasureProjectTable();
                measureProjectTable.sequenceNbr =Snowflake.nextId();


                measureProjectTable.name = $.名称;
                measureProjectTable.name = $.名称;
                measureProjectTable.bdCode = $.编码;
                measureProjectTable.fxCode = $.编码;

                measureProjectTable.projectAttr = $.项目特征;
                // measureProjectTable.unit = '项';
                measureProjectTable.quantity = 1;

                measureProjectTable.quantityExpression ='1';
                measureProjectTable.quantityExpressionNbr = 1;
                // measureProjectTable.rfee = $.人工费单价;
                // measureProjectTable.cfee = $.材料费单价;
                // measureProjectTable.jfee = $.机械费单价;
                // measureProjectTable.managerFee = $.管理费单价;
                // measureProjectTable.profitFee = $.利润单价;
                // measureProjectTable.price = $.综合单价;
                // measureProjectTable.total = $.综合合价;
                measureProjectTable.kind = BranchProjectLevelConstant.qd;
                measureProjectTable.constructId = unitProject.constructId;
                measureProjectTable.spId = unitProject.spId;
                measureProjectTable.unitId = unitProject.sequenceNbr;
                measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                measureProjectTable.adjustmentCoefficient = 1;
                if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                    //如果map中没有去查数据库
                    let res =await this.service.yuSuanProject.baseListService.queryQdByCode(measureProjectTable.fxCode);
                    if(!ObjectUtils.isEmpty(res)){
                        this.qdMap.set(measureProjectTable.fxCode,res)
                    }
                }
                if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                    if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                        measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                    }
                }
                measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                if($.名称==='安全生产、文明施工费'){
                    //安文费
                    measureProjectTable.dispNo = '1';
                    measureProjectTable.unit = '项';
                    awfMeasureProjectTableArray.push(measureProjectTable)
                }else {
                    measureProjectTable.dispNo = (i)+'';
                    //其他总价措施
                    zjMeasureProjectTableArray.push(measureProjectTable);
                }

            }

            unitProject.zjMeasureProjectTableArray = zjMeasureProjectTableArray;
            unitProject.awfMeasureProjectTableArray = awfMeasureProjectTableArray;
        }
    }
}


AnalyzingXMLService.toString = () => '[class AnalyzingXMLService]';
module.exports = AnalyzingXMLService;