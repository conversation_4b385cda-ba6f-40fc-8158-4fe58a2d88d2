const RCJPriceMathHandler = require("./RCJPriceMathHandler");
const { NumberUtil } = require("../../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../../utils/ObjectUtils");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. *n 整体乘
 *    2. R/C/J*n 对应R/C/J 乘
 */
class Kind3TypeCMathHandler extends RCJPriceMathHandler{
    constructor(ruleCtx, math) {
        super(ruleCtx, math);
        this.otherRcj = [];
    }
    async computeResQty() {
        await super.computeResQty();
        // 其他材料不参与标准换算
        // this.mathItem.activeRCJs.push(...this.otherRcj);

        let deRcjs = this.deRcjs;
        for(let relationRcj of this.mathItem.activeRCJs){
            //let deRcj = deRcjs.find((rcj) => rcj.materialCode == relationRcj.materialCode && rcj.libraryCode == relationRcj.libraryCode);
            let deRcj = deRcjs.find((rcj) => rcj.materialCode == relationRcj.materialCode);
            if(ObjectUtils.isNotEmpty(deRcj)){
                // 是否临时删除
                if(deRcj.tempDeleteFlag){
                    deRcj.tempDeleteBackupResQty = NumberUtil.numberScale(deRcj.tempDeleteBackupResQty + relationRcj.resQty, 6);
                }else{
                    deRcj.resQty = NumberUtil.numberScale(deRcj.resQty + relationRcj.resQty, 6);
                }

                if (ObjectUtils.isEmpty(deRcj.changeResQtyRuleIds)) {
                    deRcj.changeResQtyRuleIds = [this.rule.sequenceNbr];
                } else {
                    deRcj.changeResQtyRuleIds.push(this.rule.sequenceNbr);
                }
            }else{
                await this.addNewRCJ(relationRcj.libraryCode, relationRcj.materialCode, relationRcj.resQty);
            }
        }
    }

    // activeRCJ() {
    //     // 默认主材、设备受影响
    //     this.mathItem.activeRCJs = this.effectDeRCJ.filter((rcj) => {
    //         return this.isRcjActive(rcj, true);
    //     });
    // }

    isRcjActive(rcj, isMainMatConvertMod){
        // kind=3,type=c 默认主材、设备受、其他材料都不受mainMatConvertMod的影响


        // if (rcj.kind == RCJKind.主材 && !isMainMatConvertMod) {
        //     return false;
        // }
        // if (rcj.kind == RCJKind.设备 && !isMainMatConvertMod) {
        //     return false;
        // }
        // 其他材料费需要特殊处理，既不修改消耗量，又要添加到当前定额下
        if (this.isOtherRCj(rcj)) {
            this.otherRcj.push(rcj);
            return false;
        }

        return this.mathItem.activeRCJKind.includes(rcj.kind);
    }
}

module.exports = Kind3TypeCMathHandler;