const {ConvertUtil} = require("./ConvertUtils");

class ArrayUtil{
    constructor() {
        this.retain = 2;
    }

    /**
     * 去重
     * @param list 数组
     * @param distinctColumn 去重字段名
     * @return {[]|*[]}
     */
    distinctList(list, distinctColumn) {
        if (!Array.isArray(list)) {
            return [];
        }
        let groupArray = [];
        list.forEach(item => {
            // 是否存在
            let isExist = groupArray.some(g => g[distinctColumn] === item[distinctColumn]);
            // 不存在add
            if (!isExist) {
                groupArray.push(Object.assign({}, item));
            }
        })
        return groupArray;
    }

    //分组-返回Map
    group(list, groupColumn) {
        if (!Array.isArray(list)) {
            return [];
        }
        const groupMap = new Map();
        list.forEach((item) => {
            const key = item[groupColumn];
            if (groupMap.has(key)) {
                groupMap.get(key).push(item);
            } else {
                groupMap.set(key, [item]);
            }
        });
        return groupMap;
    }

    /**
     * 判断是否有交集
     * @param arr1
     * @param arr2
     * @return {boolean}
     */
     hasIntersection(arr1, arr2) {
        const set1 = new Set(arr1);
        for (let item of arr2) {
            if (set1.has(item)) {
                return true;
            }
        }
        return false;
    }



    getKeyValueObject(arr, keyToDistinct, keyToGetValue) {
        const result = arr.reduce((acc, obj) => {
            const key = obj[keyToDistinct];
            const value = obj[keyToGetValue];
            if (!acc.hasOwnProperty(key)) {
                acc[key] = value;
            }
            return acc;
        }, {});
        return result;
    }

    compareObjects(obj1, obj2) {
        let diff = [];

        const str1 = JSON.stringify(obj1);
        const str2 = JSON.stringify(obj2);

        if (str1 !== str2) {
            for (let key in obj1) {
                if (obj1[key] !== obj2[key]) {
                    diff.push(key);
                }
            }
        }

        return diff;
    }

    //const uniqueItems = uniqueByMultipleProperties(items, ['name', 'age']);
    uniqueByMultipleProperties(arr, properties) {
        return arr.reduce((acc, item) => {
            const key = properties.map(prop => item[prop]).join(',');
            if (!acc.some(i => properties.map(prop => i[prop]).join(',') === key)) {
                acc.push(item);
            }
            return acc;
        }, []);
    }



    page(list = [], pageNum= 0, pageSize = 10){
        let start = pageNum * pageSize;
        return ConvertUtil.deepCopy(list.slice(start, start + pageSize));
    }
}

module.exports = {
    ArrayUtil: new ArrayUtil()
};