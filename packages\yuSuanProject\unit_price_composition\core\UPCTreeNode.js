/*
* 项目单价构成模板树节点
* */
export class UPCTreeNode {
    constructor(id,data) {
        this.id = id;
        this.data=data;
        this.children = new Map();
    }

    // 增加节点（插入子节点）
    addChild(childData) {
        this.children.set(childData.id,childData);
        return this;
    }
    addData(data){
        this.data = this.data.concat(data);
    }

    // 查找节点（根据数据查找节点）
    findChildNode(id) {
        if(this.children.has(id)){
            return this.children.get(id);
        }
        return null; // 没有找到匹配的数据
    }
}
