'use strict';

const {Service, Log} = require('../../../core');
const {BaseGsjRate,BaseGsjRate2022} = require("../model/BaseGsjRate");

/**
 * 示例服务
 * @class
 */
class BaseGsjRateService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 查询安文费
     * @param arg
     * @return {Promise<void>}
     */
    async queryByCode(projectType,unitIs2022) {
        let result = await this.app.appDataSource.getRepository(unitIs2022?BaseGsjRate2022:BaseGsjRate).findOne({
            where: {
                code: projectType
            }
        });
        return result;
    }




    /**
     *
     * @param arg
     * @return {Promise<void>}
     */
    async queryByKindAndMethodAndRegion( kind,  taxCalculationMethod,  taxPayingRegion,unitIs2022) {
        let result = await this.app.appDataSource.getRepository(unitIs2022?BaseGsjRate2022:BaseGsjRate).findOne({
            where: {
                kind: kind,
                taxCalculationMethod: taxCalculationMethod,
                taxPayingRegion: taxPayingRegion
            }
        });
        return result;
    }
}

BaseGsjRateService.toString = () => '[class BaseGsjRateService]';
module.exports = BaseGsjRateService;
