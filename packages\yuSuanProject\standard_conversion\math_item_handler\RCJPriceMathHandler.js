const {RCJKind} = require("../../enum/ConversionSourceEnum");
const ConstantUtil = require("../../enum/ConstantUtil");
const MathItemHandler = require("./mathItemHandler");
const {ObjectUtils} = require("../../../../electron/utils/ObjectUtils");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. *n 整体乘
 *    2. R/C/J*n 对应R/C/J 乘
 */
class RCJPriceMathHandler extends MathItemHandler{
    analysisMath() {
        const rcjActionKindMap = new Map([
            ["R", [RCJKind.人工]],
            ["C", [RCJKind.材料, RCJKind.砼, RCJKind.商砼, RCJKind.浆, RCJKind.商浆, RCJKind.配比]],
            ["J", [RCJKind.机械]],
            ["ALL", [RCJKind.人工,RCJKind.材料, RCJKind.砼, RCJKind.商砼, RCJKind.浆, RCJKind.商浆, RCJKind.配比, RCJKind.设备,RCJKind.主材,RCJKind.机械]]
        ]);

        let mathItem = this.mathItem;
        mathItem.type = 1;

        let firstCharacter = mathItem.math.charAt(0);
        if("RJC".includes(firstCharacter)){
            mathItem.RJCSymbol = firstCharacter;
            mathItem.parseMath = mathItem.math.substring(1);
            mathItem.operator = this.mathOperator(mathItem.parseMath);
            mathItem.activeRCJKind = rcjActionKindMap.get(firstCharacter);

        }else if("+-*/".includes(firstCharacter)){
            mathItem.parseMath = mathItem.math;
            mathItem.RJCSymbol = "ALL";
            mathItem.operator = this.mathOperator(mathItem.parseMath);
            mathItem.activeRCJKind = rcjActionKindMap.get("ALL");
        }else{
            throw new Error(`错误的换算公式 ${mathItem.oriMath}`);
        }
    }

    activeRCJ() {
        this.mathItem.activeRCJs = this.effectDeRCJ.filter((rcj) => {
            return this.isRcjActive(rcj, this.ctx.de.mainMatConvertMod);
        });
    }

    isRcjActive(rcj, isMainMatConvertMod){
        if (rcj.kind == RCJKind.主材 && !isMainMatConvertMod) {
            return false;
        }
        if (rcj.kind == RCJKind.设备 && !isMainMatConvertMod) {
            return false;
        }
        if (this.isOtherRCj(rcj)) {
            return false;
        }

        return this.mathItem.activeRCJKind.includes(rcj.kind);
    }
}

module.exports = RCJPriceMathHandler;