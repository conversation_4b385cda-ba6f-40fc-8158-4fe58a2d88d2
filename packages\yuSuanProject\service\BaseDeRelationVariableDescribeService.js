'use strict';

const {Service, Log} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {SqlUtils} = require("../utils/SqlUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {BaseDeRelationVariableDescribe} = require("../model/BaseDeRelationVariableDescribe");
const {TaxCalculationMethodEnum} = require("../enum/TaxCalculationMethodEnum");

/**
 * 国标定额service
 */
class BaseDeRelationVariableDescribeService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseDeRelationVariableDescribeDao = this.app.appDataSource.manager.getRepository(BaseDeRelationVariableDescribe);


    /**
     * 获取父定额的规则组id
     */
    async queryGroupiD(parentDe){
        //获取父定额的规则组id
        let result= await this.baseDeRelationVariableDescribeDao.findOne({
            where: {
                deId: parentDe.standardId
            }
        });
        return  ObjectUtils.isNotEmpty(result)?result.groupid:null;
    }

}

BaseDeRelationVariableDescribeService.toString = () => '[class BaseDeRelationVariableDescribeService]';
module.exports = BaseDeRelationVariableDescribeService;
