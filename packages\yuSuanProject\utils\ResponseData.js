
class ResponseData {
    constructor(status, result, message,code) {
        this.status = status;
        this.result = result;
        this.message = message;
        this.code = code;
    }

    static success(data, message = '操作成功') {
        return new ResponseData(200, data, message,200);
    }

    static fail(message = '操作失败') {
        return new ResponseData(500,null, message, 500);
    }

    toJson() {
        return JSON.stringify(this);
    }
}

module.exports = {
    ResponseData
};
