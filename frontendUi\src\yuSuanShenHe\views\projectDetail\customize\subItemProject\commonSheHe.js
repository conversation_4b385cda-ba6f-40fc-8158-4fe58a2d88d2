/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-06-20 19:30:43
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-03-07 09:45:34
 */

import shApi from '@/api/shApi';
import { projectDetailStore } from '@/store/projectDetail';
import api from '@/api/projectDetail.js';

import { ref } from 'vue';
import { message } from 'ant-design-vue';

export const commonSheHe = ({ refresh }) => {
  const projectStore = projectDetailStore();
  const listAssociationList = ref([]);
  const listAssociationFocus = (val, row) => {
    const { constructId, singleId, ssConstructId, ssSingleId } =
      projectStore.currentTreeGroupInfo;
    const { id, ysshUnitId } = projectStore.currentTreeInfo;
    const params = {
      constructId,
      singleId,
      unitId: id,
      ssConstructId,
      ssSingleId,
      ssUnitId: ysshUnitId,
      qdId: row?.sequenceNbr,
      ssQdId: row?.ysshSysj?.sequenceNbr,
      change: row?.ysshSysj?.change,
    };
    shApi.queryQdAssociation(params).then(res => {
      console.log(JSON.parse(JSON.stringify(res)));
      res.result.unshift('无');
      console.log(JSON.parse(JSON.stringify(res)));
      listAssociationList.value = res.result.map((item, index) => {
        return {
          code: item,
          value: item,
        };
      });
    });
  };
  const listAssociationChange = (val, row) => {
    console.log('listAssociationChange', val, row);
    const { constructId, singleId, ssConstructId, ssSingleId } =
      projectStore.currentTreeGroupInfo;
    const { id, ysshUnitId } = projectStore.currentTreeInfo;
    let apiData = {
      constructId,
      singleId,
      unitId: id,
      qdId: row.sequenceNbr,
    };
    let apiFun = null;
    // if ([0, 3].includes(row?.ysshSysj.change)) {
    //   // 正常项、改项新建关联

    // }
    if (row?.ysshSysj.change === 1) {
      // 增项/绑定关联
      apiFun = shApi.bindQdAssociation;
      apiData.name = val === '无' ? '' : val;
    } else {
      apiFun = shApi.createQdAssociation;
      apiData = {
        constructId,
        singleId,
        unitId: id,
        ssConstructId,
        ssSingleId,
        ssUnitId: ysshUnitId,
        ssQdId: row?.ysshSysj?.sequenceNbr,
        bizType: val === '无' ? 0 : 1,
      };
    }
    console.log('apiData', apiData);
    apiFun(apiData).then(res => {
      console.log(res, '关联列表');
      if (res.code === 200) {
        listAssociationFocus(val, row);
        refresh();
      }
    });
  };

  // 是否审删
  const isSheHeDelete = row => {
    return row?.ysshSysj?.change === 2;
  };
  // 检查当前是否为清单项,并禁用导入依据
  const isDisabledImportBasedOn = (row, operateList) => {
    for (const item of operateList) {
      if (item.name === 'import-based-on') {
        for (const option of item.options) {
          if (row.kind == '03'&&(row?.quantity??-1)!==-1) {
            if (
              !(row.yiJuFileList && row.yiJuFileList.length > 0) &&
              ['删除依据', '查看依据'].includes(option.name)
            ) {
              option.isValid = false;
            } else {
              option.isValid = true;
            }
          } else {
            option.isValid = false;
          }
        }
      }
    }
  };
  // 导入依据、查看、删除
  const importBasedOnMethod = (activeKind, val, callback = null) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: val.sequenceNbr,
    };

    if (activeKind == '01') {
      //导入
      shApi.importYiJuFile(apiData).then(res => {
        console.log(res, 'res导入');
        if (res.status === 200) {
          if(res?.result?.code===500){}
          else{
            message.success(res.message);
          }
          
          refresh('Refresh');
          if (callback) callback();
        }
      });
    } else if (activeKind == '02') {
      let apiData = {};
      if (val) {
        apiData.filePath = val.yiJuFileList[0].filePath;
      }
      shApi.openYiJuFile(apiData).then(res => {
        console.log(res, 'res');
        if (res.status === 200) {
          // message.success(res.message);
        }
      });
    } else if (activeKind == '03') {
      apiData.yiJuFileId = val.yiJuFileList[0].sequenceNbr;
      shApi.removeYiJuFile(apiData).then(res => {
        console.log(res, 'res');
        if (res.status === 200) {
          message.success(res.message);
          refresh('Refresh');
          if (callback) callback();
        }
      });
    }
  };
  /**
   * 操作栏中重点项过滤标注红色
   * @param {*} operateList
   */
  const operateKeyItemFilteringMark = operateList => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
    };
    shApi.getUnitZdxglData(apiData).then(res => {
      console.log('getUnitZdxglData', res);
      if (res.status === 200 && res.result && res.result.length) {
        for (const item of operateList) {
          if (item.name === 'key-item-filtering') {
            item.badgeDot = true;
            item.labelStyle = { color: 'red' };
          }
        }
      } else {
        for (const item of operateList) {
          if (item.name === 'key-item-filtering') {
            item.badgeDot = false;
            item.labelStyle = {};
          }
        }
      }
    });
  };
  /**
   * ysshColumns
   * @description 重新构建ysshColumns columns
   * @param {Array<VxeColumnProps>} Columns - VxeColumnProps对象数组
   * @returns {Array<VxeColumnProps>} - VxeColumnProps对象数组
   */

  const ysshColumns = (columns, type) => {
    if (columns.length > 0) {
      let qfCodeId = columns.findIndex(a => a.field === 'qfCode');
      let quantityExpression = columns.findIndex(
        a => a.field === 'quantityExpression'
      );
      let ysshSysjId = columns.findIndex(a => a.field === 'ysshSysj');
      let ysshSysjChangeQuantity = columns.findIndex(
        a => a.field === 'ysshSysj.changeQuantity'
      );
      let ysshSysjChange = columns.findIndex(
        a => a.field === 'ysshSysj.change'
      );

      if (ysshSysjChange === -1) {
        columns = columns.splice(
          columns.findIndex(a => a.field === 'dispNo'),
          0,
          {
            title: '',
            field: 'ysshSysj.change',
            dataIndex: 'ysshSysj.change',
            width: 30,
            align: 'center',
            fixed: 'left',
          }
        );
      }
    }
    return columns;
  };
  return {
    isSheHeDelete,
    listAssociationList,
    listAssociationFocus,
    listAssociationChange,
    importBasedOnMethod,
    isDisabledImportBasedOn,
    operateKeyItemFilteringMark,
    ysshColumns,
  };
};
