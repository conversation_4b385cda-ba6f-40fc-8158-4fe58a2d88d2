/*
* 定额金额数据填充规则
* */


const baseFn = {
    //直接费单价
    "UPC_ZJF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZJF",
            "cloumn": "displayUnitPrice"
        };
    },
    //人工费单间
    "UPC_RGF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_RGF",
            "cloumn": "displayUnitPrice"
        };
    },
    //材料费单间
    "UPC_CLF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_CLF",
            "cloumn": "displayUnitPrice"
        };
    }
    ,
    //机械费单价
    "UPC_JXF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_JXF",
            "cloumn": "displayUnitPrice"
        };
    },
    //利润费单间
    "UPC_LR_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_LR",
            "cloumn": "displayUnitPrice"
        };
    },
    //管理费费单间
    "UPC_GLF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GLF",
            "cloumn": "displayUnitPrice"
        };
    },
    //主材费单间
    "UPC_ZCF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZCF",
            "cloumn": "displayUnitPrice"
        };
    }
    ,
    //工程造价单间
    "UPC_GCZJ_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GCZJ",
            "cloumn": "displayUnitPrice"
        };
    },
    //规费单价
    "UPC_GF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GF",
            "cloumn": "displayUnitPrice"
        };
    },
    //生产工具使用费单价
    "UPC_SCGJSYF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SCGJSYF",
            "cloumn": "displayUnitPrice"
        };
    },
    //繁华地段管理增加费单价
    "UPC_FHDDGLZJF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FHDDGLZJF",
            "cloumn": "displayUnitPrice"
        };
    },
    //冬季防寒费单价
    "UPC_GJFHF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_GJFHF",
            "cloumn": "displayUnitPrice"
        };
    },
    //山地管护增加费单价
    "UPC_SDGHZJF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SDGLZJF",
            "cloumn": "displayUnitPrice"
        };
    },
    //绿色施工安全防护措施费单价
    "UPC_LSSGAQFHCSF_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_LSSGAQFHCSF",
            "cloumn": "displayUnitPrice"
        };
    },
    //进项税额单价
    "UPC_JXSE_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_JXSE",
            "cloumn": "displayUnitPrice"
        };
    },
    //进项税额单价
    "UPC_FJSE_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FJSE",
            "cloumn": "displayUnitPrice"
        };
    },

    //销项税额单价
    "UPC_XXSE_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_XXSE",
            "cloumn": "displayUnitPrice"
        };
    },
    //增值税应纳税额单价
    "UPC_ZZSYNSE_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_ZZSYNSE",
            "cloumn": "displayUnitPrice"
        };
    },
    //税前工程造价单价
    "UPC_SQGCZJ_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SQGCZJ",
            "cloumn": "displayUnitPrice"
        };
    },
    //风险费用单价
    "UPC_FXFY_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_FXFY",
            "cloumn": "displayUnitPrice"
        };
    },
    //税金单价
    "UPC_SJ_PRICE": () => {
        return {
            "type": "UPC",
            "kind": "UPC_SJ",
            "cloumn": "displayUnitPrice"
        };
    },


}
const rules = {
    "sbfPrice": {
        "name": "设备费单价",
        "mathFormula": `SBF`
    },
    "sbfTotal": {
        "name": "设备费合价",
        "mathFormula": `sbfPrice*DEGCL`//【定额/人材机工程量】*【设备费单价】
    },
    "zgfPrice": {
        "name": "暂估单价",
        "mathFormula": `ZGJCLFHJ+ZGJSBFHJ`
    },
    "zgfTotal": {
        "name": "暂估合价",
        "mathFormula": `zgfPrice*DEGCL`//【定额/人材机工程量】*【暂估价单价】
    },
    "zjfPrice": {
        "name": "直接费单价",
        "mathFormula": `_.sum(UPC_ZJF_PRICE)`
    },
    //直接费合价
    "zjfTotal": {
        "name": "直接费合价",
        "mathFormula": `zjfPrice*DEGCL`
    },
    //人工费单间
    "rfee": {
        "name": "人工费单价",
        "mathFormula": `_.sum(UPC_RGF_PRICE)`
    },
    //人工费合价
    "totalRfee": {
        "name": "人工费合价",
        "mathFormula": `rfee*DEGCL`
    }
    ,
    //材料费单间
    "cfee": {
        "name": "材料费单价",
        "mathFormula": `_.sum(UPC_CLF_PRICE)`
    },
    //材料费合价
    "totalCfee": {
        "name": "材料费合价",
        "mathFormula": `cfee*DEGCL`
    }
    ,
    //机械费UPC_JXF_PRICE
    "jfee": {
        "name": "机械费单价",
        "mathFormula": `_.sum(UPC_JXF_PRICE)`
    },
    //机械费合价  UPC_JXF_PRICE_TOTAL
    "totalJfee": {
        "name": "机械费合价",
        "mathFormula": `jfee*DEGCL`
    },
    //利润费单间 UPC_LR_PRICE
    "profitFee": {
        "name": "利润费单价",
        "mathFormula": `_.sum(UPC_LR_PRICE)`
    },
    //利润费合价  UPC_LR_TOTAL
    "totalProfitFee": {
        "name": "利润费合价",
        "mathFormula": `profitFee*DEGCL`
    }
    ,
    //管理费费单间 UPC_GLF_PRICE
    "managerFee": {
        "name": "管理费单价",
        "mathFormula": `_.sum(UPC_GLF_PRICE)`
    },
    //管理费合价  UPC_GLF_TOTAL
    "totalManagerFee": {
        "name": "管理费合价",
        "mathFormula": `managerFee*DEGCL`
    }
    ,
    //主材费单间  UPC_ZCF_PRICE
    "zcfee": {
        "name": "主材费单间",
        "mathFormula": `_.sum(UPC_ZCF_PRICE)`

    },
    //主材费合价 UPC_ZCF_TOTAL
    "totalZcfee": {
        "name": "主材费合价",
        "mathFormula": `zcfee*DEGCL`
    }
    ,
    //工程造价单间 UPC_GCZJ_PRICE
    "price": {
        "name": "工程造价单价",
        "mathFormula": `_.sum(UPC_GCZJ_PRICE)`
    },
    //工程造价合价 UPC_GCZJ_TOTAL
    "total": {
        "name": "工程造价合价",
        "mathFormula": `price*DEGCL`
    },
    "gfPrice": {
        "name": "规费单价",
        "mathFormula": `_.sum(UPC_GF_PRICE)`
    },
    "gfTotal": {
        "name": "规费合价",
        "mathFormula": `gfPrice*DEGCL`
    },
    "scgjsyfPrice": {
        "name": "生产工具使用费单价",
        "mathFormula": `_.sum(UPC_SCGJSYF_PRICE)`
    },
    "scgjsyfTotal": {
        "name": "生产工具使用费合价",
        "mathFormula": `scgjsyfPrice*DEGCL`
    },
    "fhddglzjfPrice": {
        "name": "繁华地段管理增加费单价",
        "mathFormula": `_.sum(UPC_FHDDGLZJF_PRICE)`
    },
    "fhddglzjfTotal": {
        "name": "繁华地段管理增加费合价",
        "mathFormula": `fhddglzjfPrice*DEGCL`
    },
    "gjfhfPrice": {
        "name": "冬季防寒费单价",
        "mathFormula": `_.sum(UPC_GJFHF_PRICE)`
    },
    "gjfhfTotal": {
        "name": "冬季防寒费单价",
        "mathFormula": `gjfhfPrice*DEGCL`
    },
    "sdghzjfPrice": {
        "name": "山地管护增加费单价",
        "mathFormula": `_.sum(UPC_SDGHZJF_PRICE)`
    },
    "sdghzjfTotal": {
        "name": "山地管护增加费合价",
        "mathFormula": `sdghzjfPrice*DEGCL`
    },
    "lssgaqfhcsfPrice": {
        "name": "绿色施工安全防护措施费单价",
        "mathFormula": `_.sum(UPC_LSSGAQFHCSF_PRICE)`
    },
    "lssgaqfhcsfTotal": {
        "name": "绿色施工安全防护措施费合价",
        "mathFormula": `lssgaqfhcsfPrice*DEGCL`
    },
    "jxsePrice": {
        "name": "进项税额单价",
        "mathFormula": `_.sum(UPC_JXSE_PRICE)`
    },
    "jxseTotal": {
        "name": "进项税额合价",
        "mathFormula": `jxsePrice*DEGCL`
    }
    ,
    "xxsePrice": {
        "name": "销项税额单价",
        "mathFormula": `_.sum(UPC_XXSE_PRICE)`
    },
    "xxseTotal": {
        "name": "销项税额合价",
        "mathFormula": `jxsePrice*DEGCL`
    }
    ,
    "zzsynsePrice": {
        "name": "增值税应纳税额单价",
        "mathFormula": `_.sum(UPC_ZZSYNSE_PRICE)`
    },
    "zzsynseTotal": {
        "name": "增值税应纳税额合价",
        "mathFormula": `zzsynsePrice*DEGCL`
    },
    "fjsePrice": {
        "name": "附加税费单价",
        "mathFormula": `_.sum(UPC_FJSE_PRICE)`
    },
    "fjseTotal": {
        "name": "附加税费合价",
        "mathFormula": `fjsePrice*DEGCL`
    },
    "sqgczjPrice": {
        "name": "税前工程造价单价",
        "mathFormula": `_.sum(UPC_SQGCZJ_PRICE)`
    },
    "sqgczjTotal": {
        "name": "税前工程造价合价",
        "mathFormula": `sqgczjPrice*DEGCL`
    }
    ,
    "fxfyPrice": {
        "name": "风险费用单价",
        "mathFormula": `_.sum(UPC_FXFY_PRICE)`
    },
    "fxfyTotal": {
        "name": "风险费用合价",
        "mathFormula": `fxfyPrice*DEGCL`
    },
    "sjPrice": {
        "name": "税金单价",
        "mathFormula": `_.sum(UPC_SJ_PRICE)`
    },
    "sjTotal": {
        "name": "税金合价",
        "mathFormula": `sjPrice*DEGCL`
    }

}
//计算跳过的判断
let cupfilter = {
    //防寒子目的计算跳过
    "FHZMZJF_DEJ": ({de}) => {
        if (!de.coldResistantSuborder) return false;
        return true;
    }
}


module.exports = {deFillBaseRule: baseFn, deFillRules: rules, cupfilter}

