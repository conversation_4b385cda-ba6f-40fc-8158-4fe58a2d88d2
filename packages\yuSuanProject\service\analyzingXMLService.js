'use strict';



const {ConstructProject} = require("../model/ConstructProject");
const {Service} = require("../../../core");
const {Snowflake} = require("../utils/Snowflake");
const fs = require('fs')
const xml2js = require('xml2js');
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const TaxPayingRegionEnum = require("../enum/TaxPayingRegionEnum");
const ConstantUtil = require("../enum/ConstantUtil");
const PolicyDocumentTypeEnum = require("../enum/PolicyDocumentTypeEnum");
const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const {BaseTaxReformDocuments} = require("../model/BaseTaxReformDocuments");
const {ProjectTaxCalculation} = require("../model/ProjectTaxCalculation");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../utils/ResponseData");
const {MeasureProjectTable} = require("../model/MeasureProjectTable");
const {OtherProjectZygcZgj} = require("../model/OtherProjectZygcZgj");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {OtherProjectServiceCost} = require("../model/OtherProjectServiceCost");
const {OtherProjectZgj} = require("../model/OtherProjectZgj");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {OtherProject} = require("../model/OtherProject");
const {UnitProject} = require("../model/UnitProject");
const {SingleProject} = require("../model/SingleProject");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
//let abc = new AnalyzingXMLService("123");
class AnalyzingXMLService extends Service{
    constructor(ctx) {
        super(ctx);
        //this.getXML();
        // let res= this.getFile();
        // this.hzbAnalysis()

        this.qbExtraTableArray = new Array();
        this.baseTaxReformDocumentsDao = this.app.appDataSource.manager.getRepository(BaseTaxReformDocuments);
    }



    async  analysis(constructProject){
        constructProject.optionLock = true;
        //判断文件与选择文件是否一致
        let factoryName =await ReadXmlUtil.readXmlFactoryName(constructProject.importUrl)
        // if(constructProject.xmlFactory !== factoryName){
        //     return ResponseData.fail('厂家与xml厂家不一致');
        // }
        if(ObjectUtils.isEmpty(factoryName) ){
            return ResponseData.fail('不支持当前xml厂家');
        }

        let data =await ReadXmlUtil.getFile(constructProject.importUrl);
        let deStandard = await this.service.yuSuanProject.baseListDeStandardService.quotaStandardById(constructProject.deStandardId);
        constructProject.deStandardReleaseYear = deStandard.releaseYear;
        let res;
        if("惠招标" === factoryName){
            res = await this.service.yuSuanProject.analyzingXMLServiceHZB.analysis(constructProject, data);
        } /*else if("招标通" === factoryName){
            res = await this.service.yuSuanProject.analyzingXMLServiceZBT.analysis(constructProject, data);
        } */else if("冀招标"=== factoryName ||"优招标" ===factoryName){
            res = await this.service.yuSuanProject.analyzingXMLServiceJZBYZB.analysis(constructProject, data);
        } else if("招采进宝" === factoryName){
            res = await this.service.yuSuanProject.analyzingXMLServiceZCJB.analysis(constructProject, data);
        } else if("云采供" === factoryName){
            res = await this.service.yuSuanProject.analyzingXMLServiceYCG.analysis(constructProject, data);
        } else if("E招冀成"=== factoryName ){
            res = await this.service.yuSuanProject.analyzingXMLServiceEZJC.analysis(constructProject, data);
        }else if("招标通"=== factoryName ){
            res = await this.service.yuSuanProject.analyzingXMLServiceYSF.analysis(constructProject, data);
        }


        if(constructProject.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12){
            constructProject.rgfInMeasureAndRPriceInMechanicalAction = true;

            //设置默认人工费
            //人工费id
            let rgfPolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
                where: {
                    areaId:130100,
                    fileType: PolicyDocumentTypeEnum.RGF.code,
                }
            });
            if (!ObjectUtils.isEmpty(rgfPolicyDocumentList)) {

                //时间倒叙排列
                rgfPolicyDocumentList.sort(function(a, b) {
                    return b.fileDate.localeCompare(a.fileDate);
                });
                constructProject.rgfId=rgfPolicyDocumentList[0].sequenceNbr;
            }
        }


        if(!ObjectUtils.isEmpty(res)){
            return res;
        }



        // await this.service.yuSuanProject.constructProjectService.countCostCodePriceAll(constructProject);
        // await this.service.yuSuanProject.management.trigger("importData", constructProject.sequenceNbr);
        return constructProject.sequenceNbr;
    }


    /**
     * 获取默认 人工费ID 取 河北石家庄最新一期人工费ID
     * @returns {Promise<string|null>}
     */
    async getDefaultRgf(){
        let rgfPolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
            where: {
                areaId:130100,
                fileType: PolicyDocumentTypeEnum.RGF.code,
            }
        });
        if (!ObjectUtils.isEmpty(rgfPolicyDocumentList)) {

            //时间倒叙排列
            rgfPolicyDocumentList.sort(function(a, b) {
                return b.fileDate.localeCompare(a.fileDate);
            });
            return rgfPolicyDocumentList[0].sequenceNbr;
        }

        return null;
    }

    /**
     * 导入XML后初始化计税方式
     * @param obj
     * @param type 1 一般 0 简易
     * @returns {Promise<void>}
     */


}


AnalyzingXMLService.toString = () => '[class AnalyzingXMLService]';
module.exports = AnalyzingXMLService;
