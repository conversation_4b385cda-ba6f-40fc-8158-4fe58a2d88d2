const {ObjectUtils} = require("../../utils/ObjectUtils");
const EE = require('../../../../core/ee');
const RcjReplaceStrategy = require("../../rcj_handle/replace/replaceRcjStrategy");
const {UnitRcjCacheUtil} = require("../../rcj_handle/cache/UnitRcjCacheUtil");
const { NumberUtil } = require("../../../../electron/utils/NumberUtil");
const {BaseRcj} = require("../../model/BaseRcj");
const {BaseRcj2022} = require("../../model/BaseRcj2022");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const RcjLevelMarkConstant = require("../../enum/RcjLevelMarkConstant");
const TaxCalculationMethodEnum = require("../../enum/TaxCalculationMethodEnum");
const { Snowflake } = require('../../utils/Snowflake');

class ConversionService {
    constructor() {
        this.app = EE.app;
        this.service = this.app.service;
    }

    _copyCacheRcj(cacheRcj){
        let copyRcj = null;
        if(ObjectUtils.isNotEmpty(cacheRcj)){
            copyRcj = ObjectUtils.cloneDeep(cacheRcj);
            if(ObjectUtils.isEmpty(copyRcj.standardId)){
                copyRcj.standardId = copyRcj.sequenceNbr;
            }
            copyRcj.sequenceNbr = Snowflake.nextId();
        }
        return copyRcj;
    }

    getCacheRcjAndCopy(unitProject, rcjCode,libraryCode){
        let cacheRcj = UnitRcjCacheUtil.getByCodeAndLibraryCode(unitProject, rcjCode,libraryCode);

        return this._copyCacheRcj(cacheRcj);

        // let copyRcj = null;
        // if(ObjectUtils.isNotEmpty(cacheRcj)){
        //     copyRcj = ObjectUtils.cloneDeep(cacheRcj);
        //     if(ObjectUtils.isEmpty(copyRcj.standardId)){
        //         copyRcj.standardId = copyRcj.sequenceNbr;
        //     }
        //     copyRcj.sequenceNbr = Snowflake.nextId();
        // }
        // return copyRcj;
    }

    /**
     * 获取RCJ(适用于父级人材机获取)，并处理standardId
     * @param unitProject
     * @param libraryCode
     * @param rcjCode
     * @param de
     * @return {Promise<BaseRcj2022|null|*>}
     */
    async getRCJ(unitProject, libraryCode, rcjCode, de) {
        let cacheRcj = this.getCacheRcjAndCopy(unitProject, rcjCode);
        if(ObjectUtils.isNotEmpty(cacheRcj)){
            return cacheRcj;
        }

        let isUnit2022 = PricingFileFindUtils.is22Unit(unitProject);
        let baseRcj = await this.getRcjByCodes(libraryCode, rcjCode, isUnit2022)
        if(ObjectUtils.isNotEmpty(baseRcj)){
            if(ObjectUtils.isEmpty(baseRcj.standardId)){
                baseRcj.standardId = baseRcj.sequenceNbr;
            }
            baseRcj.sequenceNbr = Snowflake.nextId();
        }

        // 此处注释是因为查询方法不应该更新人材机缓存
        //UnitRcjCacheUtil.add(unitProject, baseRcj, de.sequenceNbr)
        return baseRcj;
    }

    async getRCJNew(unitProject, libraryCode, rcjCode, de) {
        let cacheRcj = this.getCacheRcjAndCopy(unitProject, rcjCode,libraryCode);
        if(ObjectUtils.isNotEmpty(cacheRcj) && cacheRcj.libraryCode == libraryCode){
            return cacheRcj;
        }

        let isUnit2022 = PricingFileFindUtils.is22Unit(unitProject);
        let baseRcj = await this.getRcjByCodes(libraryCode, rcjCode, isUnit2022)
        if(ObjectUtils.isNotEmpty(baseRcj)){
            if(ObjectUtils.isEmpty(baseRcj.standardId)){
                baseRcj.standardId = baseRcj.sequenceNbr;
            }
            baseRcj.sequenceNbr = Snowflake.nextId();

            if(isUnit2022){
                let taxCalculationMethod = unitProject.projectTaxCalculation.taxCalculationMethod;
                //是否是简易计税
                let isSimple = taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code;
                if(isSimple){
                    baseRcj.dePrice = baseRcj.priceBaseJournalTax;
                }else{
                    baseRcj.dePrice = baseRcj.priceBaseJournal;
                    baseRcj.priceMarket = baseRcj.priceBaseJournal;
                    baseRcj.priceMarketTax = baseRcj.priceBaseJournalTax;
                }
            }
        }

        if(ObjectUtils.isNotEmpty(baseRcj)){
            let fiveElementsSameRcjs = UnitRcjCacheUtil.queryFiveElmentsSameRcjs(unitProject, baseRcj);
            fiveElementsSameRcjs = fiveElementsSameRcjs || [];
            let sameCodeRcjs = fiveElementsSameRcjs.filter(sr => this._getOriginalCode(sr.materialCode) == baseRcj.materialCode);
            if(ObjectUtils.isNotEmpty(sameCodeRcjs)){
                return this._copyCacheRcj(sameCodeRcjs[0]);
            }
        }

        return baseRcj;
    }

    _getOriginalCode(code) {
        let str = code;
        if (str.includes("#")) {
            str = str.substring(0, str.lastIndexOf("#"));
        }
        return str;
    }

    _standardConvertMathRu(num){
        if(num < 0){
            return Math.floor(num)
        }else{
            return Math.ceil(num)
        }
    }

    /**
     * 公式格式化：替换公式中RD(),RU(),MOD(),平方符号（^）,百分号；以及用户输入值V
     * @param mathStr
     * @param rule
     * @return {string|*}
     */
    mathFormat(mathStr, rule){
        if(ObjectUtils.isEmpty(mathStr)){
            return "";
        }
        //处理向上/向下取整、取余
        let tmpMath = mathStr.replace(/RU/g, "this._standardConvertMathRu")
            .replace(/RD/g, "Math.floor")
            .replace(/MOD\(([^)]+),\s*([^)]+)\)/g, "(($1)%($2))")
            .replace(/\^/g, "**")
            .replace(/%/g, "*0.01")
        ;

        if(/\bV\b/.test(tmpMath)){
            let defaultValue = isNaN(+rule.defaultValue) ? null : +rule.defaultValue;
            let inputValue = isNaN(+rule.selectedRule) ? null : +rule.selectedRule;
            tmpMath = tmpMath.replace(/\bV\b/g, inputValue == null ? defaultValue : inputValue);
        }

        return tmpMath;
    }

    /**
     * 人材机替换
     * @param fromRcj
     * @param toRcj
     * @param param
     * @return {Promise<*>}
     */
    async editRcj(fromRcj, toRcj, param) {
        const {constructId, singleId, unitId, unitProject, de} = param;
        // 为了适配rcjReplaceStrategy对替换目标人材机的处理
        if(ObjectUtils.isNotEmpty(toRcj.standardId)){
            toRcj.sequenceNbr = toRcj.standardId;
        }
        //保留消耗量
        toRcj.resQty = fromRcj.resQty;
        toRcj.initResQty = fromRcj.initResQty;
        //人材机替换
        let rcjReplaceStrategy = new RcjReplaceStrategy({
            constructId,
            singleId,
            unitId,
            projectObj: PricingFileFindUtils.getProjectObjById(constructId)
        });
        let toRcjSupplement = toRcj.isSupplement;
        let newRcj = await rcjReplaceStrategy.execute({
            selectLine: fromRcj,
            replaceLine: toRcj,
            de: de,
            conversionCoefficient: 1,
            conversionFlag: true
        });

        if(toRcjSupplement == 1 && ObjectUtils.isNotEmpty(toRcj.childrenRcjCodeList)){
            let childrenOneRcj = UnitRcjCacheUtil.queryChildrenRcj(unitProject,toRcj);
            if(ObjectUtils.isNotEmpty(childrenOneRcj)) {
                for (let item of childrenOneRcj) {
                    let newRcj1 = ObjectUtils.cloneDeep(item);
                    newRcj1.sequenceNbr = Snowflake.nextId();
                    newRcj1.rcjId = newRcj.sequenceNbr;
                    newRcj1.deId = de.sequenceNbr;

                    // 处理临时删除标记
                    if (newRcj.tempDeleteFlag) {
                        newRcj1.tempDeleteFlag = true;
                        newRcj1.tempDeleteBackupResQty = item.resQty;
                        newRcj1.resQty = null;
                    }
                    unitProject.rcjDetailList.push(newRcj1);
                }
            }
        }

        const {sequenceNbr: id} = newRcj;
        let resultRcj = unitProject.constructProjectRcjs.find((v) => v.sequenceNbr == id);
        // 如果手动修改过消耗量，需要保留手动修改值
        if (fromRcj.hasOwnProperty("consumerResQty")) {
            resultRcj.consumerResQty = fromRcj.consumerResQty;
        }
        return resultRcj;
    }

    /**
     * 根据定额册code，材料code查询材料
     * @param libraryCode
     * @param materialCode
     * @return {Promise<BaseRcj2022|null>}
     */
    async getRcjByCodes(libraryCode, materialCode, isUnit2022){
        return await this.app.appDataSource
            .getRepository(isUnit2022 ? BaseRcj2022 : BaseRcj)
            .findOneBy({
                libraryCode: libraryCode,
                materialCode: materialCode,
            });
    }

    /**
     * 计算公式中的算式：
     *     R*(3-1*2)  => R*1
     *     *3  => *3
     *     +3*3 - (4/2)  => +7
     * @param numStr
     * @return {string}
     */
    mathAfterCalculation(numStr){
        let firstChar = numStr.charAt(0);
        let prefix = "";
        if(/[RCJ][-+*/]/.test(numStr)){
            prefix = numStr.substring(0,2);
            numStr = numStr.substring(2);
        }else if("-+*/".includes(firstChar)) {
            numStr = numStr.substring(1);
            prefix = firstChar;
        }
        let value = NumberUtil.numberScale(eval(numStr), 6);
        if(value < 0){
            return `${prefix}(${value})`;
        }else{
            return prefix + value;
        }
    }

    async deInitialRcjs(unitProject, de) {
        let deParentRcjs = [];

        // 临时删除材料缓存
        let tempDeleteFlagMap = new Map();
        let otherDeRcjs = [];
        let childrenRcjs = [];

        // 人材机处理
        for(let r of unitProject.constructProjectRcjs){
            // 临时删除材料处理
            if(r.deId == de.sequenceNbr && r.tempDeleteFlag == true){
                tempDeleteFlagMap.set(r.materialCode, true);
            }
            // 缓存非本定额的人材机
            if(r.deId != de.sequenceNbr){
                otherDeRcjs.push(r);
            }
        }

        // 子级人材机处理，删除本定额的人材机
        unitProject.rcjDetailList = unitProject.rcjDetailList || [];
        unitProject.rcjDetailList = unitProject.rcjDetailList.filter(detail => detail.deId != de.sequenceNbr)

        let isUnit2022 = PricingFileFindUtils.is22Unit(unitProject);
        let deRcjRelationList = null;
        if (isUnit2022) {
            deRcjRelationList = await this.service.yuSuanProject.baseDeRcjRelationService.getDeRcjRelationByDeId2022(de.standardId);
        } else {
            deRcjRelationList = await this.service.yuSuanProject.baseDeRcjRelationService.getDeRcjRelationByDeId(de.standardId);
        }

        for (let i = 0; i < deRcjRelationList.length; i++) {
            let code = deRcjRelationList[i].materialCode;

            // let rcj = this.getCacheRcjAndCopy(unitProject,code);
            let rcj = await this.getRCJNew(unitProject,deRcjRelationList[i].libraryCode,code);
            if(rcj){
                rcj.deId = de.sequenceNbr;
                rcj.initResQty = deRcjRelationList[i].resQty;
                rcj.resQty = deRcjRelationList[i].resQty;
                // 处理临时删除标记
                if(tempDeleteFlagMap.has(rcj.materialCode)){
                    rcj.tempDeleteFlag = true;
                    rcj.tempDeleteBackupResQty = rcj.resQty;
                    rcj.resQty = null;
                }
                deParentRcjs.push(rcj);

                // 配比人材机处理
                if(rcj.levelMark != RcjLevelMarkConstant.NO_SINK){
                    let childrenOneRcj = UnitRcjCacheUtil.queryChildrenRcj(unitProject,rcj);
                    if(ObjectUtils.isNotEmpty(childrenOneRcj)){
                        for(let item of childrenOneRcj){
                            let newRcj = ObjectUtils.cloneDeep(item);
                            newRcj.sequenceNbr = Snowflake.nextId();
                            newRcj.rcjId = rcj.sequenceNbr;
                            newRcj.deId = de.sequenceNbr;

                            // 处理临时删除标记
                            if(rcj.tempDeleteFlag){
                                newRcj.tempDeleteFlag = true;
                                newRcj.tempDeleteBackupResQty = item.resQty;
                                newRcj.resQty = null;
                            }
                            childrenRcjs.push(newRcj);
                        }

                    }else{
                        // 考虑历史版本，缓存中可能没有父子级关系
                        childrenOneRcj = await this._childrenRcjReset(unitProject,rcj, de);
                        childrenRcjs.push(...childrenOneRcj);
                    }

                }
            }
        }

        otherDeRcjs.push(...deParentRcjs);
        unitProject.constructProjectRcjs = otherDeRcjs;
        unitProject.rcjDetailList.push(...childrenRcjs);

        return deParentRcjs;
    }

    async _childrenRcjReset(unitProject, parentRcj, de){
        let childrenRcj = [];
        let resultRcjs = [];
        if (parentRcj.levelMark == RcjLevelMarkConstant.SINK_PB) {
            childrenRcj = await this.service.yuSuanProject.baseClpbService.getClpbByClId(parentRcj.standardId);
        }
        if (parentRcj.levelMark == RcjLevelMarkConstant.SINK_JX) {
            childrenRcj = await this.service.yuSuanProject.baseJxpbService.getJxpbByClId(parentRcj.standardId);
        }
        //初始化父级的主键
        for(let item of childrenRcj){
            // let newRcj = this.getCacheRcjAndCopy(unitProject,item.materialCode) || ObjectUtils.cloneDeep(item);

            let newRcj = await this.getRCJNew(unitProject,item.libraryCode,item.materialCode) || ObjectUtils.cloneDeep(item);

            newRcj.standardId = item.sequenceNbr;
            newRcj.sequenceNbr = Snowflake.nextId();
            newRcj.rcjId = parentRcj.sequenceNbr;
            newRcj.deId = de.sequenceNbr;
            newRcj.initResQty = item.resQty;
            newRcj.resQty = item.resQty;

            // 处理临时删除标记
            if(parentRcj.tempDeleteFlag){
                newRcj.tempDeleteFlag = true;
                newRcj.tempDeleteBackupResQty = item.resQty;
                newRcj.resQty = null;
            }

            resultRcjs.push(newRcj);
        }

        return resultRcjs;
    }

}
module.exports = ConversionService
