/*
 * @Author: wangru
 * @Date: 2023-05-23 11:33:04
 * @LastEditors: wangru
 * @LastEditTime: 2023-05-25 14:14:18
 */
"use strict"

const { Service, Log } = require("../../../core")
const { getRepository } = require("typeorm")
const { BaseDeLibrary } = require("../model/BaseDeLibrary")
const { BaseFeeFileRelation, BaseFeeFileRelation2022} = require("../model/BaseFeeFileRelation")
const {BaseDeCslbRelation} = require("../model/BaseDeCslbRelation");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {BaseFeeFileProject, BaseFeeFileProject2022} = require("../model/BaseFeeFileProject");
const {BaseCSLB2022, BaseCSLB} = require("../model/BaseCSLB");

/**
 * 取费文件关联关系表 service
 * @class
 */
class BaseFeeFileRelationService extends Service {
	constructor(ctx) {
		super(ctx)
	}


	async getFeeFileProject(unitProjectName,type) {
		let result = await this.app.appDataSource
			.getRepository(type)
			.find({
				where: {
					unitProjectName: unitProjectName,
				},
			})
		const map = new Map()
		const dedupedArr = result.reduce((acc, cur) => {
			if (!map.has(cur.qfCode)) {
				map.set(cur.qfCode, true)
				acc.push(cur)
			}
			return acc
		}, [])
		return dedupedArr
	}




	/**
	 * 根据工程专业查询取费文件关系
	 * @param unitProjectName 工程专业名称
	 * @returns {Promise<[]|Error>}
	 */
	async getFeeFileRelation(unitProjectName) {
		let result = await this.app.appDataSource
			.getRepository(BaseFeeFileRelation)
			.find({
				where: {
					unitProjectName: unitProjectName,
				},
			})
		const map = new Map()
		const dedupedArr = result.reduce((acc, cur) => {
			if (!map.has(cur.qfCode)) {
				map.set(cur.qfCode, true)
				acc.push(cur)
			}
			return acc
		}, [])
		return dedupedArr
	}


	/**
	 * 由于表结构变化,替换原来getFeeFileRelationByQfCode方法.
	 * @param qfCode
	 * @param unitIs2022
	 * @returns {Promise<BaseFeeFileProject2022>}
	 */
	async getFeeFileProjectByQfCode(qfCode,unitIs2022) {
		let result = await this.app.appDataSource
			.getRepository(unitIs2022?BaseFeeFileProject2022:BaseFeeFileProject)
			.findOne({
				where: {

					qfCode: qfCode
				},
			})
		return result;
	}

	/**
	 * 根据qfCode查询取费文件关系
	 * @param unitProjectName 工程专业名称
	 * @returns {Promise<[]|Error>}
	 */
	async getFeeFileRelationByQfCode(qfCode) {
		let result = await this.app.appDataSource
			.getRepository(BaseFeeFileRelation)
			.find({
				where: {
					qfCode: qfCode
				},
			})
		const map = new Map()
		const dedupedArr = result.reduce((acc, cur) => {
			if (!map.has(cur.qfCode)) {
				map.set(cur.qfCode, true)
				acc.push(cur)
			}
			return acc
		}, [])
		return dedupedArr[0]
	}

	//根据定额册编码查询取费文件关系
	async getFeeFilesByLibraryCode(libraryCode,constructIs22) {
		let result = await this.app.appDataSource
			.getRepository(constructIs22?BaseFeeFileProject2022:BaseFeeFileProject)
			.find({
				where: {
					libraryCode: libraryCode
				},
			})
		return result;
	}


	async queryCslbByQfCode(cslbCode, unitIs2022 = false) {
		let result = await this.app.appDataSource
			.getRepository(unitIs2022 ? BaseCSLB2022 : BaseCSLB)
			.findOne({
				where: {
					cslbCode: cslbCode
				},
			})

		return result;
	}


	async queryFeeFileRelationByRateCodeGroupByQfCode(cslbCode,unitIs2022=false) {
		let result = await this.app.appDataSource
			.getRepository(unitIs2022?BaseCSLB2022:BaseCSLB)
			.findOne({
				where: {
					cslbCode: cslbCode
				},
			})

		return result;
	}


	// 查全量
	async list(unitIs2022=true){
		let result = await this.app.appDataSource
			.getRepository(unitIs2022?BaseCSLB2022:BaseCSLB)
			.find()
		return result;
	}
}

BaseFeeFileRelationService.toString = () => "[class BaseFeeFileRelationService]"
module.exports = BaseFeeFileRelationService
