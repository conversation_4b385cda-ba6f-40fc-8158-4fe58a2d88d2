var {cLexer: lexer} = require('chain-lexer');
const {Snowflake} = require("../../utils/Snowflake");


class AnalyzeCore {
    static renderParams(caculateBase) {
        let patamsSet = new Set();
        lexer.start(caculateBase);
        let parsedTokens = lexer.DFA.result.tokens;
        parsedTokens.forEach((token) => {
            if (token.type == "Identifier") {
                patamsSet.add(token.value);
            }
        });
        return Array.from(patamsSet);
    }

    static renderFunction(params, caculateBase) {
        if (caculateBase.includes("return")) {
            return new Function("{" + params.join(",") + "}", caculateBase);
        }
        return new Function("{" + params.join(",") + "}", `return ${caculateBase}`);

    }
}

class UPCTemplateVO {
    constructor({
                    sequenceNbr,
                    qfCode,
                    standard,
                    sort,
                    isLock,
                    type,
                    typeCode,
                    code,
                    name,
                    caculateBase,
                    desc,
                    rate
                }) {
        this.sequenceNbr = sequenceNbr;
        this.qfCode = qfCode;
        this.standard = standard;
        this.sort = sort;
        this.type = type;
        this.typeCode = typeCode;
        this.code = code;
        this.name = name;
        this.caculateBase = caculateBase;
        this.desc = desc;
        this.rate = rate;
        this.isLock = isLock;
        this.params = [];
        this.fn = null;
    }

}
module.exports = {AnalyzeCore, UPCTemplateVO}
