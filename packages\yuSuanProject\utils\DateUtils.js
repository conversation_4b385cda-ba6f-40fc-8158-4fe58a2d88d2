const moment = require("moment");
const {ObjectUtils} = require("./ObjectUtils");


/**
 * 日期操作工具类
 */
class DateUtils{


    /**
     * 获取当前日期
     * @param obj
     * @return {false|val is Object}
     * @private
     */
    now(format){
        const now = moment();
        return now.format(format);
    }

    format(date){
        return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }

    formatNumberString(date){
        return moment(date).format('YYYYMMDDHHmmss')
    }

     test(arg,array) {
        let children = arg.children;
        if (ObjectUtils.is_Undefined(children) || ObjectUtils.isNull(children)) {
            return;
        }
        for (let i in children) {
            let child = children[i];
            array.push(child);
            this.test(child,array);
        }
    }




}

module.exports = {
    DateUtils: new DateUtils()
};

