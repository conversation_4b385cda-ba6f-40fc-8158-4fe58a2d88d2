'use strict';

const { Service, Log } = require('../../../core');
const { BaseDeAwfRelation2022, BaseDeAwfRelation } = require('../model/BaseDeAwfRelation');

/**
 * 安文费 service
 * @class
 */
class BaseDeAwfRelationService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  async getBaseDeAwfRelation(id, constructIs2022) {
    //安文费费用定额
    return await this.app.appDataSource.getRepository(constructIs2022 ? BaseDeAwfRelation2022 : BaseDeAwfRelation).findOne({
      where: { sequenceNbr: id }
    });
  }

}

BaseDeAwfRelationService.toString = () => '[class BaseDeAwfRelationService]';
module.exports = BaseDeAwfRelationService;
