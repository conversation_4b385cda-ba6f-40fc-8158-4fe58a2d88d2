const {PricingFileFindUtils} = require("./PricingFileFindUtils");
const {ObjectUtils} = require("./ObjectUtils");

class ConstructOperationUtil{

    updateConstructName(constructObj, newName){
        constructObj.constructName = newName;
        let cNameObj = constructObj.constructProjectJBXX.find(k =>k.name === "工程名称");
        cNameObj.remark = newName;
    }

    updateUnitName(unit, newName){
        unit.upName = newName;
        let unitJBXXProjectOverview = unit.unitJBXX;
        let upNameJBXX = unitJBXXProjectOverview.find((item) => item.addFlag === 0 && item.name === '单位工程名称');
        upNameJBXX.remark = newName;
    }

    updateUnitMajorType(unit, newMajorType){
        unit.constructMajorType = newMajorType;
        let unitJBXXProjectOverview = unit.unitJBXX;
        let constructMajorTypeJBXX = unitJBXXProjectOverview.find((item) => item.addFlag === 0 && item.name === '工程专业');
        constructMajorTypeJBXX.remark = newMajorType;
    }

    _flatSingleTreeToMap(singleProject){
        let treeMap = new Map();
        singleProject.levelType = 2;
        treeMap.set(singleProject.sequenceNbr, singleProject);

        // 子单项处理
        let subSingleProjects = singleProject.subSingleProjects;
        if(!ObjectUtils.isEmpty(subSingleProjects)) {
            for (let i in subSingleProjects){
                subSingleProjects[i].parentId = singleProject.sequenceNbr;
                let subTreeMap = this._flatSingleTreeToMap(subSingleProjects[i]);

                for (let [key, value] of subTreeMap.entries()) {
                    treeMap.set(key, value);
                }
            }
        }else{
            //单位
            let unitProjects = singleProject.unitProjects;
            for (let i in unitProjects) {
                let unitProject = unitProjects[i];
                unitProject.levelType = 3;
                treeMap.set(unitProject.sequenceNbr, unitProject);
            }
        }

        return treeMap;
    }

    /**
     * 平铺工程项目，生成map，每一级对象在map中key为其自身的sequenceNbr
     * @param constructId
     * @return {Map<any, any>}
     */
    flatConstructTreeToMapById(constructId){
        let constructObj = PricingFileFindUtils.getProjectObjById(constructId);
        return this.flatConstructTreeToMapByObj(constructObj);
    }

    /**
     * 平铺工程项目，生成map，每一级对象在map中key为其自身的sequenceNbr
     * @param constructId
     * @return {Map<any, any>}
     */
    flatConstructTreeToMapByObj(constructObj){
        let treeMap = new Map();
        constructObj.levelType = 1;
        treeMap.set(constructObj.sequenceNbr, constructObj);


        let singleProjects = constructObj.singleProjects;
        if (ObjectUtils.isEmpty(singleProjects)) {
            if(ObjectUtils.isNotEmpty(constructObj.unitProjectArray)) {
                let unitProjectArray = constructObj.unitProjectArray;
                for (let i in unitProjectArray) {
                    let unitProject = unitProjectArray[i];
                    unitProject.levelType = 3;
                    treeMap.set(unitProject.sequenceNbr, unitProject);
                }
            }
            if(ObjectUtils.isNotEmpty(constructObj.unitProject)){
                constructObj.unitProject.levelType = 3;
                treeMap.set(constructObj.unitProject.sequenceNbr, constructObj.unitProject);
            }
        }else{
            for (let i in singleProjects) {
                singleProjects[i].parentId = constructObj.sequenceNbr;
                let singleTreeMap = this._flatSingleTreeToMap(singleProjects[i]);
                for (let [key, value] of singleTreeMap.entries()) {
                    treeMap.set(key, value);
                }
            }
        }

        return treeMap;
    }

}

module.exports = {
    ConstructOperationUtil: new ConstructOperationUtil()
};