'use strict';

const { Service, Log } = require('../../../core');
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const { ObjectUtils } = require('../utils/ObjectUtils');
const { ArrayUtil } = require('../utils/ArrayUtil');
const { In } = require('typeorm');
const { BaseDe, BaseDe2022 } = require('../model/BaseDe');
const LibraryCodeConstant = require('../enum/LibraryCodeConstant');
const BranchProjectLevelConstant = require('../enum/BranchProjectLevelConstant');
const ConstructionMeasureTypeConstant = require('../enum/ConstructionMeasureTypeConstant');
const { BaseDeAwfRelation, BaseDeAwfRelation2022 } = require('../model/BaseDeAwfRelation');
const { BaseList } = require('../model/BaseList');
const StepItemCostLevelConstant = require('../enum/StepItemCostLevelConstant');
const { BaseDeGtfMajorRelation } = require('../model/BaseDeGtfMajorRelation');
const RemoveStrategy = require('../main_editor/remove/removeStrategy');
const { ItemBillProject } = require('../model/ItemBillProject');
const DePropertyTypeConstant = require('../enum/DePropertyTypeConstant');
const GroundTypeConstant = require('../enum/GroundTypeConstant');
const { NumberUtil } = require('../utils/NumberUtil');
const { ConvertUtil } = require('../utils/ConvertUtils');
const { Snowflake } = require('../utils/Snowflake');
const RcjLevelMarkConstant = require('../enum/RcjLevelMarkConstant');
const ConstantUtil = require('../enum/ConstantUtil');
const { BaseDeGtfMajorRelation2022 } = require('../model/BaseDeGtfMajorRelation');
const { BaseDeZsCgRelation, BaseDeZsCgRelation2022 } = require('../model/BaseDeZsCgRelation');
const { BaseCSLB, BaseCSLB2022 } = require('../model/BaseCSLB');

const {
  app: electronApp,
  dialog, shell, BrowserView, Notification,
  powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const TaxCalculationMethodEnum = require('../enum/TaxCalculationMethodEnum');
const UnitPriceConstant = require('../enum/UnitPriceConstant');
const { CostUtils } = require('../utils/CostUtils');
const CalculateBaseType = require('../enum/CalculateBaseType');
const InsertStrategy = require('../main_editor/insert/insertStrategy');
const ProjectLevelConstant = require('../enum/ProjectLevelConstant');

/**
 * @class
 */
class ConstructCostMathService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 高台增加费
   * @return {Promise<*>}
   */
  async gtfResource() {
    let baseDeGtfMajorList = await this.app.appDataSource.getRepository(BaseDeGtfMajorRelation).find();
    return baseDeGtfMajorList;
  }


  /**
   * 安文费明细数据
   * @param arg
   */
  awfDetails(arg) {
    let { constructId, singleId, unitId, deId } = arg;
    //获取当前单位项目
    let { measureProjectTables } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    return measureProjectTables.find(k => k.sequenceNbr === deId).awfDetail;
  }


  dataFiltrate(data) {
    let result = {};
    let awfList = [];
    let zjcsList = [];
    if (ObjectUtils.isEmpty(data)) {
      return result;
    }
    for (const item of data) {
      if (!ObjectUtils.isEmpty(item.sequenceNbr) && item.isCheck != 0) {
        //清单编码存在  并且 勾选记取
        if (item.zjcsClassCode === '0') {
          awfList.push(item);
        } else {
          zjcsList.push(item);
        }
      }
    }
    result.awf = awfList;
    result.zjcs = zjcsList;
    return result;
  }

  /**
   * 当定额fxCode，bdCode有一个不为空时，认为定额不为空
   * @param deList
   * @returns {*}
   * @private
   */
  _filterEmptyDe(deList) {
    return deList.filter(d => ObjectUtils.isNotEmpty(d.fxCode) || ObjectUtils.isNotEmpty(d.bdCode));
  }


  /**
   * 获取其他总价措施的费用定额,排除不符合条件的定额
   */
  async getCostDeByQdzjcs(zjcsClassCode, baseDeMap, increaseFeeHeight, qd, unit, is22De, csfyCalculateBaseArea) {
    //获取所有的施工组织措施类别
    const cslist = Array.from(baseDeMap.keys());
    //获取措施项目费用定额
    //总价措施费用定额
    //如果是22定额标准 则从22表查
    let baseCSLBDTOS = await this.app.appDataSource.getRepository(is22De ? BaseCSLB2022 : BaseCSLB).find({
      where: { cslbName: In(cslist) }
    });

    let cslbCodeList = baseCSLBDTOS.map(k => k.cslbCode);

    //如果是22定额标准 则从 22 表查
    let baseDeDTOS = await this.app.appDataSource.getRepository(is22De ? BaseDe2022 : BaseDe).find({
      where: { cslbCode: In(cslbCodeList), isZj: 1 },
      order: { sortNo: 'ASC' }
    });

    //走到这里就是高台增加费
    if (zjcsClassCode.zjcsClassCode === '16') {
      if (!ObjectUtils.isEmpty(increaseFeeHeight)) {
        for (const cs of cslist) {
          //获取高台增加费定额
          let baseDeDTO = await this.queryBaseDe(cs, increaseFeeHeight, is22De);
          if (!ObjectUtils.isEmpty(baseDeDTO)) {
            for (let i = baseDeDTOS.length - 1; i >= 0; i--) {
              let element = baseDeDTOS[i];
              if (element.zjcsClassCode === baseDeDTO.zjcsClassCode && element.deCode !== baseDeDTO.deCode) {
                baseDeDTOS.splice(i, 1); // 删除元素
              }
            }
          } else {
            baseDeDTOS = [];
          }
        }
      }
    }

    let deList = [];
    deList = baseDeDTOS.filter(k => (k.zjcsClassCode === zjcsClassCode.zjcsClassCode));
    //计取地区筛选
    if (ObjectUtils.isNotEmpty(csfyCalculateBaseArea) && csfyCalculateBaseArea === CalculateBaseType.calculateBaseAreaZhangCheng) {
      deList = deList.filter(o => !o.deName.includes(CalculateBaseType.calculateBaseAreaOtherName));
    } else {
      deList = deList.filter(o => !o.deName.includes(CalculateBaseType.calculateBaseAreaZhangChengName));
    }

    if (!is22De && zjcsClassCode.zjcsClassCode === '16' && ObjectUtils.isNotEmpty(deList)) {
      //12定额高台增加费筛选
      let name = this.switchGtzjfName2(zjcsClassCode.qdName);
      deList = deList.filter(p => p.deName.includes(name));
    }

    return this.deDataConvert(deList, qd, unit);
  }


  /**
   * 定额数据处理
   * @param deList
   * @param qd
   */
  async deDataConvert(deList, qd, unit) {
    let array = [];
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    let { constructId, spId, sequenceNbr } = unit;
    for (const de of deList) {
      let id = Snowflake.nextId();
      let measureProjectDTO = {};
      measureProjectDTO.sequenceNbr = id;
      measureProjectDTO.fxCode = de.deCode;
      measureProjectDTO.bdCode = de.deCode;
      measureProjectDTO.name = de.deName;
      measureProjectDTO.quantityExpression = '1';
      measureProjectDTO.quantityExpressionNbr = 1;
      measureProjectDTO.kind = BranchProjectLevelConstant.de;
      measureProjectDTO.quantity = 1;
      measureProjectDTO.isCostDe = DePropertyTypeConstant.ZJCS_DE;
      measureProjectDTO.isStandard = DePropertyTypeConstant.STANDARD;
      measureProjectDTO.unitId = sequenceNbr;
      measureProjectDTO.constructId = constructId;
      measureProjectDTO.standardId = de.sequenceNbr;
      measureProjectDTO.libraryCode = de.libraryCode;
      measureProjectDTO.zjcsClassCode = de.zjcsClassCode;
      measureProjectDTO.unit = de.unit;
      measureProjectDTO.rateName = de.rateName;
      measureProjectDTO.libraryCode = de.libraryCode;
      //添加取费文件， 获取取费文件数据
      let unitFeeFile = await this.service.yuSuanProject.baseFeeFileService.handleUnitAddFeeFile(constructId, spId, sequenceNbr, de.sequenceNbr, is22Unit);
      measureProjectDTO.costMajorName = unitFeeFile.feeFileName;
      measureProjectDTO.costFileCode = unitFeeFile.feeFileCode;

      array.push(measureProjectDTO);
    }
    return array;

  }

  async awfCostMath(arg) {
    let {
      constructId,
      singleId,
      unitId,
      data,
      unitIdList,
      increaseFeeHeight,
      heatingFee,
      rainySeasonConstruction,
      csfyCalculateBaseCode,
      csfyCalculateBaseArea
    } = arg;
    console.time('总价措施记取 总耗时');
    let changeDeIds = new Set();
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    unit.zjcsCostMathCache = arg;
    //过滤掉不计取的
    data = data.filter(k => k.isCheck === 1);
    //将前端传递的数据处理分别返回安文费集合总价措施清单集合
    let { awf, zjcs } = this.dataFiltrate(data);
    if (ObjectUtils.isEmpty(awf) && ObjectUtils.isEmpty(zjcs)) {
      return;
    }
    // 判断是否22定额标准
    let is22De = PricingFileFindUtils.is22Unit(unit);

    console.time('总价措施费用定额记取耗时');
    //开始循环前端选择的单位集合
    for (const uId of unitIdList) {
      //1.根据单位ID获取到单位数据，并且删除单位中已经记取了的安文费定额和总价措施定额
      let unit = await this.getUnitAndClearCostDe(constructId, uId, changeDeIds);
      //获取计税方式 '1'?'一般计税':'简易计税'
      let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
      //2.开始计算记取流程,先记取总价措施费用
      if (ObjectUtils.isNotEmpty(zjcs)) {
        //2.1 获取总价措施需要的基数定额数据
        let otherZJCSUseDe = this.getOtherZJCSUseDe(unit);
        if (ObjectUtils.isNotEmpty(otherZJCSUseDe)) {
          //2.2 处理基数定额并且根据基数定额施工组织措施类别对于基数定额进行分组（处理随主工程的情况）
          //主取费文件
          let mainFeeFile = PricingFileFindUtils.getMainFeeFileByUnitObj(unit);
          let baseDeMap = await this.baseDeGroup(otherZJCSUseDe, unit, mainFeeFile);

          //循环所选择记取总价措施清单集合
          for (const zjcsQd of zjcs) {
            //2.3  首先确认清单数据，如果该清单不存在则需要新增
            let qd = this.confirmQdData(zjcsQd, unit, ConstructionMeasureTypeConstant.ZJCS);
            if (ObjectUtils.isEmpty(qd)) {
              continue;
            }
            //2.4 计算--基数定额计算基数
            let costBase = this.costBase(baseDeMap, unit, arg);
            //2.5 确定该清单所需要的费用定额是那些 ,排除不符合条件的定额
            let costDeList = await this.getCostDeByQdzjcs(zjcsQd, baseDeMap, increaseFeeHeight, qd, unit, is22De, csfyCalculateBaseArea);
            //2.6 清单下挂定额
            for (const costDe of costDeList) {
              //获取计算基数
              let deMathBase = costBase[costDe.rateName];

              // 判断是否计算基数乘以0.5
              //雨季施工增加费   冬季施工增加费
              if (heatingFee && costDe.zjcsClassCode === '1') {
                deMathBase = NumberUtil.multiply(deMathBase, 0.5);
              }
              if (rainySeasonConstruction && costDe.zjcsClassCode === '2') {
                deMathBase = NumberUtil.multiply(deMathBase, 0.5);
              }
              //赋值计算基数
              costDe.formula = deMathBase;
              costDe.caculatePrice = 1;
              costDe.baseNum = { def: deMathBase };
              //给清单下挂定额以及下挂人材机数据
              let {
                de,
                titleData
              } = await this.confirmQdAddCostDe(unit, qd, costDe, ConstructionMeasureTypeConstant.ZJCS, deMathBase);

              // 检测是否需要措施中人工费调整
              await this.service.yuSuanProject.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(unit.constructId, unit.spId, unit.sequenceNbr, de);
              //计算单价构成
              this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(unit.constructId, unit.spId, unit.sequenceNbr,
                de.sequenceNbr, true, titleData, false);
            }
          }
        }
      }
      console.timeEnd('总价措施费用定额记取耗时');

      console.time('安文费 费用定额记取耗时');
      //3.计算安文费
      if (ObjectUtils.isNotEmpty(awf)) {
        //获取到安文费需要的基数定额数据
        let awfBaseDeList = this.getAwfUseDe(unit);
        //将基数定额根据取费文件分组
        let baseDeGroupByCostMajorName = ArrayUtil.group(awfBaseDeList, 'costMajorName');
        const szgcValue = baseDeGroupByCostMajorName.get(ConstantUtil.TITLE_WITH_MARJOR_PROJECT);
        if (ObjectUtils.isNotEmpty(szgcValue)) {
          // 如果根据costMajorName分组后存在“随主工程”  就需要把随主工程的这一组转为单位工程的主取费文件
          let mainFeeFile = PricingFileFindUtils.getMainFeeFileByUnitObj(unit);
          baseDeGroupByCostMajorName.set(mainFeeFile.feeFileName, szgcValue);
        }
        //确定安文费的费用定额
        let awfCostList = await this.getAwfCostDe(unit);
        if (ObjectUtils.isEmpty(awfCostList)) {
          return;
        }

        //循环前端安文费清单
        for (const awfQd of awf) {
          //确定安文费清单
          let qd = this.confirmQdData(awfQd, unit, ConstructionMeasureTypeConstant.AWF);
          if (ObjectUtils.isEmpty(qd)) {
            continue;
          }
          await this.hanldeAddAwfCostDe(awfCostList, baseDeGroupByCostMajorName, unit, qd);
        }
      }
    }
    console.timeEnd('安文费 费用定额记取耗时');

    await this.service.yuSuanProject.autoCostMathService.autoCostMath({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId,
      countCostCodeFlag: true,
      changeDeIdArr: changeDeIds
    });
    console.timeEnd('总价措施记取 总耗时');
  }

  async hanldeAddAwfCostDe(awfCostList, baseDeGroupByCostMajorName, unit, qd) {
    //循环费用定额
    for (const costDe of awfCostList) {
      //根据费用定额的qfName，获取到基数定额有哪些
      let baseDeList = baseDeGroupByCostMajorName.get(costDe.qfName);
      //计算基数定额的计算基数
      let { costBase, awfRate } = this.mathDeBaseCost(baseDeList, unit, costDe);
      //封装费用定额数据
      let addDe = await this.confirmAwfDeData(costDe, costBase, awfRate, unit);
      //给清单下挂定额以及下挂人材机数据
      let { de, titleData } = await this.confirmQdAddCostDe(unit, qd, addDe, ConstructionMeasureTypeConstant.AWF);
      // 检测是否需要措施中人工费调整
      await this.service.yuSuanProject.cszRgfAdjustProcess.checkDeRgfAdjustAndAddIfNecessary(unit.constructId, unit.spId, unit.sequenceNbr, de);
      //计算单价构成
      this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(unit.constructId, unit.spId, unit.sequenceNbr,
        de.sequenceNbr, true, titleData, false);
    }
  }

  /**
   * 确定安文费费用定额数据
   */
  async confirmAwfDeData(costDe, costBase, awfRate, unit) {
    let { constructId, spId, sequenceNbr } = unit;
    let measureProjectDTO = {};
    measureProjectDTO.fxCode = costDe.deCode;
    measureProjectDTO.name = costDe.deName;
    measureProjectDTO.quantityExpression = '1';
    measureProjectDTO.kind = BranchProjectLevelConstant.de;
    measureProjectDTO.quantity = 1;
    measureProjectDTO.price = NumberUtil.numberScale(NumberUtil.multiplyParams(costBase, NumberUtil.divide100(awfRate)), 2);
    measureProjectDTO.total = NumberUtil.numberScale(NumberUtil.multiplyParams(costBase, NumberUtil.divide100(awfRate)), 2);
    measureProjectDTO.zjfPrice = NumberUtil.numberScale(NumberUtil.multiplyParams(costBase, NumberUtil.divide100(awfRate)), 2);
    measureProjectDTO.zjfTotal = NumberUtil.numberScale(NumberUtil.multiplyParams(costBase, NumberUtil.divide100(awfRate)), 2);
    //安文费计算基数
    measureProjectDTO.formula = costBase;
    // 不清楚为啥计算基数会产生两个字段一个叫 formula 一个叫 baseNum  目前为了兼容  先给两个字段都给值  后续把formula这个字段弃用掉
    measureProjectDTO.baseNum = costBase;
    measureProjectDTO.isCostDe = DePropertyTypeConstant.AWF_DE;
    measureProjectDTO.unitId = sequenceNbr;
    measureProjectDTO.constructId = constructId;
    measureProjectDTO.standardId = costDe.sequenceNbr;
    measureProjectDTO.libraryCode = costDe.libraryCode;
    measureProjectDTO.isStandard = 1;
    // 安文费定额的zjcsClassCode为0  其他总价措施的费用定额的zjcsClassCode不是0
    // 这里还有一个问题  此处给0后  在添加定额的时候 zjcsClassCode取值规则处会存在覆盖值的可能性  所以在de.js中对zjcsClassCode取值时  安文费的默认给了“0”
    measureProjectDTO.zjcsClassCode = '0';
    measureProjectDTO.unit = '项';
    //添加取费文件， 获取取费文件数据
    let unitFeeFile = await this.service.yuSuanProject.baseFeeFileService.updateFeeFile(unit, costDe.qfCode);
    measureProjectDTO.costMajorName = unitFeeFile.feeFileName;
    measureProjectDTO.costFileCode = unitFeeFile.feeFileCode;
    // 添加安文费明细
    // 判断当前取费文件类型是否与主取费文件类型一直   如果一致将其他费用记取到该定额下
    let str = '分部分项+措施项目+规费';
    //主取费文件
    let mainFeeFile = PricingFileFindUtils.getMainFeeFile(constructId, spId, sequenceNbr);
    if (costDe.qfName === mainFeeFile.feeFileName) {
      str = '分部分项+措施项目+其他项目+规费';
    }
    let awfDetail = {
      no: 1,//序号
      awfMajor: costDe.qfName,
      caculateBase: str,
      anwenRateBase: unitFeeFile.anwenRateBase,
      anwenRateAdd: unitFeeFile.anwenRateAdd
    };
    measureProjectDTO.awfDetail = awfDetail;
    return measureProjectDTO;
  }


  /**
   * 清单下挂定额,下挂人材机数据
   */
  async confirmQdAddCostDe(unit, qd, costDe, type, deMathBase) {
    if (ObjectUtils.isEmpty(qd)) {
      return;
    }

    let { constructId, spId, sequenceNbr } = unit;
    //下挂费用定额
    let insertStrategy = new InsertStrategy({
      constructId,
      singleId: spId,
      unitId: sequenceNbr,
      pageType: 'csxm'
    });
    let newCostDe = {};
    if (type === ConstructionMeasureTypeConstant.AWF) {
      newCostDe = await insertStrategy.execute({
        pointLine: qd,
        newLine: costDe,
        indexId: costDe.standardId,
        libraryCode: costDe.libraryCode,
        option: 'insert',
        skip: { rcj: true, quantity: true, jiqu: true, huizong: true },
        overwriteColumn: false
      });
    } else {
      // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
      this.service.yuSuanProject.itemBillProjectOptionService.cleanQdDelTempStatus({
        constructId: constructId,
        singleId: spId,
        unitId: sequenceNbr,
        id: qd.sequenceNbr,
        modelType: 2,
        tempDeleteFlag: false
      });
      newCostDe = await insertStrategy.execute({
        pointLine: qd,
        newLine: costDe,
        indexId: costDe.standardId,
        libraryCode: costDe.libraryCode,
        option: 'insert',
        skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
        overwriteColumn: false
      });
    }

    return {
      'de': newCostDe,
      'titleData': PricingFileFindUtils.getUnit(constructId, spId, unit.sequenceNbr).measureProjectTables
    };
  }


  /**
   * 确认清单数据
   */
  confirmQdData(zjcsQd, unit, type) {
    let { constructId, spId, sequenceNbr } = unit;

    let qdList = null;
    if (type === ConstructionMeasureTypeConstant.ZJCS) {
      qdList = PricingFileFindUtils.getQdByZjcs(constructId, spId, sequenceNbr);
    }
    if (type === ConstructionMeasureTypeConstant.DJCS) {
      qdList = PricingFileFindUtils.getQdByDjcs(constructId, spId, sequenceNbr);
    }
    if (type === ConstructionMeasureTypeConstant.AWF) {
      qdList = PricingFileFindUtils.getQdByAwf(constructId, spId, sequenceNbr);
    }

    let qd = qdList.find(k => k.sequenceNbr === zjcsQd.sequenceNbr);
    if (ObjectUtils.isNotEmpty(qd)) {
      //当前项目有这条清单
      return qd;
    }
    qd = qdList.find(k => k.zjcsClassCode == parseInt(zjcsQd.zjcsClassCode));
    if (ObjectUtils.isNotEmpty(qd)) {
      //同专业记取，找到相同类型的清单
      return qd;
    }
    return null;
  }


  costBase(baseDeMap, unit, arg) {
    //获取所有的施工组织措施类别
    const cslist = Array.from(baseDeMap.keys());
    let result = {};
    for (const cs of cslist) {
      let deList = baseDeMap.get(cs);
      //计算基数
      let baseMath = this.mathBaseDeRjHj(deList, unit, arg);
      result[cs] = baseMath;
    }
    return result;
  }


  async baseDeGroup(otherZJCSUseDe, unit, mainFeeFile) {
    //将所有基数定额按施工组织措施类别进行分组
    let csMap = ArrayUtil.group(otherZJCSUseDe, 'measureType');
    //如果定额有随主工程
    let main = csMap.get(ConstantUtil.TITLE_WITH_MARJOR_PROJECT);
    if (ObjectUtils.isNotEmpty(main)) {
      let cslb = unit.secondInstallationProjectName;
      // 查询主工程定额，并合并随主工程定额
      let array = csMap.get(cslb);
      array = array || [];
      array.push(...main);
      csMap.set(cslb, array);
      csMap.delete(ConstantUtil.TITLE_WITH_MARJOR_PROJECT);
    }
    return csMap;
  }


  /**
   * 根据单位ID获取到单位数据，并且删除单位中已经记取了的安文费定额和总价措施定额
   */
  async getUnitAndClearCostDe(constructId, unitId, changeDeIds) {
    let unitList = PricingFileFindUtils.getUnitList(constructId);
    let unit = unitList.find(k => k.sequenceNbr === unitId);
    //删除单位中已经记取了的安文费定额和总价措施定额
    //删除所有的安文费定额和所有的总价措施定额
    await this.service.yuSuanProject.constructCostMathService.clearCostDe(unit, [DePropertyTypeConstant.AWF_DE, DePropertyTypeConstant.ZJCS_DE], changeDeIds);
    return unit;
  }


  /**
   * 记取安文费
   */
  async queryAwfDe(unit) {
    let { sequenceNbr, spId, constructId } = unit;
    //获取定额
    let awfUseDe = this.getAwfUseDe(unit);
    //判断是否需要记取费用
    if (ObjectUtils.isEmpty(awfUseDe)) {
      console.log('不需要进行  安文费  总价措施费用记取');
      return null;
    }
    //主取费文件
    let mainFeeFile = PricingFileFindUtils.getMainFeeFile(constructId, spId, sequenceNbr);

    let costMajorNameList = [...new Set(awfUseDe.map(k => k.costMajorName))];
    costMajorNameList = costMajorNameList.map(k => {
      if (k === ConstantUtil.TITLE_WITH_MARJOR_PROJECT) {
        k = mainFeeFile.feeFileName;
      }
      return k;
    });
    //安文费费用定额
    let awfDeList = await this.app.appDataSource.getRepository(BaseDeAwfRelation).find({
      where: { qfName: In(costMajorNameList) }
    });
    return awfDeList;
  }

  /**
   * 安文费费用计算
   * @param groupedItems
   * @param item
   */
  calculateCost(groupedItems, item, unit) {
    let { feeBuild, feeFiles } = unit;
    let feeFileId = item.feeFileId;
    //获取定额集合
    let deList = groupedItems[feeFileId];

    //定额合计求和
    const sum = deList.reduce((total, item) => {
      return total + parseFloat(item.zjfTotal);
    }, 0);

    let deIdList = deList.map(k => k.sequenceNbr);

    //规费求和
    let sum1 = 0;
    for (const i of deIdList) {
      let feeBuildElement = feeBuild[i];
      let { displayAllPrice } = feeBuildElement.find(k => k.code == 'FY3');
      sum1 += parseFloat(displayAllPrice);
    }
    let { anwenRateBase } = feeFiles.find(k => k.feeFileId);
    return (sum + sum1) * anwenRateBase;

  }


  /**
   * 获取单位的措施项目下的安文费清单或者其他总价措施清单
   * @param deStandardId
   * @returns
   */
  zjcsList(constructId, singleId, unitId, constructionMeasureType) {

    let array = [];
    //获取到当前的单位 并且获取措施项目数据
    let { measureProjectTables } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取措施项目中的标题ID集合
    let measureIdArray = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === constructionMeasureType).map(i => i.sequenceNbr);
    if (ObjectUtils.isEmpty(measureIdArray)) {
      return [];
    }
    let qdList = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.qd, measureIdArray);

    for (const item of qdList) {
      if (!ObjectUtils.isEmpty(item.zjcsClassCode)) {
        array.push({
          sequenceNbr: item.sequenceNbr,
          zjcsClassCode: item.zjcsClassCode + '',
          qdName: item.name,
          qdCode: item.fxCode,
          isCheck: 0
        });
      }
    }
    return array;
  }


  /**
   * 获取安文费使用到的定额数据
   * @param unit
   */
  getAwfUseDe(unit) {
    let { measureProjectTables, itemBillProjects } = unit;
    //获取分部分项下的定额数据
    let itemBillDeList = itemBillProjects.filter(k => k.kind === BranchProjectLevelConstant.de);
    //获取措施项目中的单价措施标题
    let measureDJCS = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS).map(i => i.sequenceNbr);
    let DJCSDeList = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureDJCS);
    //获取其他总价措施标题
    let measureZJCS = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS).map(i => i.sequenceNbr);
    let ZJCSDeList = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureZJCS);
    //组装分部分项，单价措施，总价措施下的所有定额数据
    itemBillDeList.push(...DJCSDeList);
    itemBillDeList.push(...ZJCSDeList);
    itemBillDeList = this._filterEmptyDe(itemBillDeList);
    itemBillDeList = itemBillDeList.filter(k => k.isCostDe !== DePropertyTypeConstant.AWF_DE);
    return itemBillDeList;
  }

  /**
   * 获取安文费的费用定额
   */
  async getAwfCostDe(unit) {

    let { measureProjectTables, itemBillProjects, constructId, spId, sequenceNbr } = unit;
    //获取分部分项下的定额数据
    let itemBillDeList = itemBillProjects.filter(k => k.kind === BranchProjectLevelConstant.de);
    //获取措施项目中的单价措施标题
    let measureDJCS = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS).map(i => i.sequenceNbr);
    let DJCSDeList = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureDJCS);
    //获取其他总价措施标题
    let measureZJCS = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS).map(i => i.sequenceNbr);
    let ZJCSDeList = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, measureZJCS);
    //组装分部分项，单价措施，总价措施下的所有定额数据
    itemBillDeList.push(...DJCSDeList);
    itemBillDeList.push(...ZJCSDeList);
    itemBillDeList = this._filterEmptyDe(itemBillDeList);

    //获取所有的其他项目数据
    let otherSum = this.service.yuSuanProject.otherProjectService.getMarkSafaTotal({
      constructId: constructId,
      singleId: spId,
      unitId: sequenceNbr
    });
    if (ObjectUtils.isEmpty(itemBillDeList) && ObjectUtils.isEmpty(otherSum)) {
      console.log('不需要进行  安文费  总价措施费用记取');
      return null;
    }
    //主取费文件
    let mainFeeFile = PricingFileFindUtils.getMainFeeFile(constructId, spId, sequenceNbr);

    let deCostMajorNameList = [...new Set(itemBillDeList.map(k => k.costMajorName))];

    deCostMajorNameList = deCostMajorNameList.map(k => {
      if (k === ConstantUtil.TITLE_WITH_MARJOR_PROJECT) {
        k = mainFeeFile.feeFileName;
      }
      return k;
    });

    //如果其他项目中存在数据，则也需要记取安文费
    if (!ObjectUtils.isEmpty(otherSum)) {
      deCostMajorNameList.push(mainFeeFile.feeFileName);
    }
    let constructIs2022 = PricingFileFindUtils.is22Unit(unit);
    //安文费费用定额
    let awfDeList = await this.app.appDataSource.getRepository(constructIs2022 ? BaseDeAwfRelation2022 : BaseDeAwfRelation).find({
      where: { qfName: In(deCostMajorNameList) }
    });
    return awfDeList;
  }

  /**
   * 获取其他总价措施使用到的定额数据
   * @param unit
   */
  getOtherZJCSUseDe(unit) {
    let { constructId, spId, sequenceNbr } = unit;
    let array = [];
    //获取分部分项标题下的定额数据
    let fbfxDe = this._filterEmptyDe(PricingFileFindUtils.getDeByfbfx(constructId, spId, sequenceNbr));
    //获取措施项目中的单价措施标题下的定额数据
    let djcsDe = this._filterEmptyDe(PricingFileFindUtils.getDeByDjcs(constructId, spId, sequenceNbr));
    array.push(...fbfxDe);
    array.push(...djcsDe);
    // 总价措施的基数定额需要过滤掉【总价措施、安文费、安装】的费用定额
    const costDeArr = [DePropertyTypeConstant.AWF_DE, DePropertyTypeConstant.ZJCS_DE, DePropertyTypeConstant.AZ_DE];
    return array.filter(k => !costDeArr.includes(k.isCostDe));
  }


  /**
   * 获取总价措施费用分类列表
   * @param deStandardId
   * @returns
   */
  async zjcsClassList(args) {
    let { unitId, singleId, constructId } = args;
    //获取到当前的单位
    let unitProjectDTO = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let is22De = PricingFileFindUtils.is22Unit(unitProjectDTO);
    //组装数据
    let resultModel = {};
    let zjcsClassList = [];
    let awfqdModelList = [];
    let zjcsqdModelList = [];


    //获取安文费下的所有清单
    let awfQdList = this.zjcsList(constructId, singleId, unitId, ConstructionMeasureTypeConstant.AWF);
    //获取总价措施下的所有清单
    let zjcsQdList = this.zjcsList(constructId, singleId, unitId, ConstructionMeasureTypeConstant.ZJCS);

    //安文费清单转model, 是否记取默认为0
    awfQdList.forEach(awf => {
      awfqdModelList.push(awf);
    });
    //总价措施清单转model, 是否记取默认为0
    zjcsQdList.forEach(zjcs => {
      zjcsqdModelList.push(zjcs);
    });


    //获取分部分项定额
    let fbfxDe = PricingFileFindUtils.getDeByfbfx(constructId, singleId, unitId);
    //获取措施项目的单价措施定额
    let djcsDe = PricingFileFindUtils.getDeByDjcs(constructId, singleId, unitId);
    //获取措施项目的总价措施定额,不包括总价措施费用定额
    let zjcsDe = PricingFileFindUtils.getDeByZjcs(constructId, singleId, unitId);
    zjcsDe = zjcsDe.filter(k => k.isCostDe !== 2);

    //获取所有的其他项目数据是否勾选安文费
    let otherProjectDTOS = this.service.yuSuanProject.otherProjectService.getMarkSafaTotal({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId
    });

    let awfFlag = true;
    let zjcsFlag = true;
    //判断是否需要记取费用
    if (ObjectUtils.isEmpty(fbfxDe) && ObjectUtils.isEmpty(djcsDe) && ObjectUtils.isEmpty(zjcsDe) && ObjectUtils.isEmpty(otherProjectDTOS)) {
      awfFlag = false;
    }
    //判断是否需要记取总价措施费用
    if (ObjectUtils.isEmpty(fbfxDe) && ObjectUtils.isEmpty(djcsDe)) {
      zjcsFlag = false;
    }
    if (!awfFlag && !zjcsFlag) {
      return null;
    }

    if (awfFlag) {
      if (unitProjectDTO.fixationSecurityFee === '1') {
        zjcsClassList.push({
          zjcsClassCode: '0',
          qdName: '安全文明施工费',
          isCheck: 0,
          fixedAWFee: 1
        });
      } else {
        zjcsClassList.push({
          zjcsClassCode: '0',
          qdName: '安全文明施工费',
          isCheck: 0
        });
      }

    }

    if (zjcsFlag) {
      let fbcs = fbfxDe.map(k => k.measureType);
      let djcs = djcsDe.map(k => k.measureType);
      fbcs.push(...djcs);
      //去重施工组织措施类别
      let cslbList = [...new Set(fbcs)];

      // 单位主施工组织措施类别
      let unitCoreCslb = unitProjectDTO.secondInstallationProjectName;
      if (cslbList.includes(ConstantUtil.TITLE_WITH_MARJOR_PROJECT) && !cslbList.includes(unitCoreCslb)) {
        cslbList.push(unitCoreCslb);
        cslbList = cslbList.filter(c => c != ConstantUtil.TITLE_WITH_MARJOR_PROJECT);
      }
      //总价措施费用定额
      let baseCSLBDTOS = await this.app.appDataSource.getRepository(is22De ? BaseCSLB2022 : BaseCSLB).find({
        where: { cslbName: In(cslbList) }
      });
      let clsbCodes = baseCSLBDTOS.map(k => k.cslbCode);

      let baseDeDTOS = await this.app.appDataSource.getRepository(is22De ? BaseDe2022 : BaseDe).find({
        where: { cslbCode: In(clsbCodes), isZj: 1 },
        order: { sortNo: 'ASC' }
      });


      let baseDeDistinctDTOS = [];
      // 根据属性进行去重
      if (is22De) {
        baseDeDistinctDTOS = baseDeDTOS.filter(dto => !ObjectUtils.isEmpty(dto.zjcsClassCode)).filter((item, index, self) => {
          return index === self.findIndex((i) => (i.zjcsClassName === item.zjcsClassName));
        });
      } else {
        let gtzje = baseDeDTOS.filter(p => p.zjcsClassCode == '16');
        if (ObjectUtils.isNotEmpty(gtzje)) {
          for (let ooo of gtzje) {
            let name = this.switchGtzjfName1(ooo.deName);
            ooo.zjcsClassName = ooo.zjcsClassName + name;
          }
        }
        baseDeDistinctDTOS = baseDeDTOS.filter(dto => !ObjectUtils.isEmpty(dto.zjcsClassCode)).filter((item, index, self) => {
          return index === self.findIndex((i) => (i.zjcsClassName === item.zjcsClassName));
        });
      }

      //排序
      baseDeDistinctDTOS.sort((a, b) => parseInt(a.zjcsClassCode) - parseInt(b.zjcsClassCode));

      //封装总价措施列表数据
      for (const baseDeDTO of baseDeDistinctDTOS) {
        let isCheck = 1;
        // if (baseDeDTO.zjcsClassCode === '9' || baseDeDTO.zjcsClassCode === '11' || baseDeDTO.zjcsClassCode === '12' || baseDeDTO.zjcsClassCode === '16') {
        //   isCheck = 0;
        // }
        if (baseDeDTO.zjcsClassCode === '11' || baseDeDTO.zjcsClassCode === '12') {
          isCheck = 0;
        }
        if (!is22De) {
          if (unitProjectDTO.mainDeLibrary == '2013-YLLH-DEK') {
            if (baseDeDTO.zjcsClassCode == '15') {
              // 12园林绿化 繁华地段交叉施工增加费 不勾选
              isCheck = 0;
            }
            if (baseDeDTO.zjcsClassCode == '9') {
              // 12园林绿化 停水停电增加费 勾选
              isCheck = 1;
            }
          }
          if (unitProjectDTO.mainDeLibrary == '2013-FGJZ-DEG') {
            if (baseDeDTO.zjcsClassCode == '9') {
              // 12仿古建筑 停水停电增加费 勾选
              isCheck = 1;
            }
          }
        }
        zjcsClassList.push({
          ['qdName']: baseDeDTO.zjcsClassName,
          ['zjcsClassCode']: baseDeDTO.zjcsClassCode,
          ['isCheck']: isCheck
        });
      }
    }
    //组装响应给前端的数据
    resultModel.awfQd = awfqdModelList;
    resultModel.zjcsClassQd = zjcsqdModelList;
    resultModel.zjcsClassList = zjcsClassList;
    return resultModel;
  }


  /**
   * 高台增加费名称转换
   * @returns {string}
   */
  switchGtzjfName1(deName) {
    let result = '';

    if (deName.includes('2.5m以内')) {
      result = '（2.5m）';
    } else if (deName.includes('6m以内')) {
      result = '（6m以内）';
    } else if (deName.includes('2~5m')) {
      result = '（2-5m）';
    } else if (deName.includes('5m以上')) {
      result = '（5m以上）';
    }
    return result;
  }


  /**
   * 高台增加费名称转换
   * @returns {string}
   */
  switchGtzjfName2(zjcsClassName) {
    let result = '';

    switch (zjcsClassName) {
      case '高台增加费（2.5m）':
        result = '2.5m以内';
        break;
      case '高台增加费（6m以内）':
        result = '6m以内';
        break;
      case '高台增加费（2-5m）':
        result = '2~5m';
        break;
      case '高台增加费（5m以上）':
        result = '5m以上';
        break;
    }
    return result;
  }


  /**
   * 装饰垂运层高下拉框
   * @return {Promise<void>}
   */
  async storeyList(args) {
    let { constructId, singleId, unitId } = args;
    //原本返回constructId为空或者不存在时 返回null,由于开发阶段需要,改为一个固定值
    if (ObjectUtils.isEmpty(constructId)) {
      return null;
    }
    let is22De = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
    let storeyList = await this.app.appDataSource
      .getRepository(is22De ? BaseDeZsCgRelation2022 : BaseDeZsCgRelation).find();
    const groupedItems = storeyList.reduce((groups, item) => {
      const location = item.location;
      if (!groups[location]) {
        groups[location] = [];
      }
      groups[location].push(item);
      return groups;
    }, {});
    return { down: groupedItems[0], up: groupedItems[1] };
  }


  /**
   * 查询河北省装饰装修工程消耗量定额（2012）下挂的定额
   */
  async getDecorationDe(constructId, singleId, unitId) {
    return await this.service.yuSuanProject.baseDeService.selectDecorationDe(constructId, singleId, unitId);
  }

  /**
   * 获取分部分项 单价措施下指定河北省装饰装修工程消耗量定额（2012）的定额
   */
  async conditionDeList(arg) {
    const result = await this.getZsBaseDeList(arg);
    if (ObjectUtils.isEmpty(result)) {
      return [];
    }
    let copyArray = [];
    for (const item of result) {
      let data = {};
      data.sequenceNbr = item.sequenceNbr;
      data.bdName = item.bdName;
      data.bdCode = item.bdCode;
      data.fxCode = item.fxCode;
      data.name = item.name;
      data.kind = item.kind;
      data.type = item.type;
      data.quantity = item.quantity;
      data.parentId = item.parentId;
      data.upOrDown = 'up';
      data.up = null;
      // 新的定额默认为记取
      data.value = 1;
      // 项目特征
      data.projectAttr = item.projectAttr;
      copyArray.push(data);
    }
    return copyArray;
  }

  /**
   * 获取装饰装修B1-B7对应的所有基数定额
   */
  async getZsBaseDeList(arg) {
    let array = [];
    let { unitId, singleId, constructId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let { measureProjectTables, itemBillProjects, constructMajorType } = unit;
    array.push(...(itemBillProjects.getAllNodes()));
    //塞入措施项目的第一行数据
    array.push(measureProjectTables.root);

    //获取到所有的单价措施标题下数据
    let measureDJCS = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS);
    for (const item of measureDJCS) {
      let { datas } = this.service.yuSuanProject.baseBranchProjectOptionService.getDJCSConditionList(measureProjectTables, item);
      if (!ObjectUtils.isEmpty(datas)) {
        array.push(...datas);
      }
    }
    //获取装修装饰定额b1-b7
    let deIdList = await this.getDecorationDe(constructId, singleId, unitId);
    deIdList = deIdList.map(i => i.sequenceNbr);
    let flag = false;
    for (let i = array.length - 1; i >= 0; i--) {
      let item = array[i];
      let { kind, libraryCode, standardId } = item;
      if (BranchProjectLevelConstant.de === kind) {

        if ((libraryCode === LibraryCodeConstant.ZSZXDEY_2022 || libraryCode === LibraryCodeConstant.ZSZXDEY_2012) && deIdList.includes(standardId)) {
          flag = true;
        } else {
          array.splice(i, 1);
        }
      }
    }
    if (flag) {
      //处理值包含定额行以及父级的所有数据
      let deList = array.filter(k => k.kind === BranchProjectLevelConstant.de);
      let result = [];
      this.findDataByParentId(result, array, deList.map(a => a.parentId));
      result.push(...deList);
      return result;
    }
    return [];
  }

  findDataByParentId(result, arr, parentIds) {
    for (const parentId of parentIds) {
      const foundItems = arr.find(item => item.sequenceNbr === parentId);
      if (!result.map(i => i.sequenceNbr).includes(foundItems.sequenceNbr)) {
        result.push(foundItems);
      }
      if (ObjectUtils.isEmpty(foundItems.parentId) || foundItems.parentId === '0') {
        return;
      } else {
        this.findDataByParentId(result, arr, [foundItems.parentId]);
      }

    }
    return result;
  }

  qdExistDe(arg) {
    let { unitId, singleId, constructId, qdId, constructionMeasureType, isCostDe } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let { measureProjectTables, itemBillProjects } = unit;
    let array = [];
    if (ConstructionMeasureTypeConstant.DJCS === constructionMeasureType || ConstructionMeasureTypeConstant.ZJCS === constructionMeasureType) {
      let qd = measureProjectTables.find(k => k.sequenceNbr === qdId);
      if (ObjectUtils.isEmpty(qd)) {
        return false;
      }
      array = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.de, qd.sequenceNbr);
    }
    if (ConstructionMeasureTypeConstant.FBFX === constructionMeasureType) {
      let qd = itemBillProjects.find(k => k.sequenceNbr === qdId);
      if (ObjectUtils.isEmpty(qd)) {
        return false;
      }
      array = PricingFileFindUtils.getUnitDatas(itemBillProjects, BranchProjectLevelConstant.de, qd.sequenceNbr);
    }

    if (ObjectUtils.isEmpty(array)) {
      return false;
    }
    let de = array.find(k => k.isStandard === 1 && k.isCostDe == isCostDe);
    if (ObjectUtils.isEmpty(de)) {
      return false;
    } else {
      return true;
    }
  }

  /**
   * 记取位置下拉框
   */
  async recordPosition(arg) {
    let { unitId, singleId, constructId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let result = [];
    //单价措施
    let djcs = this.cyqdDataHandler(unit, ConstructionMeasureTypeConstant.DJCS);
    if (djcs.isExist) {
      result.push({
        className: '措施项目-单价措施',
        classCode: ConstructionMeasureTypeConstant.DJCS,
        data: djcs.data
      });
    }
    //其他总价措施
    let qtzjcs = this.cyqdDataHandler(unit, ConstructionMeasureTypeConstant.ZJCS);
    if (qtzjcs.isExist) {
      result.push({
        className: '措施项目-其他总价措施',
        classCode: ConstructionMeasureTypeConstant.ZJCS,
        data: qtzjcs.data
      });
    }
    //分部分项
    let fbfx = this.cyqdDataHandler(unit, ConstructionMeasureTypeConstant.FBFX);
    if (fbfx.isExist) {
      result.push({ className: '分部分项', classCode: ConstructionMeasureTypeConstant.FBFX, data: fbfx.data });
    }
    //去除所有定额数据
    if (!ObjectUtils.isEmpty(result)) {
      for (const item of result) {
        item.data = item.data.filter(i => BranchProjectLevelConstant.de !== i.kind);
      }
    }

    return result;
  }

  /**
   * 垂运清单处理
   * @param unit
   * @param type
   */
  cyqdDataHandler(unit, type) {
    let result = {};
    let array = [];
    //处理分部分项
    if (ConstructionMeasureTypeConstant.FBFX === type) {
      let { itemBillProjects } = unit;
      array = array.concat(itemBillProjects.getAllNodes());
    }
    //处理单价措施或者其他总价措施数据
    if (ConstructionMeasureTypeConstant.DJCS === type) {
      let { measureProjectTables } = unit;
      array.push(measureProjectTables.root);
      //获取到所有的单价措施标题下数据
      let measure = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === type);
      for (const item of measure) {
        let { datas } = this.service.yuSuanProject.baseBranchProjectOptionService.getDJCSConditionList(measureProjectTables, item);
        if (!ObjectUtils.isEmpty(datas)) {
          array.push(...datas);
        }
      }
    }
    // 处理其他总价措施数据
    if (ConstructionMeasureTypeConstant.ZJCS === type) {
      let { measureProjectTables } = unit;
      // 取到其他总价措施的标题数据
      let measure = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === type);
      if (ObjectUtils.isNotEmpty(measure)) {
        array.push(...(measure[0].children));
      }
    }

    let flag = false;
    for (let i = array.length - 1; i >= 0; i--) {
      let item = array[i];
      let { kind, fxCode } = item;
      if (BranchProjectLevelConstant.qd === kind) {
        if (!ObjectUtils.isEmpty(fxCode) && fxCode.startsWith('011703001')) {
          flag = true;
        } else {
          array.splice(i, 1);
        }
      }
    }
    result.isExist = flag;
    result.data = array;
    return result;
  }


  cyCostMathCache(arg) {
    let { unitId, singleId, constructId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    return unit.cyCostMathCache;

  }

  zjcsCostMathCache(arg) {
    let { unitId, singleId, constructId } = arg;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //为空默认值赋值
    if (ObjectUtils.isEmpty(unit.zjcsCostMathCache)) {
      let zjcsCostMathCache = {};
      zjcsCostMathCache.csfyCalculateBaseCode = CalculateBaseType.calculateBaseInitValue;
      zjcsCostMathCache.csfyCalculateBaseArea = CalculateBaseType.calculateBaseAreaOther;
      unit.zjcsCostMathCache = zjcsCostMathCache;
    } else {
      if (ObjectUtils.isEmpty(unit.zjcsCostMathCache.csfyCalculateBaseCode)) {
        unit.zjcsCostMathCache.csfyCalculateBaseCode = CalculateBaseType.calculateBaseInitValue;
      }
      if (ObjectUtils.isEmpty(unit.zjcsCostMathCache.csfyCalculateBaseArea)) {
        unit.zjcsCostMathCache.csfyCalculateBaseArea = CalculateBaseType.calculateBaseAreaOther;
      }
    }

    //判断是否显示计取地区 分部分项、措施项目的定额施工组织措施类别包含 道路桥梁维修工程、排水设施维修养护工程、路灯维修工程
    unit.zjcsCostMathCache.csfyCalculateBaseAreaView = false;
    let fbFxAll = unit.itemBillProjects;
    let itemBillProjects = fbFxAll.filter(item => item.kind === BranchProjectLevelConstant.de && ObjectUtils.isNotEmpty(item.measureType) && (ObjectUtils.isEmpty(item.tempDeleteFlag) || item.tempDeleteFlag === false)
      && (item.measureType === CalculateBaseType.dlqlwxgc || item.measureType === CalculateBaseType.pssswxyhgc || item.measureType === CalculateBaseType.ldwxgc)
    );
    if (ObjectUtils.isNotEmpty(itemBillProjects)) {
      unit.zjcsCostMathCache.csfyCalculateBaseAreaView = true;
    } else {
      let csxmTotalAll = unit.measureProjectTables;
      if (ObjectUtils.isNotEmpty(csxmTotalAll)) {
        let csxmTotalAllFilter = csxmTotalAll.filter(item => item.kind === BranchProjectLevelConstant.de && ObjectUtils.isNotEmpty(item.measureType) && (ObjectUtils.isEmpty(item.tempDeleteFlag) || item.tempDeleteFlag === false)
          && (item.measureType === CalculateBaseType.dlqlwxgc || item.measureType === CalculateBaseType.pssswxyhgc || item.measureType === CalculateBaseType.ldwxgc));
        if (ObjectUtils.isNotEmpty(csxmTotalAllFilter)) {
          unit.zjcsCostMathCache.csfyCalculateBaseAreaView = true;
        }
      }
    }

    return unit.zjcsCostMathCache;

  }


  /**
   * 装饰垂运记取
   * @param arg
   * @return {Promise<void>}
   */
  async czysCostMath(arg) {
    let { unitId, singleId, constructId, optionType, qdId, constructionMeasureType, data, up, down } = arg;
    let changeDeIds = new Set();
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const is22Unit = PricingFileFindUtils.is22Unit(unit);
    //只筛选定额
    data = data.filter(k => k.kind === StepItemCostLevelConstant.de);

    //记取参数缓存
    unit.cyCostMathCache = arg;
    //删除垂运费用定额
    await this.service.yuSuanProject.constructCostMathService.clearCostDe(unit, [DePropertyTypeConstant.DS_CY_DE, DePropertyTypeConstant.DX_CY_DE], changeDeIds);
    if (ObjectUtils.isEmpty(qdId)) {
      console.log('没有选择指定的清单');
      return;
    }

    //默认值的设置
    if (ObjectUtils.isEmpty(constructionMeasureType)) {
      constructionMeasureType = ConstructionMeasureTypeConstant.DJCS;
    }
    //获取前端选择的地上地下的定额

    //根据定额选择的以上以下分组  值为 up/down
    let group = ArrayUtil.group(data, 'upOrDown');
    let zsdeArray = [];
    if (!ObjectUtils.isEmpty(up)) {
      //地上
      let deList = group.get(GroundTypeConstant.UP);
      if (!ObjectUtils.isEmpty(deList)) {
        zsdeArray.push(up);
      }
    }
    if (!ObjectUtils.isEmpty(down)) {
      //地下
      let deList = group.get(GroundTypeConstant.DOWN);
      if (!ObjectUtils.isEmpty(deList)) {
        zsdeArray.push(down);
      }
    }
    //如果没有选择地上或者地下定额的直接返回
    if (ObjectUtils.isEmpty(zsdeArray)) {
      return;
    }
    //根据前端选择的地上地下的定额查询实际的定额数据,并且计算工程量表达式的值
    if (!ObjectUtils.isEmpty(zsdeArray)) {
      zsdeArray = await this.app.appDataSource.getRepository(is22Unit ? BaseDe2022 : BaseDe).find({
        where: {
          deCode: In(zsdeArray),
          libraryCode: is22Unit ? LibraryCodeConstant.ZSZXDEY_2022 : LibraryCodeConstant.ZSZXDEY_2012
        }
      });
      //获取到地上地下的定额数据，计算选中定额的工程量表达式的值
      this.calculatedValue(unit, zsdeArray, up, down, data);
    }
    //查询表里的垂运清单数据
    //查找垂运清单数据--清单ID写死
    let cyQd = await this.getCyQd();
    let code = this.service.yuSuanProject.baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, cyQd.bdCodeLevel04);
    let lineData = {
      'name': cyQd.bdNameLevel04,
      'bdName': cyQd.bdNameLevel04,
      'kind': StepItemCostLevelConstant.qd,
      'fxCode': code,
      'bdCode': code,
      'standardId': cyQd.sequenceNbr,
      'unit': '天',
      'unitList': cyQd.unit,
      'libraryCode': cyQd.libraryCode
    };
    //根据标题类型获取获取标题下的数据
    let titleByQdData = this.cyqdDataHandler(unit, constructionMeasureType);
    let qd = null;
    if (titleByQdData.isExist) {
      qd = titleByQdData.data.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr === qdId);
      if (ObjectUtils.isEmpty(qd)) {
        qd = await this.createQd(unit, constructionMeasureType, lineData);
        //添加清单特征以及内容
        await this.service.yuSuanProject.listFeatureProcess.saveBatchToFbFxQdFeature(qd.libraryCode, qd.fxCode,
          qd.sequenceNbr,
          constructId, singleId, unitId);
      }
    } else {
      qd = await this.createQd(unit, constructionMeasureType, lineData);
      //添加清单特征以及内容
      await this.service.yuSuanProject.listFeatureProcess.saveBatchToFbFxQdFeature(qd.libraryCode, qd.fxCode,
        qd.sequenceNbr,
        constructId, singleId, unitId);
    }
    let newTitleByQdData = this.cyqdDataHandler(unit, constructionMeasureType);
    await this.addToExistingQd(qd.sequenceNbr, unit, newTitleByQdData.data, constructionMeasureType, zsdeArray);

    //费用定额自动记取计算
    await this.service.yuSuanProject.autoCostMathService.autoCostMath({
      constructId: constructId,
      singleId: singleId,
      unitId: unitId,
      countCostCodeFlag: true,
      changeDeIdArr: changeDeIds
    });

    let result = '已成功记取至';
    let { fxCode, bdCode, name } = qd;
    if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
      result = result.concat('分部分项-').concat(ObjectUtils.isEmpty(bdCode) ? fxCode : bdCode).concat(name).concat('下');
    }
    if (constructionMeasureType === ConstructionMeasureTypeConstant.DJCS) {
      result = result.concat('单价措施-').concat(ObjectUtils.isEmpty(fxCode) ? bdCode : fxCode).concat(name).concat('下');
    }
    if (constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS) {
      result = result.concat('其他总价措施-').concat(ObjectUtils.isEmpty(fxCode) ? bdCode : fxCode).concat(name).concat('下');
    }
    return result;
  }


  /**
   * 装饰垂运---记取至已有清单
   */
  async addToExistingQd(qdId, unit, allList, constructionMeasureType, deList) {
    if (ObjectUtils.isEmpty(deList)) {
      return;
    }
    //根据包含垂运清单的所有数据
    let qd = allList.find(k => k.sequenceNbr === qdId);
    await this.cyQdAddDe(unit, qd, deList, constructionMeasureType);

    return qd;

  }

  /**
   *  垂运清单下挂上垂运定额
   * @param qdList 原始的清单数据，一定是从分部分项或者措施项目中取出来的数据
   * @param deList 定额数据
   */
  async cyQdAddDe(unit, qd, deList, constructionMeasureType) {
    if (ObjectUtils.isEmpty(qd)) {
      return;
    }
    if (ObjectUtils.isEmpty(deList)) {
      return;
    }
    let { constructId, spId, sequenceNbr } = unit;
    for (const deItem of deList) {
      if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
        // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
        this.service.yuSuanProject.itemBillProjectOptionService.cleanQdDelTempStatus({
          constructId: constructId,
          singleId: spId,
          unitId: sequenceNbr,
          id: qd.sequenceNbr,
          modelType: 1,
          tempDeleteFlag: false
        });
        let insertStrategy = new InsertStrategy({
          constructId,
          singleId: spId,
          unitId: sequenceNbr,
          pageType: 'fbfx'
        });
        await insertStrategy.execute({
          pointLine: qd,
          newLine: this.dataHandler(deItem),
          indexId: deItem.sequenceNbr,
          libraryCode: deItem.libraryCode,
          option: 'insert',
          skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
          overwriteColumn: false
        });
      } else {
        // 检查要添加的定额所属清单是不是临时删除的  如果是临时删除的就需要恢复这个清单和清单下的所有数据
        this.service.yuSuanProject.itemBillProjectOptionService.cleanQdDelTempStatus({
          constructId: constructId,
          singleId: spId,
          unitId: sequenceNbr,
          id: qd.sequenceNbr,
          modelType: 2,
          tempDeleteFlag: false
        });
        let insertStrategy = new InsertStrategy({
          constructId,
          singleId: spId,
          unitId: sequenceNbr,
          pageType: 'csxm'
        });
        await insertStrategy.execute({
          pointLine: qd,
          newLine: this.dataHandler(deItem),
          indexId: deItem.sequenceNbr,
          libraryCode: deItem.libraryCode,
          option: 'insert',
          skip: { rcj: false, quantity: true, jiqu: true, huizong: true },
          overwriteColumn: false
        });
      }
    }
  }

  dataHandler(deItem) {
    let itemBillProject = new ItemBillProject();
    itemBillProject.bdName = deItem.deName;
    itemBillProject.bdCode = deItem.deCode;
    itemBillProject.name = deItem.deName;
    itemBillProject.fxCode = deItem.deCode;
    itemBillProject.kind = StepItemCostLevelConstant.de;
    itemBillProject.unit = deItem.unit;
    itemBillProject.standardId = deItem.sequenceNbr;
    itemBillProject.isCostDe = deItem.isCostDe; //添加定额标识 垂运定额
    itemBillProject.isStandard = DePropertyTypeConstant.STANDARD;//定额数据位标准定额数据
    itemBillProject.libraryCode = deItem.libraryCode;
    itemBillProject.quantityExpression = deItem.quantityExpression;//工程量表达式展示用
    itemBillProject.quantityExpressionNbr = deItem.quantityExpressionNbr;//工程量表达式计算用
    itemBillProject.quantityExpressionNbr = deItem.quantityExpressionNbr;//工程量表达式计算用
    itemBillProject.quantity = deItem.quantity;
    return itemBillProject;
  }


  /**
   * 新建清单
   * @param unit
   * @param arg
   * @param lineData
   */
  async createQd(unit, constructionMeasureType, qdData, deList) {

    /**
     * （1）    措施清单-单价措施（默认）；选取后新增垂运清单至单价措施最后一行若为空清单行则填充（若不存在单价措施标题行则新增，若存在多个记取至行号较小的标题行下）
     * （2）    措施清单-其他总价措施；选取后新增垂运清单至其他总价措施下最后一行（若不存在其他总价措施标题行则新增，若存在多个记取至行号较小的标题行下）
     *          分部分项，选取后新增至最后一行
     */
      //返回的清单数据
    let qDresult = {};
    let {
      constructId,
      spId,
      sequenceNbr,
      qdIdList,
      measureProjectTables,
      itemBillProjects,
      constructMajorType
    } = unit;
    if (constructionMeasureType === ConstructionMeasureTypeConstant.DJCS || constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS) {
      //获取到所有的单价措施标题或者总价措施
      let measure = measureProjectTables.find(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === constructionMeasureType);
      //构建返回参数值
      if (ObjectUtils.isEmpty(measure)) {
        //新增单价措施标题行
        let defaultLine = {};
        if (constructionMeasureType === ConstructionMeasureTypeConstant.DJCS) {
          defaultLine = this.service.yuSuanProject.stepItemCostService.Default_THR_Line();
        }
        if (constructionMeasureType === ConstructionMeasureTypeConstant.ZJCS) {
          defaultLine = this.service.yuSuanProject.stepItemCostService.Default_SEC_Line();
        }
        let {
          data,
          index
        } = await this.service.yuSuanProject.stepItemCostService.save(constructId, spId, sequenceNbr, this.service.yuSuanProject.stepItemCostService.getInsertPointLineByCurrentName(defaultLine.name), defaultLine);
        let save = await this.service.yuSuanProject.stepItemCostService.save(constructId, spId, sequenceNbr, data, qdData);
        qDresult = save.data;
      } else {
        let qdList = PricingFileFindUtils.getUnitDatas(measureProjectTables, BranchProjectLevelConstant.qd, measure.sequenceNbr);
        //获取到空数据行的清单
        let emptQd = qdList.find(k => k.kind === BranchProjectLevelConstant.qd && ObjectUtils.isEmpty(k.fxCode) && ObjectUtils.isEmpty(k.bdCode));
        if (ObjectUtils.isEmpty(emptQd)) {
          let {
            data,
            index
          } = await this.service.yuSuanProject.stepItemCostService.save(constructId, spId, sequenceNbr, measure, qdData);
          qDresult = data;
        } else {
          emptQd.name = qdData.name;
          emptQd.kind = StepItemCostLevelConstant.qd;
          emptQd.fxCode = qdData.fxCode;
          emptQd.standardId = qdData.standardId;
          emptQd.unit = qdData.unit;
          emptQd.libraryCode = qdData.libraryCode;
          qDresult = emptQd;
        }
      }
    }
    //分部分项
    if (constructionMeasureType === ConstructionMeasureTypeConstant.FBFX) {
      const allData = itemBillProjects.getAllNodes();
      let {
        data,
        index
      } = await this.service.yuSuanProject.itemBillProjectOptionService.insertLine(constructId, spId, sequenceNbr, allData[allData.length - 1], qdData);
      qDresult = data;
    }
    return qDresult;

  }

  /**
   * 获取垂运清单
   */
  async getCyQd() {
    let cyQd = await this.app.appDataSource.getRepository(BaseList).findOne({
      where: { sequenceNbr: '1658012394309423165' }
    });
    return cyQd;
  }

  /**
   * 计算选择的地上地下工程量表达式
   * @param zsdeArray
   * @param up
   * @param down
   * @param data
   */
  calculatedValue(unit, zsdeArray, up, down, data) {
    if (ObjectUtils.isEmpty(data)) {
      return;
    }
    //根据定额选择的以上以下分组  值为 up/down
    let group = ArrayUtil.group(data, 'upOrDown');
    //地上
    let upDeDataList = zsdeArray.filter(k => ObjectUtils.isEmpty(k.deCode) ? k.bdCode : k.deCode === up);
    if (!ObjectUtils.isEmpty(upDeDataList)) {
      let deList = group.get(GroundTypeConstant.UP);
      for (const upDeData of upDeDataList) {
        if (!ObjectUtils.isEmpty(deList)) {
          let quantityExpression = this.quantityExpression(unit, deList);
          upDeData.quantityExpression = 'DSZSGR';
          upDeData.quantityExpressionNbr = quantityExpression;
          upDeData.isCostDe = DePropertyTypeConstant.DS_CY_DE;
          const regex = /\b\d+\b/;
          let match = upDeData.unit.match(regex);
          if (match) {
            const number = parseInt(match[0]);
            upDeData.quantity = NumberUtil.numberScale(NumberUtil.divide(upDeData.quantityExpressionNbr, number), 6);
          } else {
            upDeData.quantity = NumberUtil.numberScale(NumberUtil.divide(upDeData.quantityExpressionNbr, 1), 6);
          }
        } else {
          let index = zsdeArray.indexOf(upDeData);
          if (index !== -1) {
            zsdeArray.splice(index, 1);
          }
        }
      }
    }
    //地下
    let downDeDataList = zsdeArray.filter(k => ObjectUtils.isEmpty(k.deCode) ? k.bdCode : k.deCode === down);
    if (!ObjectUtils.isEmpty(downDeDataList)) {
      let deList = group.get(GroundTypeConstant.DOWN);
      for (const downDeData of downDeDataList) {
        if (!ObjectUtils.isEmpty(deList)) {
          let quantityExpression = this.quantityExpression(unit, deList);
          downDeData.quantityExpression = 'DXZSGR';
          downDeData.quantityExpressionNbr = quantityExpression;
          downDeData.isCostDe = DePropertyTypeConstant.DX_CY_DE;
          const regex = /\b\d+\b/;
          let match = downDeData.unit.match(regex);
          if (match) {
            const number = parseInt(match[0]);
            downDeData.quantity = NumberUtil.numberScale(NumberUtil.divide(downDeData.quantityExpressionNbr, number), 6);
          } else {
            downDeData.quantity = NumberUtil.numberScale(NumberUtil.divide(downDeData.quantityExpressionNbr, 1), 6);
          }
        } else {
          let index = zsdeArray.indexOf(downDeData);
          if (index !== -1) {
            zsdeArray.splice(index, 1);
          }
        }
      }
    }
  }

  /**
   * 计算工程量表达式
   */
  quantityExpression(unit, deList) {
    let { constructId, spId, sequenceNbr } = unit;
    //获取到当前项目的所有人材机数据
    let rcjList = PricingFileFindUtils.getRcjList(constructId, spId, sequenceNbr);
    if (!ObjectUtils.isEmpty(deList)) {
      let deIdList = deList.map(k => k.sequenceNbr);
      //求和
      let reduce = rcjList.filter(k => k.kind === 1 && k.unit === '工日' && deIdList.includes(k.deId))
        .map(k => k.totalNumber).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
      return NumberUtil.numberScale(reduce, 6);
    }
  }


  /**
   * 根据定额ID查询是否属于某种费用定额
   */
  costDeByDe(de) {
    let { value } = de;
    /**.
     * //不是费用定额
     *     static NON_COST_DE = 0;
     *     static AWF_DE = 1; //安文费
     *     static ZJCS_DE = 2;//总价措施
     *     static CG_DE = 3;//超高
     *     static CY_DE = 4;//垂运
     *     static AZ_DE = 5;//安装费
     * @type {null}
     */
    let result = 0;
    if (ObjectUtils.isEmpty(de)) {
      return result;
    }
    //判断垂运定额
    // if (value === 110){
    //     result = DePropertyTypeConstant.CY_DE;
    // }
    //判断超高费用定额
    if (value === 120) {
      result = DePropertyTypeConstant.CG_DE;
    }
    //判断安装费用定额
    if ([210, 220, 230, 240, 250].includes(value)) {
      result = DePropertyTypeConstant.AZ_DE;
    }
    if (de.isCostDe == DePropertyTypeConstant.DS_CY_DE || de.isCostDe == DePropertyTypeConstant.DX_CY_DE) {
      result = de.isCostDe;
    }
    if (!ObjectUtils.isEmpty(de.zjcsClassCode)) {
      result = DePropertyTypeConstant.ZJCS_DE;
    }

    return result;
  }

  /**
   * 这个方法的作用是：根据基础定额库的信息确认要添加的定额是不是一个费用定额 如果是就返回对应的isCostDe
   */
  getIsCostDeByBaseDe(baseDe) {
    let { deCode, value, zjcsClassCode } = baseDe;

    if (ObjectUtils.isEmpty(value) && ObjectUtils.isEmpty(zjcsClassCode)) {
      return DePropertyTypeConstant.NON_COST_DE;
    }
    // 这个value是基础定额库定额的value 【110是垂运定额、120是超高定额、210是安装的超高费、220是安装的系统调试费、230是安装的垂直运输费、240是安装的脚手架搭拆费、250是安装的操作高度增加费】
    // 所以这个value只能判断是不是垂运、超高、安装  并不能区分出是不是【总价措施】或者【安文费】
    // 同时我们的垂运分为了【地上】和【地下】 value=110只能说明是垂运  当value==110的时候  deCode是【B8-1、B8-3、B8-2、B8-4】中的时，为地下，否则为地上
    const azCostCode = [210, 220, 230, 240, 250];
    if (ObjectUtils.isNotEmpty(value)) {
      if (azCostCode.includes(value)) {
        // 安装
        return DePropertyTypeConstant.AZ_DE;
      }
      if (value == 120) {
        // 超高
        return DePropertyTypeConstant.CG_DE;
      }
      if (value == 110) {
        // 垂运
        const DS_CY = ['B8-1', 'B8-3', 'B8-2', 'B8-4'];
        if (DS_CY.includes(deCode)) {
          return DePropertyTypeConstant.DX_CY_DE;
        } else {
          return DePropertyTypeConstant.DS_CY_DE;
        }
      }
    }
    if (ObjectUtils.isNotEmpty(zjcsClassCode)) {
      if (zjcsClassCode == '0') {
        // 安文费
        return DePropertyTypeConstant.AWF_DE;
      } else {
        // 其他总价措施
        return DePropertyTypeConstant.ZJCS_DE;
      }
    }
    return DePropertyTypeConstant.NON_COST_DE;
  }

  /**
   * 计算基数
   * @param itemBillProjectDTOS
   * @param measureProjectDTOS
   * @return {number}
   */
  mathDeBaseCost(deList, unit, costDe) {
    ////以对应定额的【（∑定额综合合价）+规费合价+其他项目费合计（此部分仅记取一次，记取至【主取费文件】对应安文费定额下）作为计算基数，乘以相应费率
    let { feeBuild, feeFiles, constructId, spId, sequenceNbr } = unit;
    //计算基数
    let costBase = 0;
    let is12De = !PricingFileFindUtils.is22UnitById(constructId, spId, sequenceNbr);
    //获取计取基数
    if (!ObjectUtils.isEmpty(deList)) {
      for (const itemBillProjectDTO of deList) {
        let feeBuildList = feeBuild[itemBillProjectDTO.sequenceNbr];

        if (ObjectUtils.isNotEmpty(feeBuildList)) {
          // 取定额单价构成中的【工程造价】中的合价求和  如果是12定额还需要加上规费
          for (const up of feeBuildList) {
            if (up.type == UnitPriceConstant.GCZJ_TYPE) { // 定额的综合合价
              costBase = NumberUtil.add(costBase, up.allPrice);
            } else if (is12De && up.type == UnitPriceConstant.GF_TYPE) { // 定额规费
              costBase = NumberUtil.add(costBase, up.allPrice);
            }
          }
        }
      }
    }

    //获取到取费文件数据
    let unitFeeFile = null;
    //获取到取费文件数据
    unitFeeFile = feeFiles.find(k => k.feeFileCode === costDe.qfCode);

    //主取费文件
    let mainFeeFile = PricingFileFindUtils.getMainFeeFile(constructId, spId, sequenceNbr);

    //判断当前取费文件类型是否与主取费文件类型一致  如果一致将其他费用记取到该定额下
    if (costDe.qfName === mainFeeFile.feeFileName || costDe.costMajorName === mainFeeFile.feeFileName) {
      //获取所有的其他项目数据
      let otherSum = this.service.yuSuanProject.otherProjectService.getMarkSafaTotal({
        constructId: constructId,
        singleId: spId,
        unitId: sequenceNbr
      });
      costBase = NumberUtil.add(costBase, otherSum);
    }

    //获取 安文费费率
    let awfRate = 0;
    if (!ObjectUtils.isEmpty(unitFeeFile)) {
      awfRate = unitFeeFile.anwenRateBase;
    } else {
      awfRate = mainFeeFile.anwenRateBase;
    }
    return { 'costBase': costBase, 'awfRate': awfRate };
  }

  /**
   * //计算分部分项、措施项目的定额 人机合计
   * @param fbde
   * @param csde
   * @param unit
   * @return {number}
   */
  mathBaseDeRjHj(delist, unit, arg) {
    let { sequenceNbr, spId, constructId } = unit;
    //获取单位下所有人材机数据
    let rcjList = PricingFileFindUtils.getRcjList(constructId, spId, sequenceNbr);
    //获取计税方式 '1'?'一般计税':'简易计税'
    let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
    //获取计取基数
    let csfyCalculateBaseCode = '';
    if (ObjectUtils.isEmpty(unit.zjcsCostMathCache) || ObjectUtils.isEmpty(unit.zjcsCostMathCache.csfyCalculateBaseCode)) {
      // 多单位记取时  缓存只缓存在了主单位中，其他选择的单位没有缓存数据  所以需要从参数中获取
      if (ObjectUtils.isNotEmpty(arg)) {
        csfyCalculateBaseCode = arg.csfyCalculateBaseCode;
      } else {
        csfyCalculateBaseCode = CalculateBaseType.calculateBaseInitValue;
      }
    } else {
      csfyCalculateBaseCode = unit.zjcsCostMathCache.csfyCalculateBaseCode;
    }

    //获取基数定额的ID
    let deIdList = delist.map(k => k.sequenceNbr);

    //筛选基数定额的人机数据
    let baseRcjs = rcjList.filter(k => deIdList.includes(k.deId) && [1, 3].includes(k.kind));

    let rSum = 0;
    let jSum = 0;

    const is22De = PricingFileFindUtils.is22Unit(unit);

    //循环基数定额集合
    for (const de of delist) {
      //获取到单个基数定额人材机明细数据
      //筛选人明细
      let formula = 1;
      let rBase = 1;
      let jBase = 1;
      if (DePropertyTypeConstant.AZ_DE == de.isCostDe) {
        rBase = de.baseNum[1];
        jBase = de.baseNum[3];
      } else {
        // 12的超高费用定额有计算基数  应该算进去  22的超高的计算基数默认就是1 乘1没有影响  所以此处不做12或者22的判断
        if (de.isCostDe == DePropertyTypeConstant.CG_DE) {
          // 这里有个恶心的问题  计算基数这个值不知道为啥会产生两个字段 一个叫formula 一个叫 baseNum   超高的费用定额的计算基数是叫baseNum
          formula = de.baseNum;
          if (!is22De) {
            // 12的超高定额的baseNum不是一个数字  是个对象  里面的def字段存的计算基数
            formula = de.baseNum.def;
          }
        } else if ([DePropertyTypeConstant.FXTJ_CG, DePropertyTypeConstant.FXTJ_CZYS, DePropertyTypeConstant.FXTJ_ZXXJX, DePropertyTypeConstant.FXTJ_GCSDF].includes(de.isCostDe)) {
          formula = de.formula;
        }
      }


      let baseRList = baseRcjs.filter(k => k.kind === 1 && k.deId === de.sequenceNbr);
      if (!ObjectUtils.isEmpty(baseRList)) {
        const RSum = baseRList.reduce((total, item) =>
          total + NumberUtil.multiplyParams(CostUtils.getRJPriceSCJ(item, taxCalculationMethod, csfyCalculateBaseCode), item.unit == '%' ? NumberUtil.divide100(item.resQty) : item.resQty, de.quantity, formula, rBase), 0);
        rSum = NumberUtil.add(RSum, rSum);
      }
      //筛选机明细
      let baseJList = baseRcjs.filter(k => k.kind === 3 && k.deId === de.sequenceNbr);
      if (!ObjectUtils.isEmpty(baseJList)) {
        for (const k of baseJList) {
          //解析并且下沉
          if (k.levelMark != RcjLevelMarkConstant.NO_SINK && k.markSum == 1) {
            let { rDetail, cDetail, jDetail } = PricingFileFindUtils.getRcjDetailGroup(unit, k);
            //人
            if (!ObjectUtils.isEmpty(rDetail)) {
              const RSum = rDetail.reduce((total, item) => total + NumberUtil.multiplyParams(CostUtils.getRJPriceSCJ(item, taxCalculationMethod, csfyCalculateBaseCode), item.unit == '%' ? NumberUtil.divide100(item.resQty) : item.resQty, k.resQty, de.quantity, formula, rBase), 0);
              rSum = NumberUtil.add(NumberUtil.numberScale(RSum, 2), rSum);
            }
            //机
            if (!ObjectUtils.isEmpty(jDetail)) {
              const JSum = jDetail.reduce((total, item) => total + NumberUtil.multiplyParams(CostUtils.getRJPriceSCJ(item, taxCalculationMethod, csfyCalculateBaseCode), item.unit == '%' ? NumberUtil.divide100(item.resQty) : item.resQty, k.resQty, de.quantity, formula, jBase), 0);
              if (!ObjectUtils.isEmpty(jDetail)) {
                jSum = NumberUtil.add(NumberUtil.numberScale(JSum, 2), jSum);
              }
            }
          } else {
            let JSum = 0;
            const qtJxFlag = ConstantUtil.OTHER_JX.filter(item => k.materialCode.startsWith(item));
            if (ObjectUtils.isNotEmpty(qtJxFlag)) {
              // 说明是其他机械费
              // 其他机械费的算法和普通的机械费算法不一样  需要取出当前定额的【人材机中非其他机械费的机械费】的定额价/市场价 之和 作为其他机械费的计算基数
              const constructProjectRcjs = baseJList.filter(jItem => ObjectUtils.isEmpty(ConstantUtil.OTHER_JX.filter(item => jItem.materialCode.startsWith(item))));
              let qtjxBaseNum = 0;
              if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                for (const item of constructProjectRcjs) {
                  let price = is22De ? (taxCalculationMethod == ProjectLevelConstant.TAX_MODE_1 ? item.priceBaseJournal : item.priceBaseJournalTax) : item.marketPrice;
                  qtjxBaseNum = NumberUtil.add(NumberUtil.multiplyParams(price, item.resQty), qtjxBaseNum);
                }
              }
              JSum = NumberUtil.multiplyParams(CostUtils.getRJPriceSCJ(k, taxCalculationMethod, csfyCalculateBaseCode), k.unit == '%' ? NumberUtil.divide100(k.resQty) : k.resQty, de.quantity, qtjxBaseNum);
            } else {
              JSum = NumberUtil.multiplyParams(CostUtils.getRJPriceSCJ(k, taxCalculationMethod, csfyCalculateBaseCode), k.unit == '%' ? NumberUtil.divide100(k.resQty) : k.resQty, de.quantity, formula, jBase);
            }
            jSum = NumberUtil.add(JSum, jSum);
          }
        }
      }
    }
    return NumberUtil.add(rSum, jSum);
  }


  /**
   * //获取高台增加费定额
   * @param cs
   * @param increaseFeeHeight
   * @return {undefined}
   */
  async queryBaseDe(cslx, height, is22De) {
    let baseDeGtfMajorRelationEntity =
      await this.app.appDataSource.getRepository(is22De ? BaseDeGtfMajorRelation2022 : BaseDeGtfMajorRelation)
        .findOne({
          where: {
            addRateHeight: height
          }
        });
    let deCode = null;
    if (cslx === '仿古建筑工程') {
      deCode = baseDeGtfMajorRelationEntity.fgjzgc;
    }
    if (cslx === '绿化工程') {
      deCode = baseDeGtfMajorRelationEntity.lhgc;
    }
    if (cslx === '园林工程') {
      deCode = baseDeGtfMajorRelationEntity.ylgc;
    }
    if (cslx === '古建（明清）修缮工程') {
      deCode = baseDeGtfMajorRelationEntity.gjxsgc;
    }
    if (!ObjectUtils.isEmpty(deCode)) {
      let list = await this.app.appDataSource.getRepository(is22De ? BaseDe2022 : BaseDe).find({
        where: { deCode: deCode, isZj: 1 }
      });
      if (!ObjectUtils.isEmpty(list)) {
        return list[0];
      }
    }
    return null;
  }

  /**
   * todo 手动添加定额 已作废
   */
  async manuallyAddCostMath(arg) {
    if (true) {
      return;
    }
    let { constructId, singleId, unitId, de } = arg;
    if (ObjectUtils.isEmpty(de.deCode) && ObjectUtils.isEmpty(de.fxCode) && ObjectUtils.isEmpty(de.name) && ObjectUtils.isEmpty(de.deName)) {
      return;
    }

    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //单价措施定额
    let deListDjcs = PricingFileFindUtils.getDeByDjcs(constructId, singleId, unitId);
    //总价措施定额
    let deListZjcs = PricingFileFindUtils.getDeByZjcs(constructId, singleId, unitId);
    //分部分项定额
    let deListFbfx = PricingFileFindUtils.getDeByfbfx(constructId, singleId, unitId);

    //垂运记取
    let cyArg = await this.cyCostParams(arg, unit);
    if (!ObjectUtils.isEmpty(cyArg)) {
      //记取垂运定额
      await this.service.yuSuanProject.constructCostMathService.czysCostMath(cyArg);
    }
  }


  //垂运参数封装
  async cyCostParams(arg, unit) {
    let cache = unit.cyCostMathCache;
    if (ObjectUtils.isEmpty(cache)) {
      return null;
    }
    //参数
    let { constructId, singleId, unitId, de } = arg;
    let { itemBillProjects, measureProjectTables } = unit;

    //封装执行垂运记取
    let copyCache = ConvertUtil.deepCopy(cache);
    copyCache.isFlag = true;
    //处理添加基数定额情况
    //获取垂运基数定额范围
    let decorationDe = await this.getDecorationDe(constructId, singleId, unitId);
    //查询添加是否为基数定额
    let baseDe = decorationDe.find(k => k.sequenceNbr === de.standardId);
    if (!ObjectUtils.isEmpty(baseDe)) {
      let array = [];
      array.push(itemBillProjects.getAllNodes());
      array.push(measureProjectTables.getAllNodes());
      //获取项目中跟缓存一致的费用定额
      let costDeList = array.filter(k => k.kind === BranchProjectLevelConstant.de && (k.isCostDe === DePropertyTypeConstant.DS_CY_DE || k.isCostDe === DePropertyTypeConstant.DX_CY_DE)
        && k.isStandard === DePropertyTypeConstant.STANDARD && k.bdCode === cache.up);
      if (ObjectUtils.isEmpty(costDeList)) {
        return null;
      }
      //封装执行垂运记取
      let unitCyBaseDeList = await this.conditionDeList(arg);
      unitCyBaseDeList = unitCyBaseDeList.filter(k => k.kind === BranchProjectLevelConstant.de);
      unitCyBaseDeList = unitCyBaseDeList.filter(k => !copyCache.data.map(k => k.sequenceNbr).includes(k.sequenceNbr));
      copyCache.data.push(...unitCyBaseDeList);
      copyCache.down = null;
      return copyCache;
    }

    //查询垂运费用定额范围
    let costDeList = await this.app.appDataSource.getRepository(BaseDe).find({
      where: { value: 110 }
    });
    //查询添加的定额是否在费用定额里面
    let costDe = costDeList.find(k => k.sequenceNbr === de.standardId);
    if (!ObjectUtils.isEmpty(costDe)) {
      de.isCostDe = DePropertyTypeConstant.CY_DE;
      //let cacheDeList = copyCache.data.filter(k =>k.kind === BranchProjectLevelConstant.de);
      //获取当前单位中的基数定额集合
      let unitCyBaseDeList = await this.conditionDeList(arg);
      unitCyBaseDeList = unitCyBaseDeList.filter(k => k.kind === BranchProjectLevelConstant.de);
      //添加的是以上的费用定额
      let flag = false;
      if (de.bdCode === copyCache.up) {
        unitCyBaseDeList = unitCyBaseDeList.filter(k => !copyCache.data.map(k => k.sequenceNbr).includes(k.sequenceNbr));
        copyCache.data.push(...unitCyBaseDeList);
        copyCache.down = null;
        flag = true;
      }
      //添加的是以下的费用定额
      if (de.bdCode === copyCache.down) {
        copyCache.up = null;
        flag = true;
      }
      if (flag) {
        return copyCache;
      }
    }
  }

  /**
   * 删除指定的费用定额
   */
  async clearCostDe(unit, costTypeArray, changeDeIds) {
    let { measureProjectTables, itemBillProjects } = unit;
    let csxmRemoveStrategy = new RemoveStrategy({
      constructId: unit.constructId,
      singleId: unit.spId,
      unitId: unit.sequenceNbr,
      pageType: 'csxm'
    });
    await PricingFileFindUtils.execClearCostDe(measureProjectTables, costTypeArray, unit, changeDeIds, csxmRemoveStrategy);
    let fbfxRemoveStrategy = new RemoveStrategy({
      constructId: unit.constructId,
      singleId: unit.spId,
      unitId: unit.sequenceNbr,
      pageType: 'fbfx'
    });
    await PricingFileFindUtils.execClearCostDe(itemBillProjects, costTypeArray, unit, changeDeIds, fbfxRemoveStrategy);
    //重新计算所有金额  做定额和清单的汇总
    await this.service.yuSuanProject.unitPriceService.reCacaulateChange(unit.constructId, unit.spId, unit.sequenceNbr, measureProjectTables, changeDeIds);
    await this.service.yuSuanProject.unitPriceService.reCacaulateChange(unit.constructId, unit.spId, unit.sequenceNbr, itemBillProjects, changeDeIds);
  };
}

ConstructCostMathService.toString = () => '[class ConstructCostMathService]';
module.exports = ConstructCostMathService;
