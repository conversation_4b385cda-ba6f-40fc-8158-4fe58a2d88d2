const {ConvertUtil} = require("../../utils/ConvertUtils");

const Kind3RuleHandler = require("./Kind3RuleHandler");
const { NumberUtil } = require("../../../../electron/utils/NumberUtil");
const { Snowflake } = require("../../utils/Snowflake");
const {ConversionInfoUtil} = require("../util/ConversionInfoUtil");

class Kind3TypeBRuleHandler extends Kind3RuleHandler {
    constructor(strategyCtx, rule)  {
        super(strategyCtx, rule);
        this.formatMath = strategyCtx.conversionService.mathFormat(rule.math, rule);
    }

    async addDeByRule(){
        const {
            constructId,
            unitId,
            singleId,
            de,
            unitProject,
            deBeLong,
        } = this.ctx;
        let rule = this.rule;

        let ruleMath = this.formatMath;
        let newMath = ruleMath;
        let addDeNumber = 0;
        if("+-*/".includes(ruleMath.charAt(0))){
            addDeNumber = NumberUtil.numberScale(eval(ruleMath.substring(1)), 6);
            newMath = ruleMath.charAt(0) + addDeNumber;
        }else{
            addDeNumber = NumberUtil.numberScale(eval(ruleMath), 6);
            newMath = "" + addDeNumber;
        }

        // 如果新增定额数量小于1，则直接退出
        // if(addDeNumber < 1){
        //     return;
        // }

        const newDe = await this.getRelationDe(rule.libraryCode, rule.relationDeId);

        let baseNewLine = {
            sequenceNbr: Snowflake.nextId(),
            name: newDe.deName,
            kind: "04",
            parentId: de.parentId,
            unit: newDe.unit,
            libraryCode: newDe.libraryCode,
            bdCode: newDe.deCode,
            rcjFlag: 0
        }

        const { data: rawLine } = await (deBeLong == "fbfx"
            ? this.ctx.service.yuSuanProject.itemBillProjectOptionService.fillDataFromIndexPage(
                constructId,
                singleId,
                unitId,
                de,
                baseNewLine.kind,
                newDe.sequenceNbr,
                baseNewLine.unit,
                unitProject.itemBillProjects.root.sequenceNbr,
                baseNewLine.rcjFlag,
                null,
                null,
                baseNewLine.libraryCode,
                null
                )
            : this.ctx.service.yuSuanProject.stepItemCostService.fillDataFromIndexPage(
                constructId,
                singleId,
                unitId,
                de,
                baseNewLine.kind,
                newDe.sequenceNbr,
                baseNewLine.unit,
                baseNewLine.rcjFlag,
                null,
                null,
                null,
                baseNewLine.libraryCode
        ));

        let conversionAddByRules = [];
        let inputIndex = -99;

        let addByCurRule = {
            sequenceNbr: Snowflake.nextId(),
            type: "",
            kind: "0",
            math: newMath,
            relation: newMath,
            defaultValue: 1,
            selectedRule: addDeNumber,
            index: inputIndex++,
            libraryCode: rule.libraryCode,
            ruleInfo: newMath,
            selected: true
        };
        conversionAddByRules.push(addByCurRule);

        let standardConversion = this.ctx.de.conversionInfo.find(info=>info.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE);
        let infos = standardConversion.children;

        let curRuleIndex = infos.findIndex(v => v.sequenceNbr == rule.sequenceNbr)

        for(let i = curRuleIndex + 1; i < infos.length; i++){
            let ruleTmp = infos[i];
            // kind=3,type=b的规则不加入新增定额的规则范围内
            if(ruleTmp.kind == "3" && ruleTmp.type == 'b'){
                continue;
            }

            let newRule = ConvertUtil.deepCopy(ruleTmp);
            newRule.sequenceNbr = Snowflake.nextId();
            newRule.index = inputIndex++;
            conversionAddByRules.push(newRule)
        }


        const { line: addedDe, belong: _ } = this.ctx.service.yuSuanProject.baseBranchProjectOptionService.findLineOnlyById(rawLine.sequenceNbr) ;
        addedDe.conversionAddByRule = conversionAddByRules;

        addedDe.quantityExpression = "HSGCL";
        //获取父级定额的工程量
        addedDe.quantityExpressionNbr = de.quantity
        addedDe.quantity = de.quantity;

        let ruleDeIdObj = {
            deId: addedDe.sequenceNbr,
            ruleId: rule.sequenceNbr
        }
        if(de.addByRuleDeIds) {
            de.addByRuleDeIds.push(ruleDeIdObj);
        }else{
            de.addByRuleDeIds = [ruleDeIdObj];
        }

        this.ctx.deUpDateObj.addedDes.push(addedDe);
    }

    /**
     * 逐条执行换算规则
     */
    async execute(){
        await this.prepare();
        await this.addDeByRule();
        this.after();
    }

    analysisRule(){
        // 什么都不做
    }

    deCodeUpdateInfo() {
        return {redStr: `[Z ${this.rule.relationDeCode}]`, blackStr: null}
    }

    deTypeUpdateInfo(rule){
        // 什么都不做
    }
}
module.exports = Kind3TypeBRuleHandler;
