'use strict';

const {Service} = require('../../../core');
const {BaseRuleDetails} = require("../model/BaseRuleDetails");
const {SqlUtils} = require("../utils/SqlUtils");

/**
 * 规则明细表
 * @class
 */
class BaseRuleDetailsService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseRuleDetailsDao = this.app.appDataSource.manager.getRepository(BaseRuleDetails);

    /**
     * 查规则明细
     * @param relationGroupCode 关联规则组编码
     * @return {Promise<BaseRuleDetails[]|Error>}
     */
    async listByRelationGroupCode(relationGroupCode) {
        if (!relationGroupCode) {
            throw new Error("必传参数relationGroupCode为空");
        }

        // 根据国标定额id查关联关系（ps: 若后续存在获取不到国标定额id的情况，可通过定额编码、名称、定额册code来查）
        let sql = "select * from base_rule_details where relation_group_code = ?";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(relationGroupCode);
        let convertRes = SqlUtils.convertToModel(sqlRes);

        return convertRes;
    }
}

BaseRuleDetailsService.toString = () => '[class BaseRuleDetailsService]';
module.exports = BaseRuleDetailsService;
