const XXXToYYYMathHandler = require("./XXXToYYYMathHandler");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. HXXXX YYYY 对应材料编码 替换为 YYYY 消耗量不变
 */
class Hxxx$yyyMathHandler extends XXXToYYYMathHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 3;
        let mathSubArr = mathItem.math.substring(1).split(/\s+/);
        mathItem.fromRCJCode = mathSubArr[0];
        mathItem.fromRCJLibraryCode = this.rule.libraryCode;
        mathItem.toRCJCode = mathSubArr[1];
        mathItem.toRCJLibraryCode = this.rule.libraryCode;
    }

}

module.exports = Hxxx$yyyMathHandler;