{"name": "Pricing", "version": "1.0.40", "description": "云算房-计价软件", "main": "main.js", "scripts": {"start": "npm run build && chcp 65001 && electron .", "dev": "chcp 65001 && electron . --env=local", "dev-tsc": "tsc &&  electron . --env=local", "reload": "nodemon --config ./electron/config/nodemon.json", "test": "set DEBUG=* && electron . --env=local", "build-w": "npm run build && electron-builder -w=nsis --x64", "build-w-32": "electron-builder -w=nsis --ia32", "build-w-64": "electron-builder -w=nsis --x64", "build-w-arm64": "electron-builder -w=nsis --arm64", "build-wz": "electron-builder -w=7z --x64", "build-wz-32": "electron-builder -w=7z --ia32", "build-wz-64": "electron-builder -w=7z --x64", "build-wz-arm64": "electron-builder -w=7z --arm64", "build-m": "electron-builder -m", "build-m-arm64": "electron-builder -m --arm64", "rd": "node core/bin/tools.js rd --dist_dir=./frontendUyarni/dist", "encrypt": "node core/bin/tools.js encrypt", "rebuild": "electron-rebuild", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "typeorm": "typeorm-ts-node-commonjs", "build": "tsc"}, "build": {"productName": "云算房计价软件", "appId": "com.xili.jijia.merge", "copyright": "云算房计价预算", "directories": {"output": "out"}, "publish": [{"provider": "generic", "url": "https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/test/"}], "asar": true, "files": ["**/*", "!frontendUi/", "!frontend/", "!run/", "!logs/", "!data/"], "extraResources": {"from": "./build/extraResources/", "to": "extraResources"}, "electronDownload": {"mirror": "https://npmmirror.com/mirrors/electron/"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "./build/icons/256.ico", "uninstallerIcon": "./build/icons/256.ico", "installerHeaderIcon": "./build/icons/256.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "云算房计价"}, "fileAssociations": [{"ext": "YSF", "name": "", "role": "Editor", "icon": "./build/icons/ysf-64.png"}, {"ext": "YSFZ", "name": "", "role": "Editor", "icon": "./build/icons/ysfz-64.png"}, {"ext": "YSFD", "name": "", "role": "Editor", "icon": "./build/icons/ysfd-64.png"}, {"ext": "YSFG", "name": "", "role": "Editor", "icon": "./build/icons/ysfg-64.png"}, {"ext": "YSH", "name": "", "role": "Editor", "icon": "./build/icons/ysh-64.png"}, {"ext": "YJS", "name": "", "role": "Editor", "icon": "./build/icons/yjs-64.png"}, {"ext": "ygs", "name": "", "role": "Editor", "icon": "./build/icons/ygs-64.ico"}], "mac": {"icon": "./build/icons/icon.icns", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": ["dmg", "zip"]}, "win": {"icon": "./build/icons/256.ico", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "requestedExecutionLevel": "requireAdministrator", "target": [{"target": "nsis"}]}, "linux": {"icon": "./build/icons/icon.icns", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": ["deb"], "category": "Utility"}}, "keywords": ["Electron"], "author": "河北宏盛建通信息技术有限公司", "license": "Apache", "devDependencies": {"@electron/rebuild": "^3.6.0", "@types/better-sqlite3": "^7.6.9", "@types/node": "^16.11.10", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "debug": "^4.3.3", "decimal.js": "^10.4.3", "electron": "^21.4.4", "electron-builder": "^23.6.0", "nodemon": "^2.0.16", "sqlite3": "^5.1.6", "ts-node": "10.7.0", "typescript": "^4.5.2"}, "dependencies": {"@jcstdio/jc-utils": "^0.0.19", "@types/node": "^16.11.10", "@valuation/rules-engine": "^1.0.15", "@vue/reactivity": "^3.3.8", "adm-zip": "^0.5.10", "agentkeepalive": "^4.2.0", "ali-oss": "^6.21.0", "archiver": "^7.0.1", "axios": "^1.4.0", "better-sqlite3": "^8.4.0", "bytenode": "^1.3.6", "chain-lexer": "^1.10.0", "co": "^4.6.0", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "dayjs": "^1.11.7", "debug": "^4.3.3", "depd": "^2.0.0", "diff": "^5.1.0", "egg-errors": "^2.3.0", "egg-logger": "^2.7.1", "ejs": "^3.1.10", "electron-updater": "^5.3.0", "eventemitter3": "^5.0.1", "excel-formula": "^1.5.0", "exceljs": "4.3.0", "express": "^4.19.2", "extend2": "^1.0.1", "fs-extra": "^10.0.0", "get-port": "^5.1.1", "getmac": "^5.21.0", "globby": "^10.0.0", "handlebars": "^4.7.8", "humanize-ms": "^1.2.1", "hyperformula": "^2.7.1", "iconv-lite": "^0.6.3", "internet-available": "^1.0.0", "is-type-of": "^1.2.1", "javascript-obfuscator": "^4.0.0", "jschardet": "^1.0.0", "json-schema": "^0.4.0", "jszip": "^3.10.1", "koa": "^2.13.4", "koa-body": "^5.0.0", "koa-convert": "^2.0.0", "koa-static": "^5.0.0", "koa2-cors": "^2.0.6", "lodash": "^4.17.21", "mathjs": "^12.3.0", "mkdirp": "^2.1.6", "moment": "^2.29.4", "morgan": "^1.10.0", "mysql": "^2.18.1", "nanoid": "^4.0.2", "node-schedule": "^2.1.1", "node-unrar-js": "^2.0.0", "object-sizeof": "^2.6.4", "officegen": "^0.6.5", "prebuild-install": "^7.1.1", "reflect-metadata": "^0.1.13", "sax": "^1.2.4", "semver": "^7.3.8", "serialize-javascript": "^6.0.1", "socket.io": "^4.4.1", "socket.io-client": "^4.4.1", "table-parser": "^1.0.1", "typeorm": "^0.3.16", "urllib": "^2.38.0", "uuid": "^9.0.0", "winston": "^3.14.2", "winston-daily-rotate-file": "^5.0.0", "xe-utils": "3.5.14", "xlsx": "^0.17.4", "xml-flow": "^1.0.4", "xml2js": "^0.6.0", "xmlbuilder2": "^3.1.1", "xmldoc": "^1.3.0"}}