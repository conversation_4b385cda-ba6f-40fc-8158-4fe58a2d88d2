'use strict';

const {BaseAnwenRate2022} = require("../model/BaseAnwenRate");
const {Service, Log} = require('../../../core');
const {BaseArea} = require("../model/BaseArea");
const {ConstructProject} = require("../model/ConstructProject");
const {BaseListDeStandard} = require("../model/BaseListDeStandard");
const {BaseAnwenRate} = require("../model/BaseAnwenRate");
const ConstantUtil = require("../enum/ConstantUtil");
const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");

/**
 * 示例服务
 * @class
 */
class BaseAnwenRateService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 查询安文费
     */
    //taxCalculationMethod 计税方式
    async queryByLibraryCode(qfName,libraryCode,engineeringLocation,adjacentRoadsNum,floorSpace,municipalEngineeringCost,unitIs2022,taxCalculationMethod) {
        let result = await this.app.appDataSource.getRepository(unitIs2022?BaseAnwenRate2022:BaseAnwenRate).find({
            where:{
                libraryCode:libraryCode,
                projectLocation:engineeringLocation,//工程所在地
                roadSurfaceNum:adjacentRoadsNum,//临路面数
                floorSpace:floorSpace
            }
        });
        //针对市政工程的查询
        let feeFilesBySHIZHENG = [];
        if (unitIs2022) {
            feeFilesBySHIZHENG = await this.service.yuSuanProject.baseFeeFileRelationService.getFeeFilesByLibraryCode("2022-SZGC-DEK",unitIs2022);
        }else {
            feeFilesBySHIZHENG = await this.service.yuSuanProject.baseFeeFileRelationService.getFeeFilesByLibraryCode("2012-SZGC-DEK",unitIs2022);
        }
        if (feeFilesBySHIZHENG.map(item => item.qfName).includes(qfName)) {
            let unitProject = result.find((item ) => item.municipalConstructionCost === municipalEngineeringCost);
            if (!ObjectUtil.isEmpty(unitProject)&& unitIs2022){
                //简易计税
                if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code){
                    //安文费
                    unitProject.anwenRate = parseFloat(unitProject.anwenRateJyjs);
                }else if (taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code){
                    //一般计税
                    unitProject.anwenRate = parseFloat(unitProject.anwenRateYbjs);
                }
            }
            return  unitProject;
        }
        //2022定额 安文费 费率受计税方式影响
        if (!ObjectUtil.isEmpty(result) ){
            let baseAnwenRate = null;
            if (ObjectUtil.isEmpty(municipalEngineeringCost)){
                baseAnwenRate = result[0];
            }else {
                baseAnwenRate = result.find(k =>k.municipalConstructionCost ==municipalEngineeringCost);
            }

            if (unitIs2022){
                //简易计税
                if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code){
                    //安文费
                    baseAnwenRate.anwenRate = parseFloat(baseAnwenRate.anwenRateJyjs);
                }else if (taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code){
                    //一般计税
                    baseAnwenRate.anwenRate = parseFloat(baseAnwenRate.anwenRateYbjs);
                }
            }
            return baseAnwenRate;
        }
        return null;
    }


}

BaseAnwenRateService.toString = () => '[class BaseAnwenRateService]';
module.exports = BaseAnwenRateService;
